"""服务注册和管理系统

提供服务注册、发现、配置和健康检查功能。
"""

from typing import Dict, Any, List, Optional, Type, Callable
import asyncio
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

from src.services.base import (
    BaseService, LLMService, ImageGenerationService, 
    VideoGenerationService, VoiceGenerationService,
    ServiceConfig, ServiceStatus
)
from src.services.llm import OpenAILLMService, ZhipuLLMService
from src.services.image import PollinationsImageService, OpenAIImageService
from src.services.voice import EdgeTTSService, OpenAITTSService
from src.services.video import CogVideoXService, LocalVideoComposer
from src.core.events import get_event_bus, Event
from src.utils.logger import get_logger


class ServiceType(Enum):
    """服务类型枚举"""
    LLM = "llm"
    IMAGE = "image"
    VIDEO = "video"
    VOICE = "voice"


@dataclass
class ServiceRegistration:
    """服务注册信息"""
    service_type: ServiceType
    service_class: Type[BaseService]
    name: str
    description: str
    config_schema: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    priority: int = 0  # 优先级，数字越大优先级越高


class ServiceRegistry:
    """服务注册表"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._registrations: Dict[str, ServiceRegistration] = {}
        self._instances: Dict[str, BaseService] = {}
        self._configs: Dict[str, ServiceConfig] = {}
        self._health_check_interval = 60  # 健康检查间隔（秒）
        self._health_check_task: Optional[asyncio.Task] = None
        self.event_bus = get_event_bus()
        
        # 注册默认服务
        self._register_default_services()
    
    def _register_default_services(self) -> None:
        """注册默认服务"""
        # LLM服务
        self.register_service(ServiceRegistration(
            service_type=ServiceType.LLM,
            service_class=OpenAILLMService,
            name="openai_llm",
            description="OpenAI GPT语言模型服务",
            config_schema={
                "api_key": {"type": "string", "required": True, "description": "OpenAI API密钥"},
                "base_url": {"type": "string", "required": False, "description": "API基础URL"},
                "model": {"type": "string", "required": False, "default": "gpt-4", "description": "模型名称"},
                "timeout": {"type": "integer", "required": False, "default": 60, "description": "请求超时时间"}
            }
        ))
        
        self.register_service(ServiceRegistration(
            service_type=ServiceType.LLM,
            service_class=ZhipuLLMService,
            name="zhipu_llm",
            description="智谱AI语言模型服务",
            config_schema={
                "api_key": {"type": "string", "required": True, "description": "智谱AI API密钥"},
                "base_url": {"type": "string", "required": False, "description": "API基础URL"},
                "model": {"type": "string", "required": False, "default": "glm-4", "description": "模型名称"}
            }
        ))
        
        # 图像服务
        self.register_service(ServiceRegistration(
            service_type=ServiceType.IMAGE,
            service_class=PollinationsImageService,
            name="pollinations_image",
            description="Pollinations免费图像生成服务",
            config_schema={
                "base_url": {"type": "string", "required": False, "description": "API基础URL"}
            },
            priority=1  # 免费服务优先级较高
        ))
        
        self.register_service(ServiceRegistration(
            service_type=ServiceType.IMAGE,
            service_class=OpenAIImageService,
            name="openai_image",
            description="OpenAI DALL-E图像生成服务",
            config_schema={
                "api_key": {"type": "string", "required": True, "description": "OpenAI API密钥"},
                "base_url": {"type": "string", "required": False, "description": "API基础URL"},
                "model": {"type": "string", "required": False, "default": "dall-e-3", "description": "模型名称"}
            }
        ))
        
        # 语音服务
        self.register_service(ServiceRegistration(
            service_type=ServiceType.VOICE,
            service_class=EdgeTTSService,
            name="edge_tts",
            description="Edge TTS免费语音合成服务",
            config_schema={},
            dependencies=["edge-tts"],
            priority=1  # 免费服务优先级较高
        ))
        
        self.register_service(ServiceRegistration(
            service_type=ServiceType.VOICE,
            service_class=OpenAITTSService,
            name="openai_tts",
            description="OpenAI TTS语音合成服务",
            config_schema={
                "api_key": {"type": "string", "required": True, "description": "OpenAI API密钥"},
                "base_url": {"type": "string", "required": False, "description": "API基础URL"},
                "model": {"type": "string", "required": False, "default": "tts-1", "description": "模型名称"}
            }
        ))
        
        # 视频服务
        self.register_service(ServiceRegistration(
            service_type=ServiceType.VIDEO,
            service_class=CogVideoXService,
            name="cogvideox",
            description="CogVideoX视频生成服务",
            config_schema={
                "api_key": {"type": "string", "required": True, "description": "CogVideoX API密钥"},
                "base_url": {"type": "string", "required": False, "description": "API基础URL"},
                "model": {"type": "string", "required": False, "default": "cogvideox-5b", "description": "模型名称"}
            }
        ))
        
        self.register_service(ServiceRegistration(
            service_type=ServiceType.VIDEO,
            service_class=LocalVideoComposer,
            name="local_composer",
            description="本地视频合成服务",
            config_schema={},
            dependencies=["ffmpeg"],
            priority=1  # 本地服务优先级较高
        ))
    
    def register_service(self, registration: ServiceRegistration) -> None:
        """注册服务"""
        self._registrations[registration.name] = registration
        self.logger.info(f"Registered service: {registration.name} ({registration.service_type.value})")
    
    def get_available_services(self, service_type: Optional[ServiceType] = None) -> List[ServiceRegistration]:
        """获取可用服务列表"""
        services = list(self._registrations.values())
        
        if service_type:
            services = [s for s in services if s.service_type == service_type]
        
        # 按优先级排序
        services.sort(key=lambda x: x.priority, reverse=True)
        return services
    
    def get_service_registration(self, name: str) -> Optional[ServiceRegistration]:
        """获取服务注册信息"""
        return self._registrations.get(name)
    
    async def create_service(self, name: str, config: Dict[str, Any]) -> Optional[BaseService]:
        """创建服务实例"""
        registration = self.get_service_registration(name)
        if not registration:
            self.logger.error(f"Service not found: {name}")
            return None
        
        try:
            # 验证配置
            if not self._validate_config(config, registration.config_schema):
                self.logger.error(f"Invalid config for service: {name}")
                return None
            
            # 创建服务配置
            service_config = ServiceConfig(
                api_key=config.get("api_key"),
                base_url=config.get("base_url"),
                timeout=config.get("timeout", 60),
                custom_settings=config
            )
            
            # 创建服务实例
            service = registration.service_class(service_config)
            
            # 存储配置和实例
            self._configs[name] = service_config
            self._instances[name] = service
            
            self.logger.info(f"Created service instance: {name}")
            return service
            
        except Exception as e:
            self.logger.error(f"Failed to create service {name}: {e}")
            return None
    
    def get_service(self, name: str) -> Optional[BaseService]:
        """获取服务实例"""
        return self._instances.get(name)
    
    def get_services_by_type(self, service_type: ServiceType) -> List[BaseService]:
        """按类型获取服务实例"""
        services = []
        for name, registration in self._registrations.items():
            if registration.service_type == service_type and name in self._instances:
                services.append(self._instances[name])
        return services
    
    async def initialize_service(self, name: str) -> bool:
        """初始化服务"""
        service = self.get_service(name)
        if not service:
            return False
        
        try:
            success = await service.health_check()
            if success:
                self.logger.info(f"Service initialized successfully: {name}")
                await self.event_bus.publish(Event(
                    name="service.initialized",
                    data={"service_name": name, "service_type": service.__class__.__name__}
                ))
            else:
                self.logger.warning(f"Service health check failed: {name}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to initialize service {name}: {e}")
            return False
    
    async def start_health_monitoring(self) -> None:
        """开始健康监控"""
        if self._health_check_task and not self._health_check_task.done():
            return
        
        self._health_check_task = asyncio.create_task(self._health_check_loop())
        self.logger.info("Started health monitoring")
    
    async def stop_health_monitoring(self) -> None:
        """停止健康监控"""
        if self._health_check_task and not self._health_check_task.done():
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Stopped health monitoring")
    
    async def _health_check_loop(self) -> None:
        """健康检查循环"""
        while True:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self._health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Health check error: {e}")
                await asyncio.sleep(self._health_check_interval)
    
    async def _perform_health_checks(self) -> None:
        """执行健康检查"""
        for name, service in self._instances.items():
            try:
                is_healthy = await service.health_check()
                
                if not is_healthy and service.status == ServiceStatus.AVAILABLE:
                    # 服务状态从可用变为不可用
                    await self.event_bus.publish(Event(
                        name="service.unhealthy",
                        data={"service_name": name, "status": service.status.value}
                    ))
                elif is_healthy and service.status != ServiceStatus.AVAILABLE:
                    # 服务状态恢复
                    await self.event_bus.publish(Event(
                        name="service.recovered",
                        data={"service_name": name, "status": service.status.value}
                    ))
                
            except Exception as e:
                self.logger.error(f"Health check failed for {name}: {e}")
    
    def _validate_config(self, config: Dict[str, Any], schema: Dict[str, Any]) -> bool:
        """验证配置"""
        for field_name, field_schema in schema.items():
            if field_schema.get("required", False) and field_name not in config:
                self.logger.error(f"Required field missing: {field_name}")
                return False
            
            if field_name in config:
                value = config[field_name]
                expected_type = field_schema.get("type")
                
                if expected_type == "string" and not isinstance(value, str):
                    self.logger.error(f"Field {field_name} must be string")
                    return False
                elif expected_type == "integer" and not isinstance(value, int):
                    self.logger.error(f"Field {field_name} must be integer")
                    return False
        
        return True
    
    def get_service_status_summary(self) -> Dict[str, Any]:
        """获取服务状态摘要"""
        summary = {
            "total_services": len(self._instances),
            "available_services": 0,
            "unavailable_services": 0,
            "error_services": 0,
            "services": {}
        }
        
        for name, service in self._instances.items():
            status = service.status.value
            summary["services"][name] = {
                "status": status,
                "type": self._registrations[name].service_type.value,
                "last_check": datetime.utcnow().isoformat()
            }
            
            if service.status == ServiceStatus.AVAILABLE:
                summary["available_services"] += 1
            elif service.status == ServiceStatus.UNAVAILABLE:
                summary["unavailable_services"] += 1
            else:
                summary["error_services"] += 1
        
        return summary


# 全局服务注册表实例
_service_registry: Optional[ServiceRegistry] = None


def get_service_registry() -> ServiceRegistry:
    """获取全局服务注册表实例"""
    global _service_registry
    if _service_registry is None:
        _service_registry = ServiceRegistry()
    return _service_registry
