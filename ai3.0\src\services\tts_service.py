# -*- coding: utf-8 -*-
"""
语音合成服务实现
集成Edge TTS、SiliconFlow和其他TTS服务
"""

import asyncio
import json
import logging
import os
import tempfile
import time
from typing import Dict, Any, List, Optional, Union
import aiohttp
import aiofiles
from dataclasses import dataclass
from pathlib import Path

from ..core.mcp_service_manager import (
    MCPServiceInterface, ServiceRequest, ServiceResponse, ServiceType, ServiceStatus
)

logger = logging.getLogger(__name__)

@dataclass
class TTSRequest:
    """TTS请求数据结构"""
    text: str
    voice: str = 'zh-CN-XiaoxiaoNeural'  # 默认声音
    language: str = 'zh-CN'
    speed: float = 1.0  # 语速 0.5-2.0
    pitch: float = 0.0  # 音调 -50到50
    volume: float = 1.0  # 音量 0.0-1.0
    output_format: str = 'mp3'  # 输出格式
    output_path: Optional[str] = None  # 输出路径

@dataclass
class TTSResponse:
    """TTS响应数据结构"""
    audio_path: str
    duration: float = 0.0
    file_size: int = 0
    voice_used: str = ""
    service_used: str = ""
    success: bool = True
    error_message: str = ""

class BaseTTSClient(MCPServiceInterface):
    """TTS客户端基类"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.session = None
        self.supported_voices = self._get_supported_voices()
        self.supported_languages = self._get_supported_languages()
    
    def _get_supported_voices(self) -> Dict[str, Dict[str, Any]]:
        """获取支持的声音列表（子类实现）"""
        return {}
    
    def _get_supported_languages(self) -> List[str]:
        """获取支持的语言列表（子类实现）"""
        return ['zh-CN', 'en-US']
    
    async def initialize(self) -> bool:
        """初始化TTS客户端"""
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=60)
            )
            
            health_ok = await self.health_check()
            if health_ok:
                logger.info(f"{self.name}: TTS服务初始化成功")
                return True
            else:
                logger.warning(f"{self.name}: TTS服务健康检查失败")
                return False
                
        except Exception as e:
            logger.error(f"{self.name}: TTS服务初始化失败 - {e}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        if self.session:
            await self.session.close()
    
    async def process(self, request: ServiceRequest) -> ServiceResponse:
        """处理TTS请求"""
        start_time = time.time()
        
        try:
            action = request.parameters.get('action', 'synthesize')
            
            if action == 'synthesize':
                tts_request = TTSRequest(**request.parameters.get('tts_request', {}))
                result = await self.synthesize(tts_request)
                
                return ServiceResponse(
                    success=True,
                    data=result,
                    execution_time=time.time() - start_time,
                    service_name=self.name
                )
            
            elif action == 'get_voices':
                language = request.parameters.get('language', 'zh-CN')
                result = await self.get_voices(language)
                
                return ServiceResponse(
                    success=True,
                    data=result,
                    execution_time=time.time() - start_time,
                    service_name=self.name
                )
            
            elif action == 'batch_synthesize':
                texts = request.parameters.get('texts', [])
                voice = request.parameters.get('voice', 'zh-CN-XiaoxiaoNeural')
                output_dir = request.parameters.get('output_dir', tempfile.gettempdir())
                result = await self.batch_synthesize(texts, voice, output_dir)
                
                return ServiceResponse(
                    success=True,
                    data=result,
                    execution_time=time.time() - start_time,
                    service_name=self.name
                )
            
            else:
                return ServiceResponse(
                    success=False,
                    error=f"不支持的操作: {action}",
                    service_name=self.name
                )
                
        except Exception as e:
            return ServiceResponse(
                success=False,
                error=str(e),
                execution_time=time.time() - start_time,
                service_name=self.name
            )
    
    async def synthesize(self, request: TTSRequest) -> TTSResponse:
        """合成语音（子类实现）"""
        raise NotImplementedError
    
    async def get_voices(self, language: str = 'zh-CN') -> List[Dict[str, Any]]:
        """获取可用声音列表（子类实现）"""
        raise NotImplementedError
    
    async def batch_synthesize(self, texts: List[str], voice: str, output_dir: str) -> List[TTSResponse]:
        """批量合成语音"""
        results = []
        for i, text in enumerate(texts):
            output_path = os.path.join(output_dir, f"audio_{i+1}.mp3")
            request = TTSRequest(
                text=text,
                voice=voice,
                output_path=output_path
            )
            result = await self.synthesize(request)
            results.append(result)
            # 添加延迟避免API限制
            await asyncio.sleep(0.5)
        return results

class EdgeTTSClient(BaseTTSClient):
    """Edge TTS客户端"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.edge_tts = None
        self._import_edge_tts()
    
    def _import_edge_tts(self):
        """动态导入edge-tts"""
        try:
            import edge_tts
            self.edge_tts = edge_tts
        except ImportError:
            logger.warning("edge-tts未安装，请运行: pip install edge-tts")
            self.edge_tts = None
    
    def _get_supported_voices(self) -> Dict[str, Dict[str, Any]]:
        """Edge TTS支持的声音"""
        return {
            # 中文声音
            'zh-CN-XiaoxiaoNeural': {
                'name': '晓晓',
                'gender': 'Female',
                'language': 'zh-CN',
                'description': '温柔女声'
            },
            'zh-CN-YunxiNeural': {
                'name': '云希',
                'gender': 'Male',
                'language': 'zh-CN',
                'description': '成熟男声'
            },
            'zh-CN-YunyangNeural': {
                'name': '云扬',
                'gender': 'Male',
                'language': 'zh-CN',
                'description': '青年男声'
            },
            'zh-CN-XiaoyiNeural': {
                'name': '晓伊',
                'gender': 'Female',
                'language': 'zh-CN',
                'description': '甜美女声'
            },
            'zh-CN-YunjianNeural': {
                'name': '云健',
                'gender': 'Male',
                'language': 'zh-CN',
                'description': '磁性男声'
            },
            # 英文声音
            'en-US-AriaNeural': {
                'name': 'Aria',
                'gender': 'Female',
                'language': 'en-US',
                'description': 'Natural female voice'
            },
            'en-US-DavisNeural': {
                'name': 'Davis',
                'gender': 'Male',
                'language': 'en-US',
                'description': 'Natural male voice'
            },
            'en-US-JennyNeural': {
                'name': 'Jenny',
                'gender': 'Female',
                'language': 'en-US',
                'description': 'Friendly female voice'
            },
            'en-US-GuyNeural': {
                'name': 'Guy',
                'gender': 'Male',
                'language': 'en-US',
                'description': 'Professional male voice'
            }
        }
    
    def _get_supported_languages(self) -> List[str]:
        """支持的语言"""
        return ['zh-CN', 'en-US', 'ja-JP', 'ko-KR', 'es-ES', 'fr-FR', 'de-DE']
    
    async def health_check(self) -> bool:
        """健康检查"""
        return self.edge_tts is not None
    
    async def synthesize(self, request: TTSRequest) -> TTSResponse:
        """使用Edge TTS合成语音"""
        if not self.edge_tts:
            raise Exception("Edge TTS未安装或初始化失败")
        
        try:
            # 设置输出路径
            if not request.output_path:
                output_dir = tempfile.gettempdir()
                timestamp = int(time.time() * 1000)
                request.output_path = os.path.join(output_dir, f"edge_tts_{timestamp}.mp3")
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(request.output_path), exist_ok=True)
            
            # 创建TTS通信对象
            communicate = self.edge_tts.Communicate(
                text=request.text,
                voice=request.voice,
                rate=f"{int((request.speed - 1) * 100):+d}%" if request.speed != 1.0 else "+0%",
                pitch=f"{int(request.pitch):+d}Hz" if request.pitch != 0.0 else "+0Hz",
                volume=f"{int((request.volume - 1) * 100):+d}%" if request.volume != 1.0 else "+0%"
            )
            
            # 保存音频文件
            await communicate.save(request.output_path)
            
            # 获取文件信息
            file_size = os.path.getsize(request.output_path) if os.path.exists(request.output_path) else 0
            
            # 估算音频时长（粗略计算）
            duration = len(request.text) * 0.1  # 假设每个字符0.1秒
            
            return TTSResponse(
                audio_path=request.output_path,
                duration=duration,
                file_size=file_size,
                voice_used=request.voice,
                service_used=self.name,
                success=True
            )
            
        except Exception as e:
            logger.error(f"Edge TTS合成失败: {e}")
            return TTSResponse(
                audio_path="",
                success=False,
                error_message=str(e),
                service_used=self.name
            )
    
    async def get_voices(self, language: str = 'zh-CN') -> List[Dict[str, Any]]:
        """获取可用声音列表"""
        if not self.edge_tts:
            return []
        
        try:
            voices = await self.edge_tts.list_voices()
            filtered_voices = []
            
            for voice in voices:
                if language.lower() in voice['Locale'].lower():
                    filtered_voices.append({
                        'name': voice['ShortName'],
                        'display_name': voice['FriendlyName'],
                        'gender': voice['Gender'],
                        'language': voice['Locale'],
                        'description': voice.get('Description', '')
                    })
            
            return filtered_voices
            
        except Exception as e:
            logger.error(f"获取Edge TTS声音列表失败: {e}")
            return []

class SiliconFlowTTSClient(BaseTTSClient):
    """SiliconFlow TTS客户端"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.api_key = config.get('api_key', '')
        self.base_url = config.get('base_url', 'https://api.siliconflow.cn/v1/audio/speech')
    
    def _get_supported_voices(self) -> Dict[str, Dict[str, Any]]:
        """SiliconFlow支持的声音"""
        return {
            'alloy': {
                'name': 'Alloy',
                'gender': 'Neutral',
                'language': 'en-US',
                'description': 'Neutral voice'
            },
            'echo': {
                'name': 'Echo',
                'gender': 'Male',
                'language': 'en-US',
                'description': 'Male voice'
            },
            'fable': {
                'name': 'Fable',
                'gender': 'Neutral',
                'language': 'en-US',
                'description': 'Storytelling voice'
            },
            'onyx': {
                'name': 'Onyx',
                'gender': 'Male',
                'language': 'en-US',
                'description': 'Deep male voice'
            },
            'nova': {
                'name': 'Nova',
                'gender': 'Female',
                'language': 'en-US',
                'description': 'Female voice'
            },
            'shimmer': {
                'name': 'Shimmer',
                'gender': 'Female',
                'language': 'en-US',
                'description': 'Bright female voice'
            }
        }
    
    async def health_check(self) -> bool:
        """健康检查"""
        return bool(self.api_key)
    
    async def synthesize(self, request: TTSRequest) -> TTSResponse:
        """使用SiliconFlow合成语音"""
        if not self.api_key:
            raise Exception("SiliconFlow API密钥未配置")
        
        try:
            # 设置输出路径
            if not request.output_path:
                output_dir = tempfile.gettempdir()
                timestamp = int(time.time() * 1000)
                request.output_path = os.path.join(output_dir, f"siliconflow_tts_{timestamp}.mp3")
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(request.output_path), exist_ok=True)
            
            # 构建请求数据
            data = {
                'model': 'tts-1',
                'input': request.text,
                'voice': request.voice if request.voice in self.supported_voices else 'alloy',
                'response_format': 'mp3',
                'speed': request.speed
            }
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            async with self.session.post(self.base_url, headers=headers, json=data) as response:
                if response.status == 200:
                    # 保存音频文件
                    async with aiofiles.open(request.output_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            await f.write(chunk)
                    
                    # 获取文件信息
                    file_size = os.path.getsize(request.output_path)
                    duration = len(request.text) * 0.1  # 估算时长
                    
                    return TTSResponse(
                        audio_path=request.output_path,
                        duration=duration,
                        file_size=file_size,
                        voice_used=request.voice,
                        service_used=self.name,
                        success=True
                    )
                else:
                    error_text = await response.text()
                    raise Exception(f"SiliconFlow TTS API错误 {response.status}: {error_text}")
                    
        except Exception as e:
            logger.error(f"SiliconFlow TTS合成失败: {e}")
            return TTSResponse(
                audio_path="",
                success=False,
                error_message=str(e),
                service_used=self.name
            )
    
    async def get_voices(self, language: str = 'en-US') -> List[Dict[str, Any]]:
        """获取可用声音列表"""
        voices = []
        for voice_id, voice_info in self.supported_voices.items():
            if language in voice_info['language']:
                voices.append({
                    'name': voice_id,
                    'display_name': voice_info['name'],
                    'gender': voice_info['gender'],
                    'language': voice_info['language'],
                    'description': voice_info['description']
                })
        return voices

class TTSServiceManager:
    """TTS服务管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.clients = {}
        self.default_engine = config.get('default_engine', 'edge')
        self._initialize_clients()
    
    def _initialize_clients(self):
        """初始化TTS客户端"""
        # Edge TTS（免费，无需API密钥）
        edge_config = self.config.get('edge_tts', {})
        self.clients['edge'] = EdgeTTSClient('Edge TTS', edge_config)
        
        # SiliconFlow TTS
        siliconflow_config = self.config.get('siliconflow', {})
        if siliconflow_config.get('api_key'):
            self.clients['siliconflow'] = SiliconFlowTTSClient('SiliconFlow TTS', siliconflow_config)
    
    def get_client(self, name: str) -> Optional[BaseTTSClient]:
        """获取TTS客户端"""
        return self.clients.get(name)
    
    def get_all_clients(self) -> List[BaseTTSClient]:
        """获取所有TTS客户端"""
        return list(self.clients.values())
    
    def get_best_client(self, language: str = 'zh-CN') -> Optional[BaseTTSClient]:
        """获取最佳TTS客户端"""
        # 中文优先使用Edge TTS
        if language.startswith('zh') and 'edge' in self.clients:
            return self.clients['edge']
        
        # 英文可以使用SiliconFlow或Edge TTS
        if language.startswith('en'):
            if 'siliconflow' in self.clients:
                return self.clients['siliconflow']
            elif 'edge' in self.clients:
                return self.clients['edge']
        
        # 默认使用Edge TTS
        return self.clients.get('edge')
    
    async def initialize_all(self):
        """初始化所有客户端"""
        for client in self.clients.values():
            await client.initialize()
    
    async def cleanup_all(self):
        """清理所有客户端"""
        for client in self.clients.values():
            await client.cleanup()
    
    async def synthesize_text(self, text: str, voice: str = None, language: str = 'zh-CN', 
                             output_path: str = None, engine: str = None) -> Optional[TTSResponse]:
        """合成语音（便捷方法）"""
        # 选择引擎
        if engine:
            client = self.get_client(engine)
        else:
            client = self.get_best_client(language)
        
        if not client:
            raise Exception(f"没有可用的TTS服务支持语言: {language}")
        
        # 选择声音
        if not voice:
            if language.startswith('zh'):
                voice = 'zh-CN-XiaoxiaoNeural'  # 默认中文声音
            else:
                voice = 'en-US-AriaNeural'  # 默认英文声音
        
        request = TTSRequest(
            text=text,
            voice=voice,
            language=language,
            output_path=output_path
        )
        
        return await client.synthesize(request)
    
    async def synthesize_storyboard_audio(self, storyboard_data: Dict[str, Any], 
                                        output_dir: str, language: str = 'zh-CN') -> Dict[str, List[str]]:
        """为分镜生成配音"""
        audio_files = {'zh': [], 'en': []}
        
        try:
            shots = storyboard_data.get('shots', [])
            
            for i, shot in enumerate(shots):
                # 中文配音
                zh_text = shot.get('narration', '')
                if zh_text:
                    zh_output = os.path.join(output_dir, f"shot_{i+1}_zh.mp3")
                    zh_response = await self.synthesize_text(
                        text=zh_text,
                        language='zh-CN',
                        output_path=zh_output,
                        engine='edge'
                    )
                    if zh_response and zh_response.success:
                        audio_files['zh'].append(zh_response.audio_path)
                
                # 英文配音
                en_text = shot.get('narration_en', '')
                if en_text:
                    en_output = os.path.join(output_dir, f"shot_{i+1}_en.mp3")
                    en_response = await self.synthesize_text(
                        text=en_text,
                        language='en-US',
                        output_path=en_output
                    )
                    if en_response and en_response.success:
                        audio_files['en'].append(en_response.audio_path)
                
                # 添加延迟避免API限制
                await asyncio.sleep(0.5)
            
            return audio_files
            
        except Exception as e:
            logger.error(f"分镜配音生成失败: {e}")
            return audio_files
    
    async def get_available_voices(self, language: str = 'zh-CN') -> Dict[str, List[Dict[str, Any]]]:
        """获取所有可用声音"""
        all_voices = {}
        
        for engine_name, client in self.clients.items():
            try:
                voices = await client.get_voices(language)
                all_voices[engine_name] = voices
            except Exception as e:
                logger.error(f"获取 {engine_name} 声音列表失败: {e}")
                all_voices[engine_name] = []
        
        return all_voices

# 便捷函数
def create_tts_service_manager(config: Dict[str, Any]) -> TTSServiceManager:
    """创建TTS服务管理器"""
    return TTSServiceManager(config)

if __name__ == "__main__":
    # 测试代码
    async def test_tts_service():
        # 模拟配置
        test_config = {
            'default_engine': 'edge',
            'edge_tts': {},
            'siliconflow': {
                'api_key': '',  # 需要配置实际API密钥
                'base_url': 'https://api.siliconflow.cn/v1/audio/speech'
            }
        }
        
        manager = create_tts_service_manager(test_config)
        await manager.initialize_all()
        
        # 测试中文语音合成
        try:
            response = await manager.synthesize_text(
                text="你好，这是一个语音合成测试。",
                language='zh-CN'
            )
            if response and response.success:
                print(f"中文语音合成成功: {response.audio_path}")
                print(f"使用服务: {response.service_used}")
                print(f"文件大小: {response.file_size} bytes")
        except Exception as e:
            print(f"中文语音合成失败: {e}")
        
        # 测试英文语音合成
        try:
            response = await manager.synthesize_text(
                text="Hello, this is a text-to-speech test.",
                language='en-US'
            )
            if response and response.success:
                print(f"英文语音合成成功: {response.audio_path}")
                print(f"使用服务: {response.service_used}")
        except Exception as e:
            print(f"英文语音合成失败: {e}")
        
        await manager.cleanup_all()
    
    # asyncio.run(test_tts_service())
    print("TTS服务模块已加载")