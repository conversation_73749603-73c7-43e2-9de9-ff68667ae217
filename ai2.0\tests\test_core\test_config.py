"""配置管理测试"""

import pytest
import tempfile
import yaml
from pathlib import Path
from src.core.config import ConfigManager


class TestConfigManager:
    """配置管理器测试"""
    
    def test_load_base_config(self):
        """测试加载基础配置"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir)
            
            # 创建基础配置文件
            base_config = {
                'app': {
                    'name': 'Test App',
                    'version': '1.0.0',
                    'debug': True
                },
                'database': {
                    'url': 'sqlite:///test.db'
                }
            }
            
            with open(config_dir / 'base.yaml', 'w', encoding='utf-8') as f:
                yaml.dump(base_config, f)
            
            # 创建环境目录
            env_dir = config_dir / 'environments'
            env_dir.mkdir()
            
            config_manager = ConfigManager(config_dir)
            
            assert config_manager.app.name == 'Test App'
            assert config_manager.app.version == '1.0.0'
            assert config_manager.app.debug is True
            assert config_manager.database.url == 'sqlite:///test.db'
    
    def test_environment_override(self):
        """测试环境配置覆盖"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir)
            
            # 基础配置
            base_config = {
                'app': {
                    'name': 'Test App',
                    'debug': False
                }
            }
            
            with open(config_dir / 'base.yaml', 'w', encoding='utf-8') as f:
                yaml.dump(base_config, f)
            
            # 环境配置
            env_dir = config_dir / 'environments'
            env_dir.mkdir()
            
            dev_config = {
                'app': {
                    'debug': True
                }
            }
            
            with open(env_dir / 'development.yaml', 'w', encoding='utf-8') as f:
                yaml.dump(dev_config, f)
            
            # 设置环境变量
            import os
            original_env = os.environ.get('APP_ENV')
            os.environ['APP_ENV'] = 'development'
            
            try:
                config_manager = ConfigManager(config_dir)
                
                assert config_manager.app.name == 'Test App'  # 保持基础配置
                assert config_manager.app.debug is True  # 被环境配置覆盖
            finally:
                if original_env:
                    os.environ['APP_ENV'] = original_env
                else:
                    os.environ.pop('APP_ENV', None)
    
    def test_get_and_set_config(self):
        """测试配置获取和设置"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir)
            
            base_config = {
                'test': {
                    'nested': {
                        'value': 'original'
                    }
                }
            }
            
            with open(config_dir / 'base.yaml', 'w', encoding='utf-8') as f:
                yaml.dump(base_config, f)
            
            # 创建环境目录
            env_dir = config_dir / 'environments'
            env_dir.mkdir()
            
            config_manager = ConfigManager(config_dir)
            
            # 测试获取
            assert config_manager.get('test.nested.value') == 'original'
            assert config_manager.get('nonexistent', 'default') == 'default'
            
            # 测试设置
            config_manager.set('test.nested.value', 'modified')
            assert config_manager.get('test.nested.value') == 'modified'
            
            # 测试设置新值
            config_manager.set('test.new.key', 'new_value')
            assert config_manager.get('test.new.key') == 'new_value'
    
    def test_config_objects_creation(self):
        """测试配置对象创建"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir)
            
            # 创建完整配置
            config = {
                'app': {
                    'name': 'Test App',
                    'version': '2.0.0',
                    'debug': True,
                    'log_level': 'DEBUG'
                },
                'database': {
                    'url': 'postgresql://user:pass@localhost/db',
                    'echo': True,
                    'pool_size': 10
                },
                'ui': {
                    'theme': 'dark',
                    'language': 'en-US',
                    'window': {
                        'width': 1920,
                        'height': 1080
                    }
                }
            }
            
            with open(config_dir / 'base.yaml', 'w', encoding='utf-8') as f:
                yaml.dump(config, f)
            
            # 创建环境目录
            env_dir = config_dir / 'environments'
            env_dir.mkdir()
            
            config_manager = ConfigManager(config_dir)
            
            # 验证应用配置
            assert config_manager.app.name == 'Test App'
            assert config_manager.app.version == '2.0.0'
            assert config_manager.app.debug is True
            assert config_manager.app.log_level == 'DEBUG'
            
            # 验证数据库配置
            assert config_manager.database.url == 'postgresql://user:pass@localhost/db'
            assert config_manager.database.echo is True
            assert config_manager.database.pool_size == 10
            
            # 验证UI配置
            assert config_manager.ui.theme == 'dark'
            assert config_manager.ui.language == 'en-US'
            assert config_manager.ui.window_width == 1920
            assert config_manager.ui.window_height == 1080
