# -*- coding: utf-8 -*-
"""
AI视频生成工作流程自动化
整合文章创作、分镜生成、配音、绘图、视频生成、发布等全流程
"""

import asyncio
import json
import logging
import os
import tempfile
import time
from typing import Dict, Any, List, Optional, Union, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
import aiofiles
from datetime import datetime, timedelta

# 导入各个服务
from ..services.llm_service import LLMServiceManager, LLMRequest
from ..services.translation_service import TranslationServiceManager, TranslationRequest
from ..services.tts_service import TTSServiceManager, TTSRequest
from ..services.image_service import ImageServiceManager, ImageRequest
from ..services.video_service import VideoServiceManager, VideoRequest
from ..services.social_media_service import SocialMediaServiceManager, PublishRequest
from ..core.mcp_service_manager import MCPServiceManager

logger = logging.getLogger(__name__)

@dataclass
class WorkflowConfig:
    """工作流程配置"""
    # 输入配置
    input_text: str = ""  # 输入文章或主题
    target_language: str = "zh-CN"  # 目标语言
    video_style: str = "realistic"  # 视频风格
    video_duration: float = 30.0  # 视频总时长（秒）
    
    # 输出配置
    output_dir: str = ""
    video_title: str = ""
    video_description: str = ""
    video_tags: List[str] = None
    
    # 发布配置
    publish_platforms: List[str] = None
    publish_privacy: str = "public"
    schedule_time: Optional[str] = None
    
    # 质量配置
    image_quality: str = "high"  # low, medium, high
    video_quality: str = "1080p"  # 720p, 1080p, 4k
    audio_quality: str = "high"  # low, medium, high
    
    # 服务选择
    preferred_llm: str = ""  # 优先使用的LLM服务
    preferred_tts: str = ""  # 优先使用的TTS服务
    preferred_image: str = ""  # 优先使用的图像生成服务
    preferred_video: str = ""  # 优先使用的视频生成服务

@dataclass
class WorkflowStep:
    """工作流程步骤"""
    name: str
    status: str = "pending"  # pending, running, completed, failed
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    result: Any = None
    error: str = ""
    progress: float = 0.0  # 0.0 - 1.0

@dataclass
class WorkflowResult:
    """工作流程结果"""
    success: bool = False
    total_time: float = 0.0
    steps: List[WorkflowStep] = None
    
    # 生成的文件
    article_file: str = ""
    storyboard_file: str = ""
    audio_files: List[str] = None
    image_files: List[str] = None
    video_files: List[str] = None
    final_video: str = ""
    
    # 发布结果
    publish_results: Dict[str, Any] = None
    
    # 错误信息
    error_message: str = ""
    failed_steps: List[str] = None

class VideoGenerationWorkflow:
    """AI视频生成工作流程"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.workflow_config = None
        self.steps = []
        self.current_step = 0
        self.start_time = 0.0
        
        # 初始化服务管理器
        self.mcp_manager = MCPServiceManager(config)
        self.llm_manager = None
        self.translation_manager = None
        self.tts_manager = None
        self.image_manager = None
        self.video_manager = None
        self.social_media_manager = None
        
        # 进度回调
        self.progress_callback: Optional[Callable] = None
        self.step_callback: Optional[Callable] = None
    
    async def initialize(self):
        """初始化工作流程"""
        try:
            # 初始化各个服务管理器
            llm_config = self.config.get('llm', {})
            self.llm_manager = LLMServiceManager(llm_config)
            
            translation_config = self.config.get('translation', {})
            self.translation_manager = TranslationServiceManager(translation_config)
            
            tts_config = self.config.get('tts', {})
            self.tts_manager = TTSServiceManager(tts_config)
            
            image_config = self.config.get('image_generation', {})
            self.image_manager = ImageServiceManager(image_config)
            
            video_config = self.config.get('video_generation', {})
            self.video_manager = VideoServiceManager(video_config)
            
            social_media_config = self.config.get('social_media', {})
            self.social_media_manager = SocialMediaServiceManager(social_media_config)
            
            # 初始化所有服务
            await asyncio.gather(
                self.llm_manager.initialize_all(),
                self.translation_manager.initialize_all(),
                self.tts_manager.initialize_all(),
                self.image_manager.initialize_all(),
                self.video_manager.initialize_all(),
                self.social_media_manager.initialize_all()
            )
            
            logger.info("视频生成工作流程初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"工作流程初始化失败: {e}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        try:
            await asyncio.gather(
                self.llm_manager.cleanup_all() if self.llm_manager else asyncio.sleep(0),
                self.translation_manager.cleanup_all() if self.translation_manager else asyncio.sleep(0),
                self.tts_manager.cleanup_all() if self.tts_manager else asyncio.sleep(0),
                self.image_manager.cleanup_all() if self.image_manager else asyncio.sleep(0),
                self.video_manager.cleanup_all() if self.video_manager else asyncio.sleep(0),
                self.social_media_manager.cleanup_all() if self.social_media_manager else asyncio.sleep(0)
            )
            logger.info("工作流程资源清理完成")
        except Exception as e:
            logger.error(f"工作流程清理失败: {e}")
    
    def set_progress_callback(self, callback: Callable[[float, str], None]):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def set_step_callback(self, callback: Callable[[WorkflowStep], None]):
        """设置步骤回调函数"""
        self.step_callback = callback
    
    def _update_progress(self, progress: float, message: str = ""):
        """更新进度"""
        if self.progress_callback:
            self.progress_callback(progress, message)
    
    def _update_step(self, step: WorkflowStep):
        """更新步骤状态"""
        if self.step_callback:
            self.step_callback(step)
    
    def _create_step(self, name: str) -> WorkflowStep:
        """创建工作流程步骤"""
        step = WorkflowStep(name=name)
        self.steps.append(step)
        return step
    
    def _start_step(self, step: WorkflowStep):
        """开始步骤"""
        step.status = "running"
        step.start_time = time.time()
        self._update_step(step)
        logger.info(f"开始执行步骤: {step.name}")
    
    def _complete_step(self, step: WorkflowStep, result: Any = None):
        """完成步骤"""
        step.status = "completed"
        step.end_time = time.time()
        step.result = result
        step.progress = 1.0
        self._update_step(step)
        logger.info(f"步骤完成: {step.name}")
    
    def _fail_step(self, step: WorkflowStep, error: str):
        """步骤失败"""
        step.status = "failed"
        step.end_time = time.time()
        step.error = error
        self._update_step(step)
        logger.error(f"步骤失败: {step.name} - {error}")
    
    async def run_workflow(self, workflow_config: WorkflowConfig) -> WorkflowResult:
        """运行完整的视频生成工作流程"""
        self.workflow_config = workflow_config
        self.start_time = time.time()
        self.steps = []
        
        # 创建输出目录
        if not workflow_config.output_dir:
            workflow_config.output_dir = os.path.join(tempfile.gettempdir(), f"video_gen_{int(time.time())}")
        
        os.makedirs(workflow_config.output_dir, exist_ok=True)
        
        result = WorkflowResult(steps=[])
        
        try:
            # 步骤1: 文章创作/改写
            step1 = self._create_step("文章创作/改写")
            self._start_step(step1)
            self._update_progress(0.1, "正在创作/改写文章...")
            
            article_result = await self._step_article_creation(workflow_config)
            if article_result:
                self._complete_step(step1, article_result)
                result.article_file = article_result.get('file_path', '')
            else:
                self._fail_step(step1, "文章创作失败")
                result.error_message = "文章创作失败"
                return result
            
            # 步骤2: 五阶段分镜生成
            step2 = self._create_step("五阶段分镜生成")
            self._start_step(step2)
            self._update_progress(0.2, "正在生成分镜脚本...")
            
            storyboard_result = await self._step_storyboard_generation(workflow_config, article_result)
            if storyboard_result:
                self._complete_step(step2, storyboard_result)
                result.storyboard_file = storyboard_result.get('file_path', '')
            else:
                self._fail_step(step2, "分镜生成失败")
                result.error_message = "分镜生成失败"
                return result
            
            # 步骤3: 配音生成
            step3 = self._create_step("配音生成")
            self._start_step(step3)
            self._update_progress(0.35, "正在生成配音...")
            
            audio_result = await self._step_audio_generation(workflow_config, storyboard_result)
            if audio_result:
                self._complete_step(step3, audio_result)
                result.audio_files = audio_result.get('audio_files', [])
            else:
                self._fail_step(step3, "配音生成失败")
                result.error_message = "配音生成失败"
                return result
            
            # 步骤4: 图像生成
            step4 = self._create_step("图像生成")
            self._start_step(step4)
            self._update_progress(0.5, "正在生成图像...")
            
            image_result = await self._step_image_generation(workflow_config, storyboard_result)
            if image_result:
                self._complete_step(step4, image_result)
                result.image_files = image_result.get('image_files', [])
            else:
                self._fail_step(step4, "图像生成失败")
                result.error_message = "图像生成失败"
                return result
            
            # 步骤5: 图转视频
            step5 = self._create_step("图转视频")
            self._start_step(step5)
            self._update_progress(0.7, "正在生成视频片段...")
            
            video_result = await self._step_video_generation(workflow_config, storyboard_result, image_result)
            if video_result:
                self._complete_step(step5, video_result)
                result.video_files = video_result.get('video_files', [])
            else:
                self._fail_step(step5, "视频生成失败")
                result.error_message = "视频生成失败"
                return result
            
            # 步骤6: 视频合成
            step6 = self._create_step("视频合成")
            self._start_step(step6)
            self._update_progress(0.85, "正在合成最终视频...")
            
            merge_result = await self._step_video_merging(workflow_config, video_result, audio_result)
            if merge_result:
                self._complete_step(step6, merge_result)
                result.final_video = merge_result.get('final_video', '')
            else:
                self._fail_step(step6, "视频合成失败")
                result.error_message = "视频合成失败"
                return result
            
            # 步骤7: 发布（可选）
            if workflow_config.publish_platforms:
                step7 = self._create_step("视频发布")
                self._start_step(step7)
                self._update_progress(0.95, "正在发布视频...")
                
                publish_result = await self._step_video_publishing(workflow_config, merge_result)
                if publish_result:
                    self._complete_step(step7, publish_result)
                    result.publish_results = publish_result
                else:
                    self._fail_step(step7, "视频发布失败")
                    # 发布失败不影响整体成功
            
            # 工作流程完成
            result.success = True
            result.total_time = time.time() - self.start_time
            result.steps = self.steps
            
            self._update_progress(1.0, "视频生成工作流程完成！")
            logger.info(f"视频生成工作流程完成，耗时: {result.total_time:.2f}秒")
            
            return result
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            result.total_time = time.time() - self.start_time
            result.steps = self.steps
            
            logger.error(f"视频生成工作流程失败: {e}")
            return result
    
    async def _step_article_creation(self, config: WorkflowConfig) -> Optional[Dict[str, Any]]:
        """步骤1: 文章创作/改写"""
        try:
            # 选择LLM客户端
            client = self.llm_manager.get_best_client("article_writing")
            if not client:
                raise Exception("没有可用的LLM服务")
            
            # 构建提示词
            if config.input_text.strip():
                # 改写现有文章
                prompt = f"""
请将以下文章改写为适合制作视频的脚本内容，要求：
1. 内容生动有趣，适合视觉呈现
2. 语言简洁明了，适合配音
3. 结构清晰，便于分镜
4. 时长控制在{config.video_duration}秒左右的内容

原文章：
{config.input_text}

请输出改写后的视频脚本：
"""
            else:
                # 创作新文章
                prompt = f"""
请创作一篇适合制作{config.video_duration}秒视频的脚本，要求：
1. 主题有趣且具有视觉冲击力
2. 内容结构清晰，适合分镜
3. 语言生动，适合配音
4. 包含具体的场景描述

请输出视频脚本：
"""
            
            # 生成文章
            request = LLMRequest(
                prompt=prompt,
                max_tokens=2000,
                temperature=0.7
            )
            
            response = await client.generate_text(request)
            
            if response and response.success:
                # 保存文章
                article_file = os.path.join(config.output_dir, "article.txt")
                async with aiofiles.open(article_file, 'w', encoding='utf-8') as f:
                    await f.write(response.text)
                
                return {
                    'file_path': article_file,
                    'content': response.text,
                    'model_used': response.model_used,
                    'generation_time': response.generation_time
                }
            else:
                raise Exception("文章生成失败")
                
        except Exception as e:
            logger.error(f"文章创作步骤失败: {e}")
            return None
    
    async def _step_storyboard_generation(self, config: WorkflowConfig, article_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """步骤2: 五阶段分镜生成"""
        try:
            # 选择LLM客户端
            client = self.llm_manager.get_best_client("storyboard_generation")
            if not client:
                raise Exception("没有可用的LLM服务")
            
            article_content = article_result.get('content', '')
            
            # 生成五阶段分镜
            storyboard_data = await client.generate_storyboard(
                article_content, 
                config.video_duration,
                config.video_style
            )
            
            if storyboard_data:
                # 保存分镜数据
                storyboard_file = os.path.join(config.output_dir, "storyboard.json")
                async with aiofiles.open(storyboard_file, 'w', encoding='utf-8') as f:
                    await f.write(json.dumps(storyboard_data, ensure_ascii=False, indent=2))
                
                return {
                    'file_path': storyboard_file,
                    'storyboard_data': storyboard_data,
                    'shots_count': len(storyboard_data.get('shots', [])),
                    'total_duration': storyboard_data.get('total_duration', 0)
                }
            else:
                raise Exception("分镜生成失败")
                
        except Exception as e:
            logger.error(f"分镜生成步骤失败: {e}")
            return None
    
    async def _step_audio_generation(self, config: WorkflowConfig, storyboard_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """步骤3: 配音生成"""
        try:
            storyboard_data = storyboard_result.get('storyboard_data', {})
            
            # 为分镜生成配音
            audio_files = await self.tts_manager.generate_storyboard_audio(
                storyboard_data,
                config.output_dir,
                config.target_language
            )
            
            if audio_files:
                return {
                    'audio_files': audio_files,
                    'audio_count': len(audio_files),
                    'language': config.target_language
                }
            else:
                raise Exception("配音生成失败")
                
        except Exception as e:
            logger.error(f"配音生成步骤失败: {e}")
            return None
    
    async def _step_image_generation(self, config: WorkflowConfig, storyboard_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """步骤4: 图像生成"""
        try:
            storyboard_data = storyboard_result.get('storyboard_data', {})
            
            # 为分镜生成图像
            image_files = await self.image_manager.generate_storyboard_images(
                storyboard_data,
                config.output_dir,
                config.image_quality
            )
            
            if image_files:
                return {
                    'image_files': image_files,
                    'image_count': len(image_files),
                    'quality': config.image_quality
                }
            else:
                raise Exception("图像生成失败")
                
        except Exception as e:
            logger.error(f"图像生成步骤失败: {e}")
            return None
    
    async def _step_video_generation(self, config: WorkflowConfig, storyboard_result: Dict[str, Any], image_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """步骤5: 图转视频"""
        try:
            storyboard_data = storyboard_result.get('storyboard_data', {})
            image_files = image_result.get('image_files', [])
            
            # 为分镜生成视频
            video_files = await self.video_manager.generate_storyboard_videos(
                storyboard_data,
                image_files,
                config.output_dir
            )
            
            if video_files:
                return {
                    'video_files': video_files,
                    'video_count': len(video_files),
                    'quality': config.video_quality
                }
            else:
                raise Exception("视频生成失败")
                
        except Exception as e:
            logger.error(f"视频生成步骤失败: {e}")
            return None
    
    async def _step_video_merging(self, config: WorkflowConfig, video_result: Dict[str, Any], audio_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """步骤6: 视频合成"""
        try:
            video_files = video_result.get('video_files', [])
            audio_files = audio_result.get('audio_files', [])
            
            # 合成最终视频
            final_video = os.path.join(config.output_dir, "final_video.mp4")
            
            success = await self.video_manager.merge_videos(
                video_files,
                audio_files,
                final_video
            )
            
            if success and os.path.exists(final_video):
                return {
                    'final_video': final_video,
                    'file_size': os.path.getsize(final_video),
                    'video_segments': len(video_files),
                    'audio_segments': len(audio_files)
                }
            else:
                raise Exception("视频合成失败")
                
        except Exception as e:
            logger.error(f"视频合成步骤失败: {e}")
            return None
    
    async def _step_video_publishing(self, config: WorkflowConfig, merge_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """步骤7: 视频发布"""
        try:
            final_video = merge_result.get('final_video', '')
            
            if not os.path.exists(final_video):
                raise Exception("最终视频文件不存在")
            
            # 准备发布元数据
            metadata = {
                'title': config.video_title or "AI生成视频",
                'description': config.video_description or "使用AI技术生成的视频内容",
                'tags': config.video_tags or ['AI', '视频生成', '自动化'],
                'privacy': config.publish_privacy
            }
            
            # 发布到多个平台
            if config.schedule_time:
                # 定时发布
                result = await self.social_media_manager.schedule_publish(
                    final_video,
                    metadata,
                    config.schedule_time,
                    config.publish_platforms
                )
            else:
                # 立即发布
                result = await self.social_media_manager.publish_video_workflow(
                    final_video,
                    metadata,
                    config.publish_platforms
                )
            
            return result
            
        except Exception as e:
            logger.error(f"视频发布步骤失败: {e}")
            return None
    
    async def run_custom_workflow(self, steps: List[str], config: WorkflowConfig) -> WorkflowResult:
        """运行自定义工作流程"""
        # 允许用户自定义执行哪些步骤
        # 实现自定义工作流程逻辑
        pass
    
    def get_workflow_status(self) -> Dict[str, Any]:
        """获取工作流程状态"""
        total_steps = len(self.steps)
        completed_steps = len([s for s in self.steps if s.status == "completed"])
        failed_steps = len([s for s in self.steps if s.status == "failed"])
        running_steps = len([s for s in self.steps if s.status == "running"])
        
        overall_progress = completed_steps / total_steps if total_steps > 0 else 0.0
        
        return {
            'total_steps': total_steps,
            'completed_steps': completed_steps,
            'failed_steps': failed_steps,
            'running_steps': running_steps,
            'overall_progress': overall_progress,
            'current_step': self.current_step,
            'elapsed_time': time.time() - self.start_time if self.start_time > 0 else 0,
            'steps': [asdict(step) for step in self.steps]
        }

# 便捷函数
def create_video_generation_workflow(config: Dict[str, Any]) -> VideoGenerationWorkflow:
    """创建视频生成工作流程"""
    return VideoGenerationWorkflow(config)

async def run_video_generation(input_text: str, output_dir: str = "",
                             target_language: str = "zh-CN",
                             video_duration: float = 30.0,
                             publish_platforms: List[str] = None,
                             config: Dict[str, Any] = None) -> WorkflowResult:
    """便捷的视频生成函数"""
    if not config:
        config = {}  # 使用默认配置
    
    workflow = create_video_generation_workflow(config)
    await workflow.initialize()
    
    try:
        workflow_config = WorkflowConfig(
            input_text=input_text,
            output_dir=output_dir,
            target_language=target_language,
            video_duration=video_duration,
            publish_platforms=publish_platforms or []
        )
        
        result = await workflow.run_workflow(workflow_config)
        return result
        
    finally:
        await workflow.cleanup()

if __name__ == "__main__":
    # 测试代码
    async def test_workflow():
        # 模拟配置
        test_config = {
            'llm': {
                'routing_strategy': 'quality_first',
                'clients': {
                    'zhipu': {
                        'api_key': '',  # 需要配置实际API密钥
                        'base_url': 'https://open.bigmodel.cn/api/paas/v4/chat/completions'
                    }
                }
            },
            'tts': {
                'default_engine': 'edge',
                'engines': {
                    'edge_tts': {
                        'voice': 'zh-CN-XiaoxiaoNeural'
                    }
                }
            },
            'image_generation': {
                'routing_strategy': 'quality_first',
                'engines': {
                    'pollinations': {
                        'base_url': 'https://image.pollinations.ai/prompt'
                    }
                }
            },
            'video_generation': {
                'routing_strategy': 'quality_first',
                'engines': {
                    'cogvideox': {
                        'api_key': '',  # 需要配置实际API密钥
                        'base_url': 'https://open.bigmodel.cn/api/paas/v4/videos/generations'
                    }
                }
            },
            'social_media': {
                'platforms': {}
            }
        }
        
        # 测试工作流程
        try:
            result = await run_video_generation(
                input_text="介绍人工智能的发展历程和未来趋势",
                target_language="zh-CN",
                video_duration=30.0,
                config=test_config
            )
            
            print(f"工作流程结果: {result.success}")
            print(f"总耗时: {result.total_time:.2f}秒")
            print(f"完成步骤: {len([s for s in result.steps if s.status == 'completed'])}")
            print(f"失败步骤: {len([s for s in result.steps if s.status == 'failed'])}")
            
            if result.final_video:
                print(f"最终视频: {result.final_video}")
            
            if result.error_message:
                print(f"错误信息: {result.error_message}")
                
        except Exception as e:
            print(f"工作流程测试失败: {e}")
    
    # asyncio.run(test_workflow())
    print("视频生成工作流程模块已加载")