"""缓存系统

提供多层缓存机制，包括内存缓存、文件缓存和LRU缓存。
"""

import time
import json
import pickle
import hashlib
import asyncio
from typing import Any, Optional, Dict, Callable, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import OrderedDict
import threading
import weakref

from src.utils.logger import get_logger


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_at: float
    expires_at: Optional[float] = None
    access_count: int = 0
    last_accessed: float = 0
    size_bytes: int = 0
    
    def __post_init__(self):
        if self.last_accessed == 0:
            self.last_accessed = self.created_at
        
        # 估算大小
        if self.size_bytes == 0:
            try:
                if hasattr(self.value, '__sizeof__'):
                    self.size_bytes = self.value.__sizeof__()
                else:
                    self.size_bytes = len(str(self.value))
            except Exception:
                self.size_bytes = 0
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.expires_at is None:
            return False
        return time.time() > self.expires_at
    
    def access(self) -> None:
        """记录访问"""
        self.access_count += 1
        self.last_accessed = time.time()


class LRUCache:
    """LRU缓存实现"""
    
    def __init__(self, max_size: int = 1000, ttl: Optional[int] = None):
        self.max_size = max_size
        self.ttl = ttl  # 生存时间（秒）
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
        self.logger = get_logger(__name__)
        
        # 统计信息
        self.hits = 0
        self.misses = 0
        self.evictions = 0
    
    def _make_key(self, key: Any) -> str:
        """生成缓存键"""
        if isinstance(key, str):
            return key
        elif isinstance(key, (int, float, bool)):
            return str(key)
        else:
            # 对复杂对象生成哈希
            key_str = json.dumps(key, sort_keys=True, default=str)
            return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, key: Any) -> Optional[Any]:
        """获取缓存值"""
        cache_key = self._make_key(key)
        
        with self._lock:
            if cache_key not in self._cache:
                self.misses += 1
                return None
            
            entry = self._cache[cache_key]
            
            # 检查是否过期
            if entry.is_expired():
                del self._cache[cache_key]
                self.misses += 1
                return None
            
            # 更新访问信息
            entry.access()
            
            # 移动到末尾（最近使用）
            self._cache.move_to_end(cache_key)
            
            self.hits += 1
            return entry.value
    
    def put(self, key: Any, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        cache_key = self._make_key(key)
        current_time = time.time()
        
        # 计算过期时间
        expires_at = None
        if ttl is not None:
            expires_at = current_time + ttl
        elif self.ttl is not None:
            expires_at = current_time + self.ttl
        
        entry = CacheEntry(
            key=cache_key,
            value=value,
            created_at=current_time,
            expires_at=expires_at
        )
        
        with self._lock:
            # 如果键已存在，更新它
            if cache_key in self._cache:
                self._cache[cache_key] = entry
                self._cache.move_to_end(cache_key)
            else:
                # 检查是否需要淘汰
                if len(self._cache) >= self.max_size:
                    self._evict_lru()
                
                self._cache[cache_key] = entry
    
    def _evict_lru(self) -> None:
        """淘汰最少使用的条目"""
        if self._cache:
            evicted_key, _ = self._cache.popitem(last=False)
            self.evictions += 1
            self.logger.debug(f"Evicted LRU cache entry: {evicted_key}")
    
    def delete(self, key: Any) -> bool:
        """删除缓存条目"""
        cache_key = self._make_key(key)
        
        with self._lock:
            if cache_key in self._cache:
                del self._cache[cache_key]
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self.hits = 0
            self.misses = 0
            self.evictions = 0
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self.hits + self.misses
        hit_rate = self.hits / total_requests if total_requests > 0 else 0
        
        total_size_bytes = sum(entry.size_bytes for entry in self._cache.values())
        
        return {
            "size": len(self._cache),
            "max_size": self.max_size,
            "hits": self.hits,
            "misses": self.misses,
            "evictions": self.evictions,
            "hit_rate": hit_rate,
            "total_size_bytes": total_size_bytes,
            "total_size_mb": total_size_bytes / 1024 / 1024
        }


class FileCache:
    """文件缓存实现"""
    
    def __init__(self, cache_dir: Union[str, Path], max_size_mb: int = 1000):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_size_mb = max_size_mb
        self.logger = get_logger(__name__)
        
        # 元数据文件
        self.metadata_file = self.cache_dir / "cache_metadata.json"
        self.metadata: Dict[str, Dict[str, Any]] = {}
        self._load_metadata()
    
    def _load_metadata(self) -> None:
        """加载缓存元数据"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    self.metadata = json.load(f)
            except Exception as e:
                self.logger.error(f"Failed to load cache metadata: {e}")
                self.metadata = {}
    
    def _save_metadata(self) -> None:
        """保存缓存元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save cache metadata: {e}")
    
    def _make_filename(self, key: str) -> str:
        """生成文件名"""
        # 使用哈希避免文件名过长或包含非法字符
        hash_key = hashlib.md5(key.encode()).hexdigest()
        return f"cache_{hash_key}.pkl"
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        filename = self._make_filename(key)
        file_path = self.cache_dir / filename
        
        if not file_path.exists():
            return None
        
        # 检查元数据
        if key in self.metadata:
            meta = self.metadata[key]
            if meta.get('expires_at') and time.time() > meta['expires_at']:
                # 过期，删除文件
                self._delete_file(key, file_path)
                return None
        
        try:
            with open(file_path, 'rb') as f:
                value = pickle.load(f)
            
            # 更新访问时间
            if key in self.metadata:
                self.metadata[key]['last_accessed'] = time.time()
                self.metadata[key]['access_count'] = self.metadata[key].get('access_count', 0) + 1
                self._save_metadata()
            
            return value
        
        except Exception as e:
            self.logger.error(f"Failed to load cache file {filename}: {e}")
            self._delete_file(key, file_path)
            return None
    
    def put(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        filename = self._make_filename(key)
        file_path = self.cache_dir / filename
        
        try:
            # 保存数据
            with open(file_path, 'wb') as f:
                pickle.dump(value, f)
            
            # 更新元数据
            current_time = time.time()
            file_size = file_path.stat().st_size
            
            self.metadata[key] = {
                'filename': filename,
                'created_at': current_time,
                'last_accessed': current_time,
                'expires_at': current_time + ttl if ttl else None,
                'size_bytes': file_size,
                'access_count': 0
            }
            
            self._save_metadata()
            
            # 检查缓存大小
            self._cleanup_if_needed()
        
        except Exception as e:
            self.logger.error(f"Failed to save cache file {filename}: {e}")
    
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        filename = self._make_filename(key)
        file_path = self.cache_dir / filename
        return self._delete_file(key, file_path)
    
    def _delete_file(self, key: str, file_path: Path) -> bool:
        """删除缓存文件"""
        try:
            if file_path.exists():
                file_path.unlink()
            
            if key in self.metadata:
                del self.metadata[key]
                self._save_metadata()
            
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to delete cache file {file_path}: {e}")
            return False
    
    def _cleanup_if_needed(self) -> None:
        """如果需要则清理缓存"""
        total_size = sum(meta['size_bytes'] for meta in self.metadata.values())
        max_size_bytes = self.max_size_mb * 1024 * 1024
        
        if total_size > max_size_bytes:
            # 按最后访问时间排序，删除最旧的文件
            sorted_items = sorted(
                self.metadata.items(),
                key=lambda x: x[1]['last_accessed']
            )
            
            for key, meta in sorted_items:
                if total_size <= max_size_bytes * 0.8:  # 清理到80%
                    break
                
                file_path = self.cache_dir / meta['filename']
                if self._delete_file(key, file_path):
                    total_size -= meta['size_bytes']
    
    def clear(self) -> None:
        """清空缓存"""
        try:
            for file_path in self.cache_dir.glob("cache_*.pkl"):
                file_path.unlink()
            
            self.metadata.clear()
            self._save_metadata()
        
        except Exception as e:
            self.logger.error(f"Failed to clear cache: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_size = sum(meta['size_bytes'] for meta in self.metadata.values())
        total_access = sum(meta['access_count'] for meta in self.metadata.values())
        
        return {
            "size": len(self.metadata),
            "total_size_bytes": total_size,
            "total_size_mb": total_size / 1024 / 1024,
            "max_size_mb": self.max_size_mb,
            "total_access_count": total_access,
            "cache_dir": str(self.cache_dir)
        }


class MultiLevelCache:
    """多级缓存系统"""
    
    def __init__(
        self,
        memory_cache_size: int = 1000,
        memory_ttl: Optional[int] = 3600,
        file_cache_dir: Optional[Union[str, Path]] = None,
        file_cache_size_mb: int = 1000
    ):
        # L1缓存：内存缓存
        self.memory_cache = LRUCache(max_size=memory_cache_size, ttl=memory_ttl)
        
        # L2缓存：文件缓存
        self.file_cache = None
        if file_cache_dir:
            self.file_cache = FileCache(file_cache_dir, file_cache_size_mb)
        
        self.logger = get_logger(__name__)
    
    def get(self, key: Any) -> Optional[Any]:
        """获取缓存值"""
        # 先从内存缓存获取
        value = self.memory_cache.get(key)
        if value is not None:
            return value
        
        # 再从文件缓存获取
        if self.file_cache:
            cache_key = self.memory_cache._make_key(key)
            value = self.file_cache.get(cache_key)
            if value is not None:
                # 回填到内存缓存
                self.memory_cache.put(key, value)
                return value
        
        return None
    
    def put(self, key: Any, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        # 存储到内存缓存
        self.memory_cache.put(key, value, ttl)
        
        # 存储到文件缓存
        if self.file_cache:
            cache_key = self.memory_cache._make_key(key)
            self.file_cache.put(cache_key, value, ttl)
    
    def delete(self, key: Any) -> bool:
        """删除缓存条目"""
        memory_deleted = self.memory_cache.delete(key)
        file_deleted = True
        
        if self.file_cache:
            cache_key = self.memory_cache._make_key(key)
            file_deleted = self.file_cache.delete(cache_key)
        
        return memory_deleted or file_deleted
    
    def clear(self) -> None:
        """清空所有缓存"""
        self.memory_cache.clear()
        if self.file_cache:
            self.file_cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        stats = {
            "memory_cache": self.memory_cache.get_stats()
        }
        
        if self.file_cache:
            stats["file_cache"] = self.file_cache.get_stats()
        
        return stats


# 缓存装饰器
def cached(
    cache_instance: Optional[Union[LRUCache, MultiLevelCache]] = None,
    ttl: Optional[int] = None,
    key_func: Optional[Callable] = None
):
    """缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        cache = cache_instance or _default_cache
        
        def make_cache_key(*args, **kwargs) -> str:
            if key_func:
                return key_func(*args, **kwargs)
            
            # 默认键生成策略
            key_parts = [func.__name__]
            key_parts.extend(str(arg) for arg in args)
            key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
            return ":".join(key_parts)
        
        if asyncio.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                cache_key = make_cache_key(*args, **kwargs)
                
                # 尝试从缓存获取
                cached_result = cache.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # 执行函数并缓存结果
                result = await func(*args, **kwargs)
                cache.put(cache_key, result, ttl)
                return result
            
            return async_wrapper
        else:
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                cache_key = make_cache_key(*args, **kwargs)
                
                # 尝试从缓存获取
                cached_result = cache.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # 执行函数并缓存结果
                result = func(*args, **kwargs)
                cache.put(cache_key, result, ttl)
                return result
            
            return sync_wrapper
    
    return decorator


# 默认缓存实例
_default_cache: Optional[MultiLevelCache] = None


def get_default_cache() -> MultiLevelCache:
    """获取默认缓存实例"""
    global _default_cache
    if _default_cache is None:
        cache_dir = Path("cache")
        _default_cache = MultiLevelCache(
            memory_cache_size=1000,
            memory_ttl=3600,
            file_cache_dir=cache_dir,
            file_cache_size_mb=500
        )
    return _default_cache
