"""基础主题类

定义主题的基础结构和接口。
"""

from typing import Dict, Any, Optional
from abc import ABC, abstractmethod
from dataclasses import dataclass
from PyQt6.QtGui import QColor, QPalette, QFont


@dataclass
class ColorScheme:
    """颜色方案"""
    primary: str = "#2196F3"
    secondary: str = "#FFC107"
    success: str = "#4CAF50"
    warning: str = "#FF9800"
    error: str = "#F44336"
    info: str = "#2196F3"
    
    # 背景色
    background: str = "#FFFFFF"
    surface: str = "#F5F5F5"
    card: str = "#FFFFFF"
    
    # 文本色
    text_primary: str = "#212121"
    text_secondary: str = "#757575"
    text_disabled: str = "#BDBDBD"
    
    # 边框色
    border: str = "#E0E0E0"
    divider: str = "#E0E0E0"
    
    # 状态色
    hover: str = "#F5F5F5"
    selected: str = "#E3F2FD"
    disabled: str = "#F5F5F5"


@dataclass
class Typography:
    """字体排版"""
    font_family: str = "Segoe UI, Arial, sans-serif"
    font_size_xs: int = 10
    font_size_sm: int = 12
    font_size_md: int = 14
    font_size_lg: int = 16
    font_size_xl: int = 18
    font_size_xxl: int = 24
    
    line_height: float = 1.5
    letter_spacing: float = 0.0


@dataclass
class Spacing:
    """间距系统"""
    xs: int = 4
    sm: int = 8
    md: int = 16
    lg: int = 24
    xl: int = 32
    xxl: int = 48


@dataclass
class BorderRadius:
    """圆角系统"""
    none: int = 0
    sm: int = 4
    md: int = 8
    lg: int = 12
    xl: int = 16
    full: int = 9999


@dataclass
class Shadow:
    """阴影系统"""
    none: str = "none"
    sm: str = "0 1px 2px rgba(0, 0, 0, 0.05)"
    md: str = "0 4px 6px rgba(0, 0, 0, 0.1)"
    lg: str = "0 10px 15px rgba(0, 0, 0, 0.1)"
    xl: str = "0 20px 25px rgba(0, 0, 0, 0.1)"


class Theme(ABC):
    """主题抽象基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.colors = self._create_color_scheme()
        self.typography = self._create_typography()
        self.spacing = self._create_spacing()
        self.border_radius = self._create_border_radius()
        self.shadow = self._create_shadow()
    
    @abstractmethod
    def _create_color_scheme(self) -> ColorScheme:
        """创建颜色方案"""
        pass
    
    def _create_typography(self) -> Typography:
        """创建字体排版"""
        return Typography()
    
    def _create_spacing(self) -> Spacing:
        """创建间距系统"""
        return Spacing()
    
    def _create_border_radius(self) -> BorderRadius:
        """创建圆角系统"""
        return BorderRadius()
    
    def _create_shadow(self) -> Shadow:
        """创建阴影系统"""
        return Shadow()
    
    def get_stylesheet(self) -> str:
        """获取样式表"""
        return self._generate_stylesheet()
    
    def _generate_stylesheet(self) -> str:
        """生成样式表"""
        return f"""
        /* 全局样式 */
        QWidget {{
            font-family: {self.typography.font_family};
            font-size: {self.typography.font_size_md}px;
            color: {self.colors.text_primary};
            background-color: {self.colors.background};
        }}
        
        /* 主窗口 */
        QMainWindow {{
            background-color: {self.colors.background};
        }}
        
        /* 按钮样式 */
        QPushButton {{
            background-color: {self.colors.primary};
            color: white;
            border: none;
            border-radius: {self.border_radius.md}px;
            padding: {self.spacing.sm}px {self.spacing.md}px;
            font-size: {self.typography.font_size_md}px;
            font-weight: 500;
        }}
        
        QPushButton:hover {{
            background-color: {self._darken_color(self.colors.primary, 0.1)};
        }}
        
        QPushButton:pressed {{
            background-color: {self._darken_color(self.colors.primary, 0.2)};
        }}
        
        QPushButton:disabled {{
            background-color: {self.colors.disabled};
            color: {self.colors.text_disabled};
        }}
        
        /* 次要按钮 */
        QPushButton[class="secondary"] {{
            background-color: {self.colors.surface};
            color: {self.colors.text_primary};
            border: 1px solid {self.colors.border};
        }}
        
        QPushButton[class="secondary"]:hover {{
            background-color: {self.colors.hover};
        }}
        
        /* 输入框样式 */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background-color: {self.colors.card};
            border: 1px solid {self.colors.border};
            border-radius: {self.border_radius.sm}px;
            padding: {self.spacing.sm}px;
            font-size: {self.typography.font_size_md}px;
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {self.colors.primary};
            outline: none;
        }}
        
        /* 标签样式 */
        QLabel {{
            color: {self.colors.text_primary};
            background-color: transparent;
        }}
        
        QLabel[class="secondary"] {{
            color: {self.colors.text_secondary};
        }}
        
        QLabel[class="title"] {{
            font-size: {self.typography.font_size_xl}px;
            font-weight: bold;
        }}
        
        QLabel[class="subtitle"] {{
            font-size: {self.typography.font_size_lg}px;
            font-weight: 500;
        }}
        
        /* 卡片样式 */
        QFrame[class="card"] {{
            background-color: {self.colors.card};
            border: 1px solid {self.colors.border};
            border-radius: {self.border_radius.md}px;
            padding: {self.spacing.md}px;
        }}
        
        /* 分隔线 */
        QFrame[frameShape="4"], QFrame[frameShape="5"] {{
            color: {self.colors.divider};
            background-color: {self.colors.divider};
        }}
        
        /* 滚动条样式 */
        QScrollBar:vertical {{
            background-color: {self.colors.surface};
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {self.colors.border};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {self.colors.text_secondary};
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}
        
        /* 菜单样式 */
        QMenuBar {{
            background-color: {self.colors.surface};
            border-bottom: 1px solid {self.colors.border};
        }}
        
        QMenuBar::item {{
            padding: {self.spacing.sm}px {self.spacing.md}px;
            background-color: transparent;
        }}
        
        QMenuBar::item:selected {{
            background-color: {self.colors.hover};
        }}
        
        QMenu {{
            background-color: {self.colors.card};
            border: 1px solid {self.colors.border};
            border-radius: {self.border_radius.sm}px;
        }}
        
        QMenu::item {{
            padding: {self.spacing.sm}px {self.spacing.md}px;
        }}
        
        QMenu::item:selected {{
            background-color: {self.colors.selected};
        }}
        
        /* 工具栏样式 */
        QToolBar {{
            background-color: {self.colors.surface};
            border: none;
            spacing: {self.spacing.sm}px;
        }}
        
        QToolButton {{
            background-color: transparent;
            border: none;
            border-radius: {self.border_radius.sm}px;
            padding: {self.spacing.sm}px;
        }}
        
        QToolButton:hover {{
            background-color: {self.colors.hover};
        }}
        
        QToolButton:pressed {{
            background-color: {self.colors.selected};
        }}
        """
    
    def _darken_color(self, color: str, factor: float) -> str:
        """加深颜色"""
        # 简单的颜色加深实现
        if color.startswith('#'):
            color = color[1:]
        
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        
        r = max(0, int(r * (1 - factor)))
        g = max(0, int(g * (1 - factor)))
        b = max(0, int(b * (1 - factor)))
        
        return f"#{r:02x}{g:02x}{b:02x}"
    
    def _lighten_color(self, color: str, factor: float) -> str:
        """减淡颜色"""
        if color.startswith('#'):
            color = color[1:]
        
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        
        r = min(255, int(r + (255 - r) * factor))
        g = min(255, int(g + (255 - g) * factor))
        b = min(255, int(b + (255 - b) * factor))
        
        return f"#{r:02x}{g:02x}{b:02x}"


class BaseTheme(Theme):
    """基础主题实现"""
    
    def __init__(self, name: str = "base"):
        super().__init__(name)
    
    def _create_color_scheme(self) -> ColorScheme:
        """创建基础颜色方案"""
        return ColorScheme()
