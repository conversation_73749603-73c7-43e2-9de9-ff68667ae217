#!/usr/bin/env python3
"""最小化测试程序

测试最基本的PyQt6功能，排除复杂组件的影响。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_qt():
    """测试基本Qt功能"""
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
        from PyQt6.QtCore import Qt, QTimer
        
        print("正在创建Qt应用程序...")
        app = QApplication(sys.argv)
        
        print("正在创建主窗口...")
        window = QMainWindow()
        window.setWindowTitle("AI视频生成器 2.0 - 最小化测试")
        window.setGeometry(100, 100, 600, 400)
        
        # 创建中央组件
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        title_label = QLabel("AI视频生成器 2.0")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: #2196F3;
                margin: 30px;
            }
        """)
        layout.addWidget(title_label)
        
        status_label = QLabel("最小化测试 - 程序运行正常！")
        status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        status_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                color: #4CAF50;
                margin: 20px;
            }
        """)
        layout.addWidget(status_label)
        
        info_label = QLabel("""
        这是一个最小化测试窗口。
        
        如果您能看到这个窗口，说明：
        ✓ PyQt6 安装正确
        ✓ 基本GUI功能正常
        ✓ 程序可以正常启动
        
        窗口将在10秒后自动关闭。
        """)
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #666;
                margin: 20px;
                line-height: 1.5;
            }
        """)
        layout.addWidget(info_label)
        
        # 显示窗口
        print("正在显示窗口...")
        window.show()
        
        # 设置自动关闭定时器
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(10000)  # 10秒后关闭
        
        print("窗口已显示，10秒后自动关闭...")
        print("如果看不到窗口，请检查是否被其他窗口遮挡")
        
        # 运行应用程序
        result = app.exec()
        print(f"程序退出，返回码：{result}")
        return result
        
    except Exception as e:
        print(f"测试失败：{e}")
        import traceback
        traceback.print_exc()
        return 1


def main():
    """主函数"""
    print("AI视频生成器 2.0 - 最小化测试")
    print("=" * 50)
    
    try:
        return test_basic_qt()
    except KeyboardInterrupt:
        print("\n用户中断测试")
        return 0
    except Exception as e:
        print(f"测试异常：{e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
