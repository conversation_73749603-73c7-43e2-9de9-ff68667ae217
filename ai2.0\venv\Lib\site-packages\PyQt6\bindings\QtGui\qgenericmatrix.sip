// qgenericmatrix.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


// The implementation of QMatrix4x3.
class QMatrix4x3
{
%TypeHeaderCode
#include <qgenericmatrix.h>
%End

%PickleCode
        PYQT_FLOAT data[12];

        // We want the data in row-major order.
        sipCpp->copyDataTo(data);
    
        sipRes = Py_BuildValue("dddddddddddd",
                (double)data[0], (double)data[1], (double)data[2],
                (double)data[3], (double)data[4], (double)data[5],
                (double)data[6], (double)data[7], (double)data[8],
                (double)data[9], (double)data[10], (double)data[11]);
%End

public:
    QMatrix4x3();
    QMatrix4x3(const QMatrix4x3 &other);
    explicit QMatrix4x3(SIP_PYOBJECT values /TypeHint="Sequence[float]"/);
%MethodCode
        PYQT_FLOAT values[12];

        if ((sipError = qtgui_matrixDataFromSequence(a0, 12, values)) == sipErrorNone)
            sipCpp = new QMatrix4x3(values);
%End

    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        bool bad = false;
        int i;
        PyObject *m[12];
        PYQT_FLOAT data[12];

        // The raw data is in column-major order but we want row-major order.
        sipCpp->copyDataTo(data);

        for (i = 0; i < 12; ++i)
        {
            m[i] = PyFloat_FromDouble(data[i]);

            if (!m[i])
                bad = true;
        }

        if (!bad)
        {
            sipRes = PyUnicode_FromFormat("PyQt6.QtGui.QMatrix4x3("
                    "%R, %R, %R, "
                    "%R, %R, %R, "
                    "%R, %R, %R, "
                    "%R, %R, %R)",
                    m[0], m[1], m[2],
                    m[3], m[4], m[5],
                    m[6], m[7], m[8],
                    m[9], m[10], m[11]);
        }

        for (i = 0; i < 12; ++i)
            Py_XDECREF(m[i]);
%End

    SIP_PYLIST data() /TypeHint="List[float]"/;
%MethodCode
        sipError = qtgui_matrixDataAsList(12, sipCpp->constData(), &sipRes);
%End

    SIP_PYLIST copyDataTo() const /TypeHint="List[float]"/;
%MethodCode
        PYQT_FLOAT values[12];

        sipCpp->copyDataTo(values);
        sipError = qtgui_matrixDataAsList(12, values, &sipRes);
%End

    SIP_PYOBJECT __getitem__(SIP_PYOBJECT) const;
%MethodCode
        int row, column;

        if ((sipError = qtgui_matrixParseIndex(a0, 4, 3, &row, &column)) == sipErrorNone)
        {
            sipRes = PyFloat_FromDouble(sipCpp->operator()(row, column));

            if (!sipRes)
                sipError = sipErrorFail;
        }
%End

    void __setitem__(SIP_PYOBJECT, float);
%MethodCode
        int row, column;

        if ((sipError = qtgui_matrixParseIndex(a0, 4, 3, &row, &column)) == sipErrorNone)
            sipCpp->operator()(row, column) = a1;
%End

    bool isIdentity() const;
    void setToIdentity();

    void fill(float value);

    QMatrix3x4 transposed() const;

    QMatrix4x3 &operator+=(const QMatrix4x3 &);
    QMatrix4x3 &operator-=(const QMatrix4x3 &);
    QMatrix4x3 &operator*=(float);
    QMatrix4x3 &operator/=(float);

    bool operator==(const QMatrix4x3 &) const;
    bool operator!=(const QMatrix4x3 &) const;
};

QDataStream &operator<<(QDataStream &, const QMatrix4x3 &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QMatrix4x3 & /Constrained/) /ReleaseGIL/;
// The implementation of QMatrix4x2.
class QMatrix4x2
{
%TypeHeaderCode
#include <qgenericmatrix.h>
%End

%PickleCode
        PYQT_FLOAT data[8];

        // We want the data in row-major order.
        sipCpp->copyDataTo(data);

        sipRes = Py_BuildValue("dddddddd",
                (double)data[0], (double)data[1],
                (double)data[2], (double)data[3],
                (double)data[4], (double)data[5],
                (double)data[6], (double)data[7]);
%End

public:
    QMatrix4x2();
    QMatrix4x2(const QMatrix4x2 &other);
    explicit QMatrix4x2(SIP_PYOBJECT values /TypeHint="Sequence[float]"/);
%MethodCode
        PYQT_FLOAT values[8];

        if ((sipError = qtgui_matrixDataFromSequence(a0, 8, values)) == sipErrorNone)
            sipCpp = new QMatrix4x2(values);
%End

    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        bool bad = false;
        int i;
        PyObject *m[8];
        PYQT_FLOAT data[8];

        // The raw data is in column-major order but we want row-major order.
        sipCpp->copyDataTo(data);

        for (i = 0; i < 8; ++i)
        {
            m[i] = PyFloat_FromDouble(data[i]);

            if (!m[i])
                bad = true;
        }

        if (!bad)
        {
            sipRes = PyUnicode_FromFormat("PyQt6.QtGui.QMatrix4x2("
                    "%R, %R, "
                    "%R, %R, "
                    "%R, %R, "
                    "%R, %R)",
                    m[0], m[1],
                    m[2], m[3],
                    m[4], m[5],
                    m[6], m[7]);
        }

        for (i = 0; i < 8; ++i)
            Py_XDECREF(m[i]);
%End

    SIP_PYLIST data() /TypeHint="List[float]"/;
%MethodCode
        sipError = qtgui_matrixDataAsList(8, sipCpp->constData(), &sipRes);
%End

    SIP_PYLIST copyDataTo() const /TypeHint="List[float]"/;
%MethodCode
        PYQT_FLOAT values[8];

        sipCpp->copyDataTo(values);
        sipError = qtgui_matrixDataAsList(8, values, &sipRes);
%End

    SIP_PYOBJECT __getitem__(SIP_PYOBJECT) const;
%MethodCode
        int row, column;

        if ((sipError = qtgui_matrixParseIndex(a0, 4, 2, &row, &column)) == sipErrorNone)
        {
            sipRes = PyFloat_FromDouble(sipCpp->operator()(row, column));

            if (!sipRes)
                sipError = sipErrorFail;
        }
%End

    void __setitem__(SIP_PYOBJECT, float);
%MethodCode
        int row, column;

        if ((sipError = qtgui_matrixParseIndex(a0, 4, 2, &row, &column)) == sipErrorNone)
            sipCpp->operator()(row, column) = a1;
%End

    bool isIdentity() const;
    void setToIdentity();

    void fill(float value);
        
    QMatrix2x4 transposed() const;

    QMatrix4x2 &operator+=(const QMatrix4x2 &);
    QMatrix4x2 &operator-=(const QMatrix4x2 &);
    QMatrix4x2 &operator*=(float);
    QMatrix4x2 &operator/=(float);

    bool operator==(const QMatrix4x2 &) const;
    bool operator!=(const QMatrix4x2 &) const;
};

QDataStream &operator<<(QDataStream &, const QMatrix4x2 &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QMatrix4x2 & /Constrained/) /ReleaseGIL/;
// The implementation of QMatrix3x4.
class QMatrix3x4
{
%TypeHeaderCode
#include <qgenericmatrix.h>
%End

%PickleCode
        PYQT_FLOAT data[12];

        // We want the data in row-major order.
        sipCpp->copyDataTo(data);

        sipRes = Py_BuildValue("dddddddddddd",
                (double)data[0], (double)data[1], (double)data[2],
                (double)data[3], (double)data[4], (double)data[5],
                (double)data[6], (double)data[7], (double)data[8],
                (double)data[9], (double)data[10], (double)data[11]);
%End

public:
    QMatrix3x4();
    QMatrix3x4(const QMatrix3x4 &other);
    explicit QMatrix3x4(SIP_PYOBJECT values /TypeHint="Sequence[float]"/);
%MethodCode
        PYQT_FLOAT values[12];

        if ((sipError = qtgui_matrixDataFromSequence(a0, 12, values)) == sipErrorNone)
            sipCpp = new QMatrix3x4(values);
%End

    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        bool bad = false;
        int i;
        PyObject *m[12];
        PYQT_FLOAT data[12];

        // The raw data is in column-major order but we want row-major order.
        sipCpp->copyDataTo(data);

        for (i = 0; i < 12; ++i)
        {
            m[i] = PyFloat_FromDouble(data[i]);

            if (!m[i])
                bad = true;
        }

        if (!bad)
        {
            sipRes = PyUnicode_FromFormat("PyQt6.QtGui.QMatrix3x4("
                    "%R, %R, %R, "
                    "%R, %R, %R, "
                    "%R, %R, %R, "
                    "%R, %R, %R)",
                    m[0], m[1], m[2],
                    m[3], m[4], m[5],
                    m[6], m[7], m[8],
                    m[9], m[10], m[11]);
        }

        for (i = 0; i < 12; ++i)
            Py_XDECREF(m[i]);
%End

    SIP_PYLIST data() /TypeHint="List[float]"/;
%MethodCode
        sipError = qtgui_matrixDataAsList(12, sipCpp->constData(), &sipRes);
%End

    SIP_PYLIST copyDataTo() const /TypeHint="List[float]"/;
%MethodCode
        PYQT_FLOAT values[12];

        sipCpp->copyDataTo(values);
        sipError = qtgui_matrixDataAsList(12, values, &sipRes);
%End

    SIP_PYOBJECT __getitem__(SIP_PYOBJECT) const;
%MethodCode
        int row, column;

        if ((sipError = qtgui_matrixParseIndex(a0, 3, 4, &row, &column)) == sipErrorNone)
        {
            sipRes = PyFloat_FromDouble(sipCpp->operator()(row, column));

            if (!sipRes)
                sipError = sipErrorFail;
        }
%End
        
    void __setitem__(SIP_PYOBJECT, float);
%MethodCode
        int row, column;

        if ((sipError = qtgui_matrixParseIndex(a0, 3, 4, &row, &column)) == sipErrorNone)
            sipCpp->operator()(row, column) = a1;
%End

    bool isIdentity() const;
    void setToIdentity();

    void fill(float value);
        
    QMatrix4x3 transposed() const;

    QMatrix3x4 &operator+=(const QMatrix3x4 &);
    QMatrix3x4 &operator-=(const QMatrix3x4 &);
    QMatrix3x4 &operator*=(float);
    QMatrix3x4 &operator/=(float);
    
    bool operator==(const QMatrix3x4 &) const;
    bool operator!=(const QMatrix3x4 &) const;
};

QDataStream &operator<<(QDataStream &, const QMatrix3x4 &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QMatrix3x4 & /Constrained/) /ReleaseGIL/;
// The implementation of QMatrix3x3.
class QMatrix3x3
{
%TypeHeaderCode
#include <qgenericmatrix.h>
%End

%PickleCode
        PYQT_FLOAT data[9];

        // We want the data in row-major order.
        sipCpp->copyDataTo(data);

        sipRes = Py_BuildValue("ddddddddd",
                (double)data[0], (double)data[1], (double)data[2],
                (double)data[3], (double)data[4], (double)data[5],
                (double)data[6], (double)data[7], (double)data[8]);
%End

public:
    QMatrix3x3();
    QMatrix3x3(const QMatrix3x3 &other);
    explicit QMatrix3x3(SIP_PYOBJECT values /TypeHint="Sequence[float]"/);
%MethodCode
        PYQT_FLOAT values[9];

        if ((sipError = qtgui_matrixDataFromSequence(a0, 9, values)) == sipErrorNone)
            sipCpp = new QMatrix3x3(values);
%End

    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        bool bad = false;
        int i;
        PyObject *m[9];
        PYQT_FLOAT data[9];

        // The raw data is in column-major order but we want row-major order.
        sipCpp->copyDataTo(data);

        for (i = 0; i < 9; ++i)
        {
            m[i] = PyFloat_FromDouble(data[i]);

            if (!m[i])
                bad = true;
        }

        if (!bad)
        {
            sipRes = PyUnicode_FromFormat("PyQt6.QtGui.QMatrix3x3("
                    "%R, %R, %R, "
                    "%R, %R, %R, "
                    "%R, %R, %R)",
                    m[0], m[1], m[2],
                    m[3], m[4], m[5],
                    m[6], m[7], m[8]);
        }

        for (i = 0; i < 9; ++i)
            Py_XDECREF(m[i]);
%End

    SIP_PYLIST data() /TypeHint="List[float]"/;
%MethodCode
        sipError = qtgui_matrixDataAsList(9, sipCpp->constData(), &sipRes);
%End

    SIP_PYLIST copyDataTo() const /TypeHint="List[float]"/;
%MethodCode
        PYQT_FLOAT values[9];

        sipCpp->copyDataTo(values);
        sipError = qtgui_matrixDataAsList(9, values, &sipRes);
%End

    SIP_PYOBJECT __getitem__(SIP_PYOBJECT) const;
%MethodCode
        int row, column;

        if ((sipError = qtgui_matrixParseIndex(a0, 3, 3, &row, &column)) == sipErrorNone)
        {
            sipRes = PyFloat_FromDouble(sipCpp->operator()(row, column));

            if (!sipRes)
                sipError = sipErrorFail;
        }
%End
        
    void __setitem__(SIP_PYOBJECT, float);
%MethodCode
        int row, column;

        if ((sipError = qtgui_matrixParseIndex(a0, 3, 3, &row, &column)) == sipErrorNone)
            sipCpp->operator()(row, column) = a1;
%End

    bool isIdentity() const;
    void setToIdentity();

    void fill(float value);

    QMatrix3x3 transposed() const;

    QMatrix3x3 &operator+=(const QMatrix3x3 &);
    QMatrix3x3 &operator-=(const QMatrix3x3 &);
    QMatrix3x3 &operator*=(float);
    QMatrix3x3 &operator/=(float);

    bool operator==(const QMatrix3x3 &) const;
    bool operator!=(const QMatrix3x3 &) const;
};

QDataStream &operator<<(QDataStream &, const QMatrix3x3 &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QMatrix3x3 & /Constrained/) /ReleaseGIL/;
// The implementation of QMatrix3x2.
class QMatrix3x2
{
%TypeHeaderCode
#include <qgenericmatrix.h>
%End

%PickleCode
        PYQT_FLOAT data[6];

        // We want the data in row-major order.
        sipCpp->copyDataTo(data);

        sipRes = Py_BuildValue("dddddd",
                (double)data[0], (double)data[1],
                (double)data[2], (double)data[3],
                (double)data[4], (double)data[5]);
%End

public:
    QMatrix3x2();
    QMatrix3x2(const QMatrix3x2 &other);
    explicit QMatrix3x2(SIP_PYOBJECT values /TypeHint="Sequence[float]"/);
%MethodCode
        PYQT_FLOAT values[6];

        if ((sipError = qtgui_matrixDataFromSequence(a0, 6, values)) == sipErrorNone)
            sipCpp = new QMatrix3x2(values);
%End

    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        bool bad = false;
        int i;
        PyObject *m[6];
        PYQT_FLOAT data[6];

        // The raw data is in column-major order but we want row-major order.
        sipCpp->copyDataTo(data);

        for (i = 0; i < 6; ++i)
        {
            m[i] = PyFloat_FromDouble(data[i]);

            if (!m[i])
                bad = true;
        }

        if (!bad)
        {
            sipRes = PyUnicode_FromFormat("PyQt6.QtGui.QMatrix3x2("
                    "%R, %R, "
                    "%R, %R, "
                    "%R, %R)",
                    m[0], m[1],
                    m[2], m[3],
                    m[4], m[5]);
        }

        for (i = 0; i < 6; ++i)
            Py_XDECREF(m[i]);
%End

    SIP_PYLIST data() /TypeHint="List[float]"/;
%MethodCode
        sipError = qtgui_matrixDataAsList(6, sipCpp->constData(), &sipRes);
%End

    SIP_PYLIST copyDataTo() const /TypeHint="List[float]"/;
%MethodCode
        PYQT_FLOAT values[6];

        sipCpp->copyDataTo(values);
        sipError = qtgui_matrixDataAsList(6, values, &sipRes);
%End

    SIP_PYOBJECT __getitem__(SIP_PYOBJECT) const;
%MethodCode
        int row, column;

        if ((sipError = qtgui_matrixParseIndex(a0, 3, 2, &row, &column)) == sipErrorNone)
        {
            sipRes = PyFloat_FromDouble(sipCpp->operator()(row, column));

            if (!sipRes)
                sipError = sipErrorFail;
        }
%End

    void __setitem__(SIP_PYOBJECT, float);
%MethodCode
        int row, column;

        if ((sipError = qtgui_matrixParseIndex(a0, 3, 2, &row, &column)) == sipErrorNone)
            sipCpp->operator()(row, column) = a1;
%End

    bool isIdentity() const;
    void setToIdentity();

    void fill(float value);

    QMatrix2x3 transposed() const;

    QMatrix3x2 &operator+=(const QMatrix3x2 &);
    QMatrix3x2 &operator-=(const QMatrix3x2 &);
    QMatrix3x2 &operator*=(float);
    QMatrix3x2 &operator/=(float);

    bool operator==(const QMatrix3x2 &) const;
    bool operator!=(const QMatrix3x2 &) const;
};

QDataStream &operator<<(QDataStream &, const QMatrix3x2 &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QMatrix3x2 & /Constrained/) /ReleaseGIL/;
// The implementation of QMatrix2x4.
class QMatrix2x4
{
%TypeHeaderCode
#include <qgenericmatrix.h>
%End

%PickleCode
        PYQT_FLOAT data[8];

        // We want the data in row-major order.
        sipCpp->copyDataTo(data);

        sipRes = Py_BuildValue("dddddddd",
                (double)data[0], (double)data[1], (double)data[2], (double)data[3],
                (double)data[4], (double)data[5], (double)data[6], (double)data[7]);
%End

public:
    QMatrix2x4();
    QMatrix2x4(const QMatrix2x4 &other);
    explicit QMatrix2x4(SIP_PYOBJECT values /TypeHint="Sequence[float]"/);
%MethodCode
        PYQT_FLOAT values[8];

        if ((sipError = qtgui_matrixDataFromSequence(a0, 8, values)) == sipErrorNone)
            sipCpp = new QMatrix2x4(values);
%End

    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        bool bad = false;
        int i;
        PyObject *m[8];
        PYQT_FLOAT data[8];

        // The raw data is in column-major order but we want row-major order.
        sipCpp->copyDataTo(data);

        for (i = 0; i < 8; ++i)
        {
            m[i] = PyFloat_FromDouble(data[i]);

            if (!m[i])
                bad = true;
        }

        if (!bad)
        {
            sipRes = PyUnicode_FromFormat("PyQt6.QtGui.QMatrix2x4("
                    "%R, %R, %R, %R, "
                    "%R, %R, %R, %R)",
                    m[0], m[1], m[2], m[3],
                    m[4], m[5], m[6], m[7]);
        }

        for (i = 0; i < 8; ++i)
            Py_XDECREF(m[i]);
%End

    SIP_PYLIST data() /TypeHint="List[float]"/;
%MethodCode
        sipError = qtgui_matrixDataAsList(8, sipCpp->constData(), &sipRes);
%End

    SIP_PYLIST copyDataTo() const /TypeHint="List[float]"/;
%MethodCode
        PYQT_FLOAT values[8];

        sipCpp->copyDataTo(values);
        sipError = qtgui_matrixDataAsList(8, values, &sipRes);
%End

    SIP_PYOBJECT __getitem__(SIP_PYOBJECT) const;
%MethodCode
        int row, column;

        if ((sipError = qtgui_matrixParseIndex(a0, 2, 4, &row, &column)) == sipErrorNone)
        {
            sipRes = PyFloat_FromDouble(sipCpp->operator()(row, column));

            if (!sipRes)
                sipError = sipErrorFail;
        }
%End
        
    void __setitem__(SIP_PYOBJECT, float);
%MethodCode
        int row, column;

        if ((sipError = qtgui_matrixParseIndex(a0, 2, 4, &row, &column)) == sipErrorNone)
            sipCpp->operator()(row, column) = a1;
%End

    bool isIdentity() const;
    void setToIdentity();

    void fill(float value);

    QMatrix4x2 transposed() const;

    QMatrix2x4 &operator+=(const QMatrix2x4 &);
    QMatrix2x4 &operator-=(const QMatrix2x4 &);
    QMatrix2x4 &operator*=(float);
    QMatrix2x4 &operator/=(float);

    bool operator==(const QMatrix2x4 &) const;
    bool operator!=(const QMatrix2x4 &) const;
};

QDataStream &operator<<(QDataStream &, const QMatrix2x4 &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QMatrix2x4 & /Constrained/) /ReleaseGIL/;
// The implementation of QMatrix2x3.
class QMatrix2x3
{
%TypeHeaderCode
#include <qgenericmatrix.h>
%End

%PickleCode
        PYQT_FLOAT data[6];

        // We want the data in row-major order.
        sipCpp->copyDataTo(data);

        sipRes = Py_BuildValue("dddddd",
                (double)data[0], (double)data[1], (double)data[2],
                (double)data[3], (double)data[4], (double)data[5]);
%End

public:
    QMatrix2x3();
    QMatrix2x3(const QMatrix2x3 &other);
    explicit QMatrix2x3(SIP_PYOBJECT values /TypeHint="Sequence[float]"/);
%MethodCode
        PYQT_FLOAT values[6];

        if ((sipError = qtgui_matrixDataFromSequence(a0, 6, values)) == sipErrorNone)
            sipCpp = new QMatrix2x3(values);
%End

    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        bool bad = false;
        int i;
        PyObject *m[6];
        PYQT_FLOAT data[6];

        // The raw data is in column-major order but we want row-major order.
        sipCpp->copyDataTo(data);

        for (i = 0; i < 6; ++i)
        {
            m[i] = PyFloat_FromDouble(data[i]);

            if (!m[i])
                bad = true;
        }

        if (!bad)
        {
            sipRes = PyUnicode_FromFormat("PyQt6.QtGui.QMatrix2x3("
                    "%R, %R, %R, "
                    "%R, %R, %R)",
                    m[0], m[1], m[2],
                    m[3], m[4], m[5]);
        }

        for (i = 0; i < 6; ++i)
            Py_XDECREF(m[i]);
%End

    SIP_PYLIST data() /TypeHint="List[float]"/;
%MethodCode
        sipError = qtgui_matrixDataAsList(6, sipCpp->constData(), &sipRes);
%End

    SIP_PYLIST copyDataTo() const /TypeHint="List[float]"/;
%MethodCode
        PYQT_FLOAT values[6];

        sipCpp->copyDataTo(values);
        sipError = qtgui_matrixDataAsList(6, values, &sipRes);
%End

    SIP_PYOBJECT __getitem__(SIP_PYOBJECT) const;
%MethodCode
        int row, column;

        if ((sipError = qtgui_matrixParseIndex(a0, 2, 3, &row, &column)) == sipErrorNone)
        {
            sipRes = PyFloat_FromDouble(sipCpp->operator()(row, column));

            if (!sipRes)
                sipError = sipErrorFail;
        }
%End
        
    void __setitem__(SIP_PYOBJECT, float);
%MethodCode
        int row, column;

        if ((sipError = qtgui_matrixParseIndex(a0, 2, 3, &row, &column)) == sipErrorNone)
            sipCpp->operator()(row, column) = a1;
%End

    bool isIdentity() const;
    void setToIdentity();

    void fill(float value);

    QMatrix3x2 transposed() const;

    QMatrix2x3 &operator+=(const QMatrix2x3 &);
    QMatrix2x3 &operator-=(const QMatrix2x3 &);
    QMatrix2x3 &operator*=(float);
    QMatrix2x3 &operator/=(float);

    bool operator==(const QMatrix2x3 &) const;
    bool operator!=(const QMatrix2x3 &) const;
};

QDataStream &operator<<(QDataStream &, const QMatrix2x3 &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QMatrix2x3 & /Constrained/) /ReleaseGIL/;
// The implementation of QMatrix2x2.
class QMatrix2x2
{
%TypeHeaderCode
#include <qgenericmatrix.h>
%End

%PickleCode
        PYQT_FLOAT data[4];

        // We want the data in row-major order.
        sipCpp->copyDataTo(data);

        sipRes = Py_BuildValue("dddd",
                (double)data[0], (double)data[1],
                (double)data[2], (double)data[3]);
%End

public:
    QMatrix2x2();
    QMatrix2x2(const QMatrix2x2 &other);
    explicit QMatrix2x2(SIP_PYOBJECT values /TypeHint="Sequence[float]"/);
%MethodCode
        PYQT_FLOAT values[4];

        if ((sipError = qtgui_matrixDataFromSequence(a0, 4, values)) == sipErrorNone)
            sipCpp = new QMatrix2x2(values);
%End

    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        bool bad = false;
        int i;
        PyObject *m[4];
        PYQT_FLOAT data[4];

        // The raw data is in column-major order but we want row-major order.
        sipCpp->copyDataTo(data);

        for (i = 0; i < 4; ++i)
        {
            m[i] = PyFloat_FromDouble(data[i]);

            if (!m[i])
                bad = true;
        }

        if (!bad)
        {
            sipRes = PyUnicode_FromFormat("PyQt6.QtGui.QMatrix2x2("
                    "%R, %R, "
                    "%R, %R)",
                    m[0], m[1],
                    m[2], m[3]);
        }

        for (i = 0; i < 4; ++i)
            Py_XDECREF(m[i]);
%End

    SIP_PYLIST data() /TypeHint="List[float]"/;
%MethodCode
        sipError = qtgui_matrixDataAsList(4, sipCpp->constData(), &sipRes);
%End

    SIP_PYLIST copyDataTo() const /TypeHint="List[float]"/;
%MethodCode
        PYQT_FLOAT values[4];

        sipCpp->copyDataTo(values);
        sipError = qtgui_matrixDataAsList(4, values, &sipRes);
%End

    SIP_PYOBJECT __getitem__(SIP_PYOBJECT) const;
%MethodCode
        int row, column;

        if ((sipError = qtgui_matrixParseIndex(a0, 2, 2, &row, &column)) == sipErrorNone)
        {
            sipRes = PyFloat_FromDouble(sipCpp->operator()(row, column));

            if (!sipRes)
                sipError = sipErrorFail;
        }
%End
        
    void __setitem__(SIP_PYOBJECT, float);
%MethodCode
        int row, column;

        if ((sipError = qtgui_matrixParseIndex(a0, 2, 2, &row, &column)) == sipErrorNone)
            sipCpp->operator()(row, column) = a1;
%End

    bool isIdentity() const;
    void setToIdentity();

    void fill(float value);

    QMatrix2x2 transposed() const;

    QMatrix2x2 &operator+=(const QMatrix2x2 &);
    QMatrix2x2 &operator-=(const QMatrix2x2 &);
    QMatrix2x2 &operator*=(float);
    QMatrix2x2 &operator/=(float);

    bool operator==(const QMatrix2x2 &) const;
    bool operator!=(const QMatrix2x2 &) const;
};

QDataStream &operator<<(QDataStream &, const QMatrix2x2 &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QMatrix2x2 & /Constrained/) /ReleaseGIL/;
