"""浅色主题

实现浅色主题的颜色方案和样式。
"""

from .base_theme import Theme, ColorScheme


class LightTheme(Theme):
    """浅色主题"""
    
    def __init__(self):
        super().__init__("light")
    
    def _create_color_scheme(self) -> ColorScheme:
        """创建浅色主题颜色方案"""
        return ColorScheme(
            # 主色调
            primary="#2196F3",
            secondary="#FFC107", 
            success="#4CAF50",
            warning="#FF9800",
            error="#F44336",
            info="#2196F3",
            
            # 背景色
            background="#FFFFFF",
            surface="#F8F9FA",
            card="#FFFFFF",
            
            # 文本色
            text_primary="#212121",
            text_secondary="#757575",
            text_disabled="#BDBDBD",
            
            # 边框色
            border="#E0E0E0",
            divider="#E0E0E0",
            
            # 状态色
            hover="#F5F5F5",
            selected="#E3F2FD",
            disabled="#F5F5F5"
        )
