"""浅色主题

实现浅色主题的颜色方案和样式。
"""

from .base_theme import Theme, ColorScheme


class LightTheme(Theme):
    """浅色主题"""
    
    def __init__(self):
        super().__init__("light")
    
    def _create_color_scheme(self) -> ColorScheme:
        """创建浅色主题颜色方案"""
        return ColorScheme(
            # 主色调 - 使用更现代的蓝色
            primary="#0366d6",
            secondary="#6f42c1",
            success="#28a745",
            warning="#ffc107",
            error="#dc3545",
            info="#17a2b8",

            # 背景色 - 更柔和的背景
            background="#fafbfc",
            surface="#ffffff",
            card="#ffffff",

            # 文本色 - 更好的对比度
            text_primary="#24292e",
            text_secondary="#586069",
            text_disabled="#959da5",

            # 边框色 - 更细腻的边框
            border="#d1d5da",
            divider="#e1e4e8",

            # 状态色 - 更明显的交互反馈
            hover="#f6f8fa",
            selected="#f1f8ff",
            disabled="#f6f8fa"
        )
