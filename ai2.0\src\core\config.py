"""配置管理系统

提供统一的配置管理功能，支持多环境配置和动态更新。
"""

from typing import Dict, Any, Optional, Union
from pathlib import Path
import yaml
import os
from dataclasses import dataclass, field


@dataclass
class DatabaseConfig:
    """数据库配置"""
    url: str = "sqlite:///data/app.db"
    echo: bool = False
    pool_size: int = 5
    max_overflow: int = 10


@dataclass
class CacheConfig:
    """缓存配置"""
    type: str = "memory"  # memory, redis
    max_size: int = 1000
    ttl: int = 3600
    url: Optional[str] = None
    max_connections: Optional[int] = None


@dataclass
class ServiceConfig:
    """服务配置"""
    default_provider: str
    timeout: int = 30
    max_retries: int = 3


@dataclass
class ServicesConfig:
    """AI服务配置"""
    llm: ServiceConfig = field(default_factory=lambda: ServiceConfig("openai"))
    image: ServiceConfig = field(default_factory=lambda: ServiceConfig("pollinations"))
    video: ServiceConfig = field(default_factory=lambda: ServiceConfig("cogvideox"))
    voice: ServiceConfig = field(default_factory=lambda: ServiceConfig("edge-tts"))


@dataclass
class UIConfig:
    """UI配置"""
    theme: str = "light"
    language: str = "zh-CN"
    window_width: int = 1600
    window_height: int = 1000
    min_width: int = 1200
    min_height: int = 800


@dataclass
class ProjectConfig:
    """项目配置"""
    default_style: str = "cinematic"
    default_language: str = "zh-CN"
    auto_save: bool = True
    auto_save_interval: int = 300


@dataclass
class PerformanceConfig:
    """性能配置"""
    max_concurrent_tasks: int = 5
    image_cache_size: int = 100
    video_cache_size: int = 10


@dataclass
class AppConfig:
    """应用配置"""
    name: str = "AI视频生成器"
    version: str = "2.0.0"
    debug: bool = False
    log_level: str = "INFO"


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Optional[Union[str, Path]] = None):
        self.config_dir = Path(config_dir) if config_dir else Path("config")
        self.environment = os.getenv('APP_ENV', 'development')
        self._config: Dict[str, Any] = {}
        self._load_config()
        
        # 创建配置对象
        self.app = self._create_app_config()
        self.database = self._create_database_config()
        self.cache = self._create_cache_config()
        self.services = self._create_services_config()
        self.ui = self._create_ui_config()
        self.project = self._create_project_config()
        self.performance = self._create_performance_config()
    
    def _load_config(self) -> None:
        """加载配置文件"""
        # 加载基础配置
        base_config_path = self.config_dir / 'base.yaml'
        if base_config_path.exists():
            with open(base_config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f) or {}
        
        # 加载环境特定配置
        env_config_path = self.config_dir / 'environments' / f'{self.environment}.yaml'
        if env_config_path.exists():
            with open(env_config_path, 'r', encoding='utf-8') as f:
                env_config = yaml.safe_load(f) or {}
                self._merge_config(self._config, env_config)
    
    def _merge_config(self, base: Dict, override: Dict) -> None:
        """合并配置"""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值"""
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def _create_app_config(self) -> AppConfig:
        """创建应用配置"""
        app_data = self.get('app', {})
        return AppConfig(
            name=app_data.get('name', 'AI视频生成器'),
            version=app_data.get('version', '2.0.0'),
            debug=app_data.get('debug', False),
            log_level=app_data.get('log_level', 'INFO')
        )
    
    def _create_database_config(self) -> DatabaseConfig:
        """创建数据库配置"""
        db_data = self.get('database', {})
        return DatabaseConfig(
            url=db_data.get('url', 'sqlite:///data/app.db'),
            echo=db_data.get('echo', False),
            pool_size=db_data.get('pool_size', 5),
            max_overflow=db_data.get('max_overflow', 10)
        )
    
    def _create_cache_config(self) -> CacheConfig:
        """创建缓存配置"""
        cache_data = self.get('cache', {})
        return CacheConfig(
            type=cache_data.get('type', 'memory'),
            max_size=cache_data.get('max_size', 1000),
            ttl=cache_data.get('ttl', 3600),
            url=cache_data.get('url'),
            max_connections=cache_data.get('max_connections')
        )
    
    def _create_services_config(self) -> ServicesConfig:
        """创建服务配置"""
        services_data = self.get('services', {})
        
        llm_data = services_data.get('llm', {})
        image_data = services_data.get('image', {})
        video_data = services_data.get('video', {})
        voice_data = services_data.get('voice', {})
        
        return ServicesConfig(
            llm=ServiceConfig(
                default_provider=llm_data.get('default_provider', 'openai'),
                timeout=llm_data.get('timeout', 30),
                max_retries=llm_data.get('max_retries', 3)
            ),
            image=ServiceConfig(
                default_provider=image_data.get('default_provider', 'pollinations'),
                timeout=image_data.get('timeout', 60),
                max_retries=image_data.get('max_retries', 3)
            ),
            video=ServiceConfig(
                default_provider=video_data.get('default_provider', 'cogvideox'),
                timeout=video_data.get('timeout', 120),
                max_retries=video_data.get('max_retries', 2)
            ),
            voice=ServiceConfig(
                default_provider=voice_data.get('default_provider', 'edge-tts'),
                timeout=voice_data.get('timeout', 30),
                max_retries=voice_data.get('max_retries', 3)
            )
        )
    
    def _create_ui_config(self) -> UIConfig:
        """创建UI配置"""
        ui_data = self.get('ui', {})
        window_data = ui_data.get('window', {})
        
        return UIConfig(
            theme=ui_data.get('theme', 'light'),
            language=ui_data.get('language', 'zh-CN'),
            window_width=window_data.get('width', 1600),
            window_height=window_data.get('height', 1000),
            min_width=window_data.get('min_width', 1200),
            min_height=window_data.get('min_height', 800)
        )
    
    def _create_project_config(self) -> ProjectConfig:
        """创建项目配置"""
        project_data = self.get('project', {})
        return ProjectConfig(
            default_style=project_data.get('default_style', 'cinematic'),
            default_language=project_data.get('default_language', 'zh-CN'),
            auto_save=project_data.get('auto_save', True),
            auto_save_interval=project_data.get('auto_save_interval', 300)
        )
    
    def _create_performance_config(self) -> PerformanceConfig:
        """创建性能配置"""
        perf_data = self.get('performance', {})
        return PerformanceConfig(
            max_concurrent_tasks=perf_data.get('max_concurrent_tasks', 5),
            image_cache_size=perf_data.get('image_cache_size', 100),
            video_cache_size=perf_data.get('video_cache_size', 10)
        )
    
    def reload(self) -> None:
        """重新加载配置"""
        self._load_config()
        
        # 重新创建配置对象
        self.app = self._create_app_config()
        self.database = self._create_database_config()
        self.cache = self._create_cache_config()
        self.services = self._create_services_config()
        self.ui = self._create_ui_config()
        self.project = self._create_project_config()
        self.performance = self._create_performance_config()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self._config.copy()
