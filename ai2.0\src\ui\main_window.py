"""主窗口

应用程序的主窗口实现。
"""

from typing import Optional, Dict, Any
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QMenuBar, QToolBar, QStatusBar, QStackedWidget, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QAction, QIcon, QKeySequence

from src.ui.base import BaseWidget
from src.ui.themes import get_theme_manager
from src.ui.components import NavigationPanel, ContentArea, InfoPanel
from src.ui.components.modern_widgets import ModernCard, ModernButton, ModernLabel
from src.core.config import ConfigManager
from src.utils.logger import get_logger


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    window_closing = pyqtSignal()
    theme_changed = pyqtSignal(str)
    
    def __init__(self, config: ConfigManager):
        super().__init__()
        self.config = config
        self.logger = get_logger(__name__)
        self.theme_manager = get_theme_manager()
        
        # 组件引用
        self.navigation_panel: Optional[NavigationPanel] = None
        self.content_area: Optional[ContentArea] = None
        self.info_panel: Optional[InfoPanel] = None
        self.main_splitter: Optional[QSplitter] = None
        
        # 状态
        self._is_maximized = False
        self._normal_geometry = None
        
        self._setup_window()
        self._setup_ui()
        self._setup_menu_bar()
        self._setup_tool_bar()
        self._setup_status_bar()
        self._connect_signals()
        self._apply_theme()
    
    def _setup_window(self) -> None:
        """设置窗口属性"""
        # 窗口标题和图标
        self.setWindowTitle(self.config.app.name)
        
        # 窗口大小
        self.resize(
            self.config.ui.window_width,
            self.config.ui.window_height
        )
        self.setMinimumSize(
            self.config.ui.min_width,
            self.config.ui.min_height
        )
        
        # 窗口居中
        self._center_window()
    
    def _center_window(self) -> None:
        """窗口居中显示"""
        from PyQt6.QtGui import QGuiApplication
        screen = QGuiApplication.primaryScreen()
        if screen:
            screen_geometry = screen.availableGeometry()
            window_geometry = self.frameGeometry()
            center_point = screen_geometry.center()
            window_geometry.moveCenter(center_point)
            self.move(window_geometry.topLeft())
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建分割器
        self.main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(self.main_splitter)
        
        # 创建导航面板
        self.navigation_panel = NavigationPanel()
        self.navigation_panel.setFixedWidth(400)  # 设置为400px确保文字完整显示
        self.main_splitter.addWidget(self.navigation_panel)
        
        # 创建内容区域
        self.content_area = ContentArea()
        self.main_splitter.addWidget(self.content_area)
        
        # 创建信息面板
        self.info_panel = InfoPanel()
        self.info_panel.setFixedWidth(200)  # 减少宽度
        self.main_splitter.addWidget(self.info_panel)

        # 设置分割器比例 - 调整导航栏宽度为400px
        self.main_splitter.setSizes([400, 850, 200])
        self.main_splitter.setCollapsible(0, True)  # 导航面板可折叠
        self.main_splitter.setCollapsible(1, False) # 内容区域不可折叠
        self.main_splitter.setCollapsible(2, True)  # 信息面板可折叠
    
    def _setup_menu_bar(self) -> None:
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 新建项目
        new_action = QAction("新建项目(&N)", self)
        new_action.setShortcut(QKeySequence.StandardKey.New)
        new_action.setStatusTip("创建新项目")
        new_action.triggered.connect(self._on_new_project)
        file_menu.addAction(new_action)
        
        # 打开项目
        open_action = QAction("打开项目(&O)", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.setStatusTip("打开现有项目")
        open_action.triggered.connect(self._on_open_project)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # 保存项目
        save_action = QAction("保存项目(&S)", self)
        save_action.setShortcut(QKeySequence.StandardKey.Save)
        save_action.setStatusTip("保存当前项目")
        save_action.triggered.connect(self._on_save_project)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.setStatusTip("退出应用程序")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")
        
        # 撤销
        undo_action = QAction("撤销(&U)", self)
        undo_action.setShortcut(QKeySequence.StandardKey.Undo)
        undo_action.setStatusTip("撤销上一步操作")
        edit_menu.addAction(undo_action)
        
        # 重做
        redo_action = QAction("重做(&R)", self)
        redo_action.setShortcut(QKeySequence.StandardKey.Redo)
        redo_action.setStatusTip("重做上一步操作")
        edit_menu.addAction(redo_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")
        
        # 切换主题
        toggle_theme_action = QAction("切换主题(&T)", self)
        toggle_theme_action.setShortcut("Ctrl+T")
        toggle_theme_action.setStatusTip("在浅色和深色主题之间切换")
        toggle_theme_action.triggered.connect(self._on_toggle_theme)
        view_menu.addAction(toggle_theme_action)
        
        view_menu.addSeparator()
        
        # 显示/隐藏面板
        toggle_nav_action = QAction("显示/隐藏导航面板(&N)", self)
        toggle_nav_action.setShortcut("Ctrl+1")
        toggle_nav_action.triggered.connect(self._toggle_navigation_panel)
        view_menu.addAction(toggle_nav_action)
        
        toggle_info_action = QAction("显示/隐藏信息面板(&I)", self)
        toggle_info_action.setShortcut("Ctrl+2")
        toggle_info_action.triggered.connect(self._toggle_info_panel)
        view_menu.addAction(toggle_info_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.setStatusTip("关于AI视频生成器")
        about_action.triggered.connect(self._on_about)
        help_menu.addAction(about_action)
    
    def _setup_tool_bar(self) -> None:
        """设置工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setMovable(False)
        
        # 新建项目按钮
        new_action = QAction("新建", self)
        new_action.setStatusTip("创建新项目")
        new_action.triggered.connect(self._on_new_project)
        toolbar.addAction(new_action)
        
        # 打开项目按钮
        open_action = QAction("打开", self)
        open_action.setStatusTip("打开项目")
        open_action.triggered.connect(self._on_open_project)
        toolbar.addAction(open_action)
        
        # 保存项目按钮
        save_action = QAction("保存", self)
        save_action.setStatusTip("保存项目")
        save_action.triggered.connect(self._on_save_project)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # 生成分镜按钮
        generate_action = QAction("生成分镜", self)
        generate_action.setStatusTip("生成分镜")
        generate_action.triggered.connect(self._on_generate_storyboard)
        toolbar.addAction(generate_action)
    
    def _setup_status_bar(self) -> None:
        """设置状态栏"""
        status_bar = self.statusBar()
        status_bar.showMessage("就绪")
        
        # 添加永久状态信息
        self.theme_label = status_bar.addPermanentWidget(QFrame())
        self._update_theme_status()
    
    def _connect_signals(self) -> None:
        """连接信号"""
        # 主题变化信号
        self.theme_manager.theme_changed.connect(self._on_theme_changed)
        
        # 组件信号连接
        if self.navigation_panel:
            self.navigation_panel.page_changed.connect(self._on_page_changed)
        
        if self.content_area:
            self.content_area.status_changed.connect(self._on_status_changed)
    
    def _apply_theme(self) -> None:
        """应用主题"""
        current_theme = self.theme_manager.get_current_theme_name()
        self.theme_changed.emit(current_theme)
    
    def _update_theme_status(self) -> None:
        """更新主题状态显示"""
        current_theme = self.theme_manager.get_current_theme_name()
        theme_text = "深色主题" if current_theme == "dark" else "浅色主题"
        self.statusBar().showMessage(f"当前主题: {theme_text}", 2000)
    
    # 事件处理方法
    def _on_new_project(self) -> None:
        """新建项目"""
        self.logger.info("New project requested")
        if self.content_area:
            self.content_area.show_new_project_dialog()
    
    def _on_open_project(self) -> None:
        """打开项目"""
        self.logger.info("Open project requested")
        if self.content_area:
            self.content_area.show_open_project_dialog()
    
    def _on_save_project(self) -> None:
        """保存项目"""
        self.logger.info("Save project requested")
        if self.content_area:
            self.content_area.save_current_project()
    
    def _on_generate_storyboard(self) -> None:
        """生成分镜"""
        self.logger.info("Generate storyboard requested")
        if self.content_area:
            self.content_area.generate_storyboard()
    
    def _on_toggle_theme(self) -> None:
        """切换主题"""
        new_theme = self.theme_manager.toggle_theme()
        self.logger.info(f"Theme toggled to: {new_theme}")
    
    def _on_theme_changed(self, theme_name: str) -> None:
        """主题变化处理"""
        self._update_theme_status()
        self.theme_changed.emit(theme_name)
    
    def _on_page_changed(self, page_name: str) -> None:
        """页面变化处理"""
        self.logger.debug(f"Page changed to: {page_name}")
        if self.content_area:
            self.content_area.show_page(page_name)
    
    def _on_status_changed(self, message: str) -> None:
        """状态变化处理"""
        self.statusBar().showMessage(message)
    
    def _on_about(self) -> None:
        """关于对话框"""
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.about(
            self,
            "关于AI视频生成器",
            f"AI视频生成器 {self.config.app.version}\n\n"
            "基于现代化技术栈的AI视频生成工具\n"
            "支持智能分镜、图像生成、语音合成和视频制作"
        )
    
    def _toggle_navigation_panel(self) -> None:
        """切换导航面板显示"""
        if self.navigation_panel:
            self.navigation_panel.setVisible(not self.navigation_panel.isVisible())
    
    def _toggle_info_panel(self) -> None:
        """切换信息面板显示"""
        if self.info_panel:
            self.info_panel.setVisible(not self.info_panel.isVisible())
    
    def resizeEvent(self, event) -> None:
        """窗口尺寸变化事件"""
        super().resizeEvent(event)
        self._handle_responsive_layout()

    def _handle_responsive_layout(self) -> None:
        """处理响应式布局"""
        width = self.width()

        # 定义断点
        if width < 768:  # 小屏幕
            self._set_mobile_layout()
        elif width < 1200:  # 中等屏幕
            self._set_tablet_layout()
        else:  # 大屏幕
            self._set_desktop_layout()

    def _set_mobile_layout(self) -> None:
        """设置移动端布局"""
        if self.main_splitter:
            # 隐藏信息面板，导航面板变窄
            if self.info_panel:
                self.info_panel.setVisible(False)
            if self.navigation_panel:
                self.navigation_panel.setFixedWidth(60)

            # 调整分割器比例
            self.main_splitter.setSizes([60, self.width() - 60, 0])

    def _set_tablet_layout(self) -> None:
        """设置平板端布局"""
        if self.main_splitter:
            # 显示导航面板，隐藏信息面板
            if self.navigation_panel:
                self.navigation_panel.setVisible(True)
                self.navigation_panel.setFixedWidth(300)  # 平板也使用较宽的导航栏
            if self.info_panel:
                self.info_panel.setVisible(False)

            # 调整分割器比例
            self.main_splitter.setSizes([300, self.width() - 300, 0])

    def _set_desktop_layout(self) -> None:
        """设置桌面端布局"""
        if self.main_splitter:
            # 显示所有面板
            if self.navigation_panel:
                self.navigation_panel.setVisible(True)
                self.navigation_panel.setFixedWidth(400)  # 使用400px确保文字完整显示
            if self.info_panel:
                self.info_panel.setVisible(True)
                self.info_panel.setFixedWidth(200)  # 保持200px

            # 调整分割器比例
            content_width = self.width() - 400 - 200
            self.main_splitter.setSizes([400, content_width, 200])

    def closeEvent(self, event) -> None:
        """窗口关闭事件"""
        self.window_closing.emit()
        self.logger.info("Main window closing")
        event.accept()
