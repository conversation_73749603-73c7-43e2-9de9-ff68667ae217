{"6": {"inputs": {"text": "chibi style, kawaii, adorable anthropomorphic potted spinach character, big expressive eyes, happy smiling face, simple round green arms and legs, sitting pose in a white pot, clean and bright illustration, soft lighting, blurred kitchen background, wooden countertop", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["44", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码器"}}, "8": {"inputs": {"samples": ["13", 0], "vae": ["10", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "9": {"inputs": {"filename_prefix": "ComfyUI", "images": ["49", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "10": {"inputs": {"vae_name": "Flux\\flux-vae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "VAE加载器"}}, "13": {"inputs": {"noise": ["25", 0], "guider": ["22", 0], "sampler": ["16", 0], "sigmas": ["17", 0], "latent_image": ["27", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "自定义采样器(高级)"}}, "16": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "K采样器选择"}}, "17": {"inputs": {"scheduler": "simple", "steps": 25, "denoise": 1, "model": ["30", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "基础调度器"}}, "22": {"inputs": {"model": ["30", 0], "conditioning": ["26", 0]}, "class_type": "BasicGuider", "_meta": {"title": "基础引导"}}, "25": {"inputs": {"noise_seed": 864546076230679}, "class_type": "RandomNoise", "_meta": {"title": "随机噪波"}}, "26": {"inputs": {"guidance": 3.5, "conditioning": ["6", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导"}}, "27": {"inputs": {"width": 1024, "height": 1648, "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "空Latent_SD3"}}, "30": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": 1024, "height": 1648, "model": ["45", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "模型采样算法Flux"}}, "44": {"inputs": {"model_type": "flux", "text_encoder1": "t5xxl_fp8_e4m3fn.safetensors", "text_encoder2": "clip_l.safetensors", "t5_min_length": 512, "use_4bit_t5": "disable", "int4_model": "none"}, "class_type": "NunchakuTextEncoderLoader", "_meta": {"title": "Nunchaku Text Encoder Loader"}}, "45": {"inputs": {"model_path": "nunchaku-flux.1-dev", "cache_threshold": 0.05000000000000001, "attention": "nunchaku-fp16", "cpu_offload": "auto", "device_id": 0, "data_type": "bfloat16", "i2f_mode": "enabled"}, "class_type": "NunchakuFluxDiTLoader", "_meta": {"title": "Nunchaku FLUX DiT Loader"}}, "49": {"inputs": {"anything": ["8", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "清理GPU占用"}}}