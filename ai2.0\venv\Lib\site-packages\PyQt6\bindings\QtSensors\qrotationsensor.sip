// qrotationsensor.sip generated by MetaSIP
//
// This file is part of the QtSensors Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QRotationReading : public QSensorReading /NoDefaultCtors/
{
%TypeHeaderCode
#include <qrotationsensor.h>
%End

public:
    qreal x() const;
    qreal y() const;
    qreal z() const;
    void setFromEuler(qreal x, qreal y, qreal z);
};

%End
%If (Qt_6_2_0 -)

class QRotationFilter : public QSensorFilter
{
%TypeHeaderCode
#include <qrotationsensor.h>
%End

public:
    virtual bool filter(QRotationReading *reading) = 0;
};

%End
%If (Qt_6_2_0 -)

class QRotationSensor : public QSensor
{
%TypeHeaderCode
#include <qrotationsensor.h>
%End

public:
    explicit QRotationSensor(QObject *parent /TransferThis/ = 0);
    virtual ~QRotationSensor();
    QRotationReading *reading() const;
    bool hasZ() const;
    void setHasZ(bool hasZ);

signals:
    void hasZChanged(bool hasZ);
};

%End
