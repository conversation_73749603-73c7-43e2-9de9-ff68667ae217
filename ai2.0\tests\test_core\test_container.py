"""依赖注入容器测试"""

import pytest
from src.core.container import Container, ServiceLifetime


class TestService:
    """测试服务"""
    def __init__(self, value: str = "test"):
        self.value = value


class DependentService:
    """依赖服务"""
    def __init__(self, test_service: TestService):
        self.test_service = test_service


class TestContainer:
    """容器测试"""
    
    def test_register_and_get_singleton(self):
        """测试单例注册和获取"""
        container = Container()
        container.register_singleton(TestService)
        
        service1 = container.get(TestService)
        service2 = container.get(TestService)
        
        assert service1 is service2
        assert isinstance(service1, TestService)
    
    def test_register_and_get_transient(self):
        """测试瞬态注册和获取"""
        container = Container()
        container.register_transient(TestService)
        
        service1 = container.get(TestService)
        service2 = container.get(TestService)
        
        assert service1 is not service2
        assert isinstance(service1, TestService)
        assert isinstance(service2, TestService)
    
    def test_register_with_factory(self):
        """测试工厂方法注册"""
        container = Container()
        
        def create_service():
            return TestService("factory_created")
        
        container.register_singleton(TestService, factory=create_service)
        
        service = container.get(TestService)
        assert service.value == "factory_created"
    
    def test_dependency_injection(self):
        """测试依赖注入"""
        container = Container()
        container.register_singleton(TestService)
        container.register_transient(DependentService)
        
        dependent = container.get(DependentService)
        
        assert isinstance(dependent, DependentService)
        assert isinstance(dependent.test_service, TestService)
    
    def test_service_not_registered(self):
        """测试未注册服务"""
        container = Container()
        
        with pytest.raises(ValueError, match="Service TestService not registered"):
            container.get(TestService)
    
    def test_is_registered(self):
        """测试服务注册检查"""
        container = Container()
        
        assert not container.is_registered(TestService)
        
        container.register_singleton(TestService)
        
        assert container.is_registered(TestService)
    
    def test_scoped_services(self):
        """测试作用域服务"""
        container = Container()
        container.register_scoped(TestService)
        
        service1 = container.get(TestService)
        service2 = container.get(TestService)
        
        # 在同一作用域内应该是同一实例
        assert service1 is service2
        
        # 清除作用域后应该创建新实例
        container.clear_scoped()
        service3 = container.get(TestService)
        
        assert service1 is not service3
