# -*- coding: utf-8 -*-
"""
AI视频生成器2.0 - 主程序入口
集成MCP工具的完整AI视频生成解决方案
"""

import asyncio
import json
import logging
import os
import sys
import time
from typing import Dict, Any, List, Optional
from pathlib import Path
import argparse
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入核心模块
from src.workflow.video_generation_workflow import (
    VideoGenerationWorkflow, WorkflowConfig, WorkflowResult,
    create_video_generation_workflow, run_video_generation
)
from src.core.mcp_service_manager import MCPServiceManager
from src.services.llm_service import LLMServiceManager
from src.services.translation_service import TranslationServiceManager
from src.services.tts_service import TTSServiceManager
from src.services.image_service import ImageServiceManager
from src.services.video_service import VideoServiceManager
from src.services.social_media_service import SocialMediaServiceManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ai_video_generator.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class AIVideoGenerator:
    """AI视频生成器2.0主类"""
    
    def __init__(self, config_path: str = None):
        self.config_path = config_path or self._get_default_config_path()
        self.config = {}
        self.workflow = None
        self.is_initialized = False
        
        # 加载配置
        self._load_config()
    
    def _get_default_config_path(self) -> str:
        """获取默认配置路径"""
        # 优先使用原项目的配置文件
        original_config_dir = Path("f:/DAI/config")
        if original_config_dir.exists():
            return str(original_config_dir)
        
        # 使用新程序的配置目录
        new_config_dir = project_root / "config"
        new_config_dir.mkdir(exist_ok=True)
        return str(new_config_dir)
    
    def _load_config(self):
        """加载配置文件"""
        try:
            config_dir = Path(self.config_path)
            
            # 加载各个服务的配置
            self.config = {
                'llm': self._load_json_config(config_dir / "llm_config.json"),
                'translation': self._load_translation_config(config_dir),
                'tts': self._load_json_config(config_dir / "tts_config.json"),
                'image_generation': self._load_image_config(config_dir),
                'video_generation': self._load_video_config(config_dir),
                'social_media': self._load_social_media_config(config_dir)
            }
            
            logger.info(f"配置加载完成，配置目录: {config_dir}")
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            # 使用默认配置
            self.config = self._get_default_config()
    
    def _load_json_config(self, file_path: Path) -> Dict[str, Any]:
        """加载JSON配置文件"""
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"加载配置文件失败 {file_path}: {e}")
        return {}
    
    def _load_translation_config(self, config_dir: Path) -> Dict[str, Any]:
        """加载翻译配置"""
        # 尝试加载百度翻译配置
        baidu_config_file = config_dir / "baidu_translate_config.py"
        if baidu_config_file.exists():
            try:
                # 动态导入配置模块
                import importlib.util
                spec = importlib.util.spec_from_file_location("baidu_config", baidu_config_file)
                baidu_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(baidu_module)
                
                baidu_translate_config = getattr(baidu_module, 'BAIDU_TRANSLATE_CONFIG', {})
                
                return {
                    'routing_strategy': 'quality_first',
                    'clients': {
                        'baidu': {
                            'app_id': baidu_translate_config.get('app_id', ''),
                            'secret_key': baidu_translate_config.get('secret_key', ''),
                            'base_url': 'https://fanyi-api.baidu.com/api/trans/vip/translate'
                        }
                    }
                }
            except Exception as e:
                logger.warning(f"加载百度翻译配置失败: {e}")
        
        return {'routing_strategy': 'quality_first', 'clients': {}}
    
    def _load_image_config(self, config_dir: Path) -> Dict[str, Any]:
        """加载图像生成配置"""
        config_file = config_dir / "image_generation_config.py"
        if config_file.exists():
            try:
                import importlib.util
                spec = importlib.util.spec_from_file_location("image_config", config_file)
                image_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(image_module)
                
                image_config = getattr(image_module, 'IMAGE_GENERATION_CONFIG', {})
                return image_config
            except Exception as e:
                logger.warning(f"加载图像生成配置失败: {e}")
        
        return {'routing_strategy': 'quality_first', 'engines': {}}
    
    def _load_video_config(self, config_dir: Path) -> Dict[str, Any]:
        """加载视频生成配置"""
        config_file = config_dir / "video_generation_config.py"
        if config_file.exists():
            try:
                import importlib.util
                spec = importlib.util.spec_from_file_location("video_config", config_file)
                video_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(video_module)
                
                video_config = getattr(video_module, 'VIDEO_GENERATION_CONFIG', {})
                return video_config
            except Exception as e:
                logger.warning(f"加载视频生成配置失败: {e}")
        
        return {'routing_strategy': 'quality_first', 'engines': {}}
    
    def _load_social_media_config(self, config_dir: Path) -> Dict[str, Any]:
        """加载社交媒体配置"""
        # 社交媒体配置通常需要用户手动配置
        return {
            'platforms': {
                # 'youtube': {
                #     'client_id': '',
                #     'client_secret': '',
                #     'refresh_token': '',
                #     'api_key': ''
                # },
                # 'tiktok': {
                #     'client_key': '',
                #     'client_secret': '',
                #     'access_token': ''
                # }
            }
        }
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'llm': {
                'routing_strategy': 'quality_first',
                'clients': {}
            },
            'translation': {
                'routing_strategy': 'quality_first',
                'clients': {}
            },
            'tts': {
                'default_engine': 'edge',
                'engines': {
                    'edge_tts': {
                        'voice': 'zh-CN-XiaoxiaoNeural'
                    }
                }
            },
            'image_generation': {
                'routing_strategy': 'quality_first',
                'engines': {
                    'pollinations': {
                        'base_url': 'https://image.pollinations.ai/prompt'
                    }
                }
            },
            'video_generation': {
                'routing_strategy': 'quality_first',
                'engines': {}
            },
            'social_media': {
                'platforms': {}
            }
        }
    
    async def initialize(self) -> bool:
        """初始化AI视频生成器"""
        try:
            logger.info("正在初始化AI视频生成器2.0...")
            
            # 创建工作流程
            self.workflow = create_video_generation_workflow(self.config)
            
            # 初始化工作流程
            success = await self.workflow.initialize()
            
            if success:
                self.is_initialized = True
                logger.info("AI视频生成器2.0初始化完成")
                return True
            else:
                logger.error("AI视频生成器2.0初始化失败")
                return False
                
        except Exception as e:
            logger.error(f"初始化异常: {e}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        if self.workflow:
            await self.workflow.cleanup()
        logger.info("AI视频生成器2.0资源清理完成")
    
    async def generate_video(self, 
                           input_text: str,
                           output_dir: str = "",
                           title: str = "",
                           description: str = "",
                           tags: List[str] = None,
                           target_language: str = "zh-CN",
                           video_duration: float = 30.0,
                           video_style: str = "realistic",
                           publish_platforms: List[str] = None,
                           privacy: str = "public",
                           schedule_time: str = None) -> WorkflowResult:
        """生成视频"""
        if not self.is_initialized:
            raise Exception("AI视频生成器未初始化")
        
        # 创建输出目录
        if not output_dir:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = os.path.join(os.getcwd(), "output", f"video_{timestamp}")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 配置工作流程
        workflow_config = WorkflowConfig(
            input_text=input_text,
            output_dir=output_dir,
            video_title=title,
            video_description=description,
            video_tags=tags or [],
            target_language=target_language,
            video_duration=video_duration,
            video_style=video_style,
            publish_platforms=publish_platforms or [],
            publish_privacy=privacy,
            schedule_time=schedule_time
        )
        
        # 设置进度回调
        def progress_callback(progress: float, message: str):
            print(f"\r进度: {progress*100:.1f}% - {message}", end="", flush=True)
        
        def step_callback(step):
            if step.status == "completed":
                print(f"\n✓ {step.name} 完成")
            elif step.status == "failed":
                print(f"\n✗ {step.name} 失败: {step.error}")
        
        self.workflow.set_progress_callback(progress_callback)
        self.workflow.set_step_callback(step_callback)
        
        # 运行工作流程
        logger.info(f"开始生成视频，输出目录: {output_dir}")
        result = await self.workflow.run_workflow(workflow_config)
        
        print("\n")  # 换行
        
        if result.success:
            logger.info(f"视频生成成功！耗时: {result.total_time:.2f}秒")
            logger.info(f"最终视频: {result.final_video}")
        else:
            logger.error(f"视频生成失败: {result.error_message}")
        
        return result
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        if not self.workflow:
            return {'initialized': False}
        
        status = {
            'initialized': self.is_initialized,
            'services': {}
        }
        
        # 检查各个服务的状态
        if hasattr(self.workflow, 'llm_manager') and self.workflow.llm_manager:
            status['services']['llm'] = {
                'available_clients': len(self.workflow.llm_manager.get_all_clients()),
                'clients': [client.name for client in self.workflow.llm_manager.get_all_clients()]
            }
        
        if hasattr(self.workflow, 'tts_manager') and self.workflow.tts_manager:
            status['services']['tts'] = {
                'available_clients': len(self.workflow.tts_manager.get_all_clients()),
                'clients': [client.name for client in self.workflow.tts_manager.get_all_clients()]
            }
        
        if hasattr(self.workflow, 'image_manager') and self.workflow.image_manager:
            status['services']['image'] = {
                'available_clients': len(self.workflow.image_manager.get_all_clients()),
                'clients': [client.name for client in self.workflow.image_manager.get_all_clients()]
            }
        
        if hasattr(self.workflow, 'video_manager') and self.workflow.video_manager:
            status['services']['video'] = {
                'available_clients': len(self.workflow.video_manager.get_all_clients()),
                'clients': [client.name for client in self.workflow.video_manager.get_all_clients()]
            }
        
        if hasattr(self.workflow, 'social_media_manager') and self.workflow.social_media_manager:
            status['services']['social_media'] = {
                'available_platforms': self.workflow.social_media_manager.get_available_platforms()
            }
        
        return status

def print_banner():
    """打印程序横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    AI视频生成器 2.0                          ║
║              集成MCP工具的完整AI视频生成解决方案              ║
║                                                              ║
║  功能特性:                                                   ║
║  • 文章创作/改写 → 五阶段分镜 → 配音 → 绘图                 ║
║  • 图转视频 → 视频合成 → 多平台发布                         ║
║  • 支持中英双语，智能服务路由，高质量输出                   ║
║                                                              ║
║  支持平台: YouTube, TikTok, 微信视频号, 抖音, B站等          ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI视频生成器2.0")
    parser.add_argument('--input', '-i', type=str, help='输入文本或文章')
    parser.add_argument('--output', '-o', type=str, help='输出目录')
    parser.add_argument('--title', '-t', type=str, help='视频标题')
    parser.add_argument('--description', '-d', type=str, help='视频描述')
    parser.add_argument('--tags', type=str, help='视频标签（逗号分隔）')
    parser.add_argument('--language', '-l', type=str, default='zh-CN', help='目标语言')
    parser.add_argument('--duration', type=float, default=30.0, help='视频时长（秒）')
    parser.add_argument('--style', type=str, default='realistic', help='视频风格')
    parser.add_argument('--platforms', type=str, help='发布平台（逗号分隔）')
    parser.add_argument('--privacy', type=str, default='public', help='隐私设置')
    parser.add_argument('--config', '-c', type=str, help='配置文件路径')
    parser.add_argument('--interactive', action='store_true', help='交互模式')
    parser.add_argument('--status', action='store_true', help='显示服务状态')
    
    args = parser.parse_args()
    
    print_banner()
    
    # 创建AI视频生成器
    generator = AIVideoGenerator(args.config)
    
    try:
        # 初始化
        print("正在初始化服务...")
        success = await generator.initialize()
        
        if not success:
            print("❌ 初始化失败，请检查配置")
            return
        
        print("✅ 初始化完成")
        
        # 显示服务状态
        if args.status:
            status = generator.get_service_status()
            print("\n📊 服务状态:")
            print(json.dumps(status, ensure_ascii=False, indent=2))
            return
        
        # 交互模式
        if args.interactive:
            await interactive_mode(generator)
            return
        
        # 命令行模式
        if args.input:
            tags = args.tags.split(',') if args.tags else []
            platforms = args.platforms.split(',') if args.platforms else []
            
            print(f"\n🎬 开始生成视频...")
            print(f"输入文本: {args.input[:100]}{'...' if len(args.input) > 100 else ''}")
            print(f"目标语言: {args.language}")
            print(f"视频时长: {args.duration}秒")
            print(f"输出目录: {args.output or '自动生成'}")
            
            result = await generator.generate_video(
                input_text=args.input,
                output_dir=args.output,
                title=args.title,
                description=args.description,
                tags=tags,
                target_language=args.language,
                video_duration=args.duration,
                video_style=args.style,
                publish_platforms=platforms,
                privacy=args.privacy
            )
            
            # 显示结果
            if result.success:
                print(f"\n🎉 视频生成成功！")
                print(f"📁 输出目录: {os.path.dirname(result.final_video)}")
                print(f"🎥 最终视频: {result.final_video}")
                print(f"⏱️  总耗时: {result.total_time:.2f}秒")
                
                if result.publish_results:
                    print(f"📤 发布结果: {result.publish_results.get('successful_count', 0)} 个平台成功")
            else:
                print(f"\n❌ 视频生成失败: {result.error_message}")
                if result.failed_steps:
                    print(f"失败步骤: {', '.join(result.failed_steps)}")
        else:
            print("\n请使用 --input 参数指定输入文本，或使用 --interactive 进入交互模式")
            print("使用 --help 查看所有参数")
    
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断操作")
    except Exception as e:
        logger.error(f"程序异常: {e}")
        print(f"\n❌ 程序异常: {e}")
    finally:
        await generator.cleanup()
        print("\n👋 程序结束")

async def interactive_mode(generator: AIVideoGenerator):
    """交互模式"""
    print("\n🎯 进入交互模式")
    print("输入 'help' 查看帮助，输入 'quit' 退出")
    
    while True:
        try:
            print("\n" + "="*50)
            command = input("请选择操作 [generate/status/help/quit]: ").strip().lower()
            
            if command == 'quit':
                break
            elif command == 'help':
                print_help()
            elif command == 'status':
                status = generator.get_service_status()
                print("\n📊 服务状态:")
                print(json.dumps(status, ensure_ascii=False, indent=2))
            elif command == 'generate':
                await interactive_generate(generator)
            else:
                print("❌ 未知命令，请输入 'help' 查看帮助")
                
        except KeyboardInterrupt:
            print("\n\n⏹️  返回主菜单")
        except Exception as e:
            print(f"\n❌ 操作异常: {e}")

def print_help():
    """打印帮助信息"""
    help_text = """
📖 可用命令:
  generate  - 生成视频
  status    - 查看服务状态
  help      - 显示帮助
  quit      - 退出程序

🎬 视频生成流程:
  1. 文章创作/改写
  2. 五阶段分镜生成
  3. 配音生成
  4. 图像生成
  5. 图转视频
  6. 视频合成
  7. 多平台发布（可选）

💡 提示:
  • 确保已配置相关API密钥
  • 视频生成需要一定时间，请耐心等待
  • 支持中英双语内容生成
"""
    print(help_text)

async def interactive_generate(generator: AIVideoGenerator):
    """交互式视频生成"""
    print("\n🎬 视频生成向导")
    
    # 获取用户输入
    input_text = input("请输入文章内容或主题: ").strip()
    if not input_text:
        print("❌ 输入内容不能为空")
        return
    
    title = input("视频标题 (可选): ").strip()
    description = input("视频描述 (可选): ").strip()
    tags_input = input("视频标签 (逗号分隔，可选): ").strip()
    tags = [tag.strip() for tag in tags_input.split(',')] if tags_input else []
    
    language = input("目标语言 [zh-CN/en-US] (默认: zh-CN): ").strip() or "zh-CN"
    
    try:
        duration = float(input("视频时长/秒 (默认: 30): ").strip() or "30")
    except ValueError:
        duration = 30.0
    
    style = input("视频风格 [realistic/anime/artistic] (默认: realistic): ").strip() or "realistic"
    
    platforms_input = input("发布平台 (逗号分隔，可选): ").strip()
    platforms = [p.strip() for p in platforms_input.split(',')] if platforms_input else []
    
    privacy = input("隐私设置 [public/private] (默认: public): ").strip() or "public"
    
    # 确认信息
    print("\n📋 生成配置:")
    print(f"  输入内容: {input_text[:100]}{'...' if len(input_text) > 100 else ''}")
    print(f"  视频标题: {title or '自动生成'}")
    print(f"  目标语言: {language}")
    print(f"  视频时长: {duration}秒")
    print(f"  视频风格: {style}")
    print(f"  发布平台: {', '.join(platforms) if platforms else '无'}")
    print(f"  隐私设置: {privacy}")
    
    confirm = input("\n确认开始生成? [y/N]: ").strip().lower()
    if confirm != 'y':
        print("❌ 已取消")
        return
    
    # 开始生成
    print("\n🚀 开始生成视频...")
    
    result = await generator.generate_video(
        input_text=input_text,
        title=title,
        description=description,
        tags=tags,
        target_language=language,
        video_duration=duration,
        video_style=style,
        publish_platforms=platforms,
        privacy=privacy
    )
    
    # 显示结果
    if result.success:
        print(f"\n🎉 视频生成成功！")
        print(f"📁 输出目录: {os.path.dirname(result.final_video)}")
        print(f"🎥 最终视频: {result.final_video}")
        print(f"⏱️  总耗时: {result.total_time:.2f}秒")
        
        if result.publish_results:
            print(f"📤 发布结果: {result.publish_results.get('successful_count', 0)} 个平台成功")
        
        # 询问是否打开输出目录
        open_dir = input("\n是否打开输出目录? [y/N]: ").strip().lower()
        if open_dir == 'y':
            import subprocess
            import platform
            
            output_dir = os.path.dirname(result.final_video)
            if platform.system() == "Windows":
                subprocess.run(["explorer", output_dir])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", output_dir])
            else:  # Linux
                subprocess.run(["xdg-open", output_dir])
    else:
        print(f"\n❌ 视频生成失败: {result.error_message}")
        if result.failed_steps:
            print(f"失败步骤: {', '.join(result.failed_steps)}")

if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行主程序
    asyncio.run(main())