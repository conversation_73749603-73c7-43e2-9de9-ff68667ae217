"""Pytest配置文件

包含测试的全局配置和夹具。
"""

import pytest
import asyncio
import tempfile
import shutil
from typing import Generator, AsyncGenerator
from pathlib import Path
from unittest.mock import Mock, AsyncMock

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession

from src.core.config import ConfigManager
from src.models.base import Base
from src.services.base import ServiceConfig
from src.services.registry import ServiceRegistry
from src.utils.logger import configure_logging

# 测试数据目录
TEST_DATA_DIR = Path(__file__).parent / "data"


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """创建事件循环用于异步测试."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_data_dir() -> Path:
    """测试数据目录."""
    return TEST_DATA_DIR


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """临时目录fixture"""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def test_config(temp_dir: Path) -> ConfigManager:
    """测试配置fixture"""
    config = ConfigManager()

    # 覆盖配置为测试值
    config.app.name = "AI Video Generator Test"
    config.app.version = "2.0.0-test"
    config.app.debug = True
    config.app.log_level = "DEBUG"

    config.database.url = "sqlite:///:memory:"
    config.database.echo = True

    config.ui.window_width = 800
    config.ui.window_height = 600

    return config


@pytest.fixture
async def async_db_engine():
    """异步数据库引擎fixture"""
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        echo=True
    )

    # 创建表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    await engine.dispose()


@pytest.fixture
async def async_db_session(async_db_engine) -> AsyncGenerator[AsyncSession, None]:
    """异步数据库会话fixture"""
    async with AsyncSession(async_db_engine) as session:
        yield session


@pytest.fixture
def mock_service_config() -> ServiceConfig:
    """模拟服务配置fixture"""
    return ServiceConfig(
        api_key="test_api_key",
        base_url="https://api.test.com",
        timeout=30,
        custom_settings={
            "model": "test-model",
            "temperature": 0.7
        }
    )


@pytest.fixture
def sample_data() -> dict:
    """提供示例测试数据."""
    return {
        "project": {
            "title": "测试项目",
            "description": "这是一个测试项目",
            "content": "从前有一个小女孩，她住在森林里...",
            "style": "动画",
            "language": "zh-CN",
            "video_width": 1920,
            "video_height": 1080,
            "video_fps": 24.0
        },
        "storyboard": {
            "name": "测试分镜",
            "content": "小女孩走在森林小径上，阳光透过树叶洒下斑驳的光影。",
            "generation_settings": {
                "style": "动画",
                "duration": 10,
                "quality": "high"
            }
        },
        "shot": {
            "name": "镜头1",
            "sequence_number": 1,
            "shot_type": "medium_shot",
            "content": "小女孩的中景镜头，她正在森林中行走",
            "duration": 3.0,
            "image_prompt": "A young girl walking in a forest, medium shot, animation style",
            "video_prompt": "Girl walking through forest path",
            "voice_prompt": "小女孩轻快地走在森林小径上"
        }
    }


@pytest.fixture
async def async_client() -> AsyncGenerator[None, None]:
    """异步客户端夹具."""
    # 这里可以设置异步客户端
    yield
    # 清理代码
