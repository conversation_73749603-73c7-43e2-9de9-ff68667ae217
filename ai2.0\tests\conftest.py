"""Pytest配置文件

包含测试的全局配置和夹具。
"""

import pytest
import asyncio
from typing import Generator, AsyncGenerator
from pathlib import Path

# 测试数据目录
TEST_DATA_DIR = Path(__file__).parent / "data"


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """创建事件循环用于异步测试."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_data_dir() -> Path:
    """测试数据目录."""
    return TEST_DATA_DIR


@pytest.fixture
async def async_client() -> AsyncGenerator[None, None]:
    """异步客户端夹具."""
    # 这里可以设置异步客户端
    yield
    # 清理代码
