#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器主程序
直接启动主窗口，无需登录验证
"""

import sys
import os
import atexit
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

def exit_handler():
    """
    程序退出处理函数
    """
    print("程序正在退出...")

if __name__ == "__main__":
    # 注册退出处理函数
    atexit.register(exit_handler)
    
    # 设置Qt属性以支持QtWebEngine
    try:
        QApplication.setAttribute(Qt.AA_ShareOpenGLContexts)  # type: ignore
    except AttributeError:
        # 如果属性不存在，忽略错误
        pass
    
    app = QApplication(sys.argv)
    
    try:
        # 初始化日志系统
        from src.utils.logger import logger
        logger.info("🚀 正在启动现代化AI视频生成器...")
        print("正在启动现代化AI视频生成器...")

        # 初始化服务管理器（全局单例）
        from src.core.service_manager import ServiceManager
        from src.utils.config_manager import ConfigManager

        logger.info("⚙️ 正在初始化配置管理器...")
        config_manager = ConfigManager()
        logger.info("✅ 配置管理器初始化完成")

        logger.info("🔧 正在初始化服务管理器...")
        print("正在初始化服务管理器...")
        service_manager = ServiceManager(config_manager)
        logger.info("✅ 服务管理器初始化完成")
        print("服务管理器初始化完成")

        # 检查服务状态
        from src.core.service_manager import ServiceType
        llm_service = service_manager.get_service(ServiceType.LLM)
        image_service = service_manager.get_service(ServiceType.IMAGE)
        voice_service = service_manager.get_service(ServiceType.VOICE)

        logger.info(f"📋 服务状态检查:")
        logger.info(f"  🤖 LLM服务: {'✅ 可用' if llm_service else '❌ 不可用'}")
        logger.info(f"  🖼️ 图像服务: {'✅ 可用' if image_service else '❌ 不可用'}")
        logger.info(f"  🎤 语音服务: {'✅ 可用' if voice_service else '❌ 不可用'}")

        # 导入并创建现代化卡片式主窗口
        from src.gui.modern_card_main_window import ModernCardMainWindow

        logger.info("🎨 正在设置应用程序样式...")
        # 设置应用程序样式
        app.setStyle("Fusion")

        logger.info("🖥️ 正在创建主窗口...")
        main_window = ModernCardMainWindow()
        main_window.show()
        logger.info("✅ 现代化界面已启动")
        print("现代化界面已启动")

        logger.info("🔄 启动事件循环...")
        # 启动事件循环
        sys.exit(app.exec_())
    except ImportError as e:
        print(f"导入主窗口失败: {e}")
        QMessageBox.critical(None, "错误", f"无法启动主程序: {e}")  # type: ignore
        sys.exit(1)
    except Exception as e:
        print(f"启动主程序失败: {e}")
        QMessageBox.critical(None, "错误", f"启动主程序时发生错误: {e}")  # type: ignore
        sys.exit(1)