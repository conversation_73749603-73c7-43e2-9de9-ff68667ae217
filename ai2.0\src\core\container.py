"""依赖注入容器

提供依赖注入功能，管理服务的生命周期和依赖关系。
"""

from typing import Dict, Type, TypeVar, Callable, Any, Optional, cast
from abc import ABC, abstractmethod
import inspect
import asyncio
from enum import Enum

T = TypeVar('T')


class ServiceLifetime(Enum):
    """服务生命周期"""
    SINGLETON = "singleton"
    TRANSIENT = "transient"
    SCOPED = "scoped"


class ServiceDescriptor:
    """服务描述符"""
    
    def __init__(
        self,
        service_type: Type[T],
        implementation: Type[T],
        lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT,
        factory: Optional[Callable[[], T]] = None
    ):
        self.service_type = service_type
        self.implementation = implementation
        self.lifetime = lifetime
        self.factory = factory


class Container:
    """依赖注入容器"""
    
    def __init__(self):
        self._services: Dict[str, ServiceDescriptor] = {}
        self._singletons: Dict[str, Any] = {}
        self._scoped_instances: Dict[str, Any] = {}
        
    def register_singleton(
        self, 
        service_type: Type[T], 
        implementation: Optional[Type[T]] = None,
        factory: Optional[Callable[[], T]] = None
    ) -> 'Container':
        """注册单例服务"""
        impl = implementation or service_type
        key = self._get_service_key(service_type)
        
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation=impl,
            lifetime=ServiceLifetime.SINGLETON,
            factory=factory
        )
        
        self._services[key] = descriptor
        return self
    
    def register_transient(
        self, 
        service_type: Type[T], 
        implementation: Optional[Type[T]] = None,
        factory: Optional[Callable[[], T]] = None
    ) -> 'Container':
        """注册瞬态服务"""
        impl = implementation or service_type
        key = self._get_service_key(service_type)
        
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation=impl,
            lifetime=ServiceLifetime.TRANSIENT,
            factory=factory
        )
        
        self._services[key] = descriptor
        return self
    
    def register_scoped(
        self, 
        service_type: Type[T], 
        implementation: Optional[Type[T]] = None,
        factory: Optional[Callable[[], T]] = None
    ) -> 'Container':
        """注册作用域服务"""
        impl = implementation or service_type
        key = self._get_service_key(service_type)
        
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation=impl,
            lifetime=ServiceLifetime.SCOPED,
            factory=factory
        )
        
        self._services[key] = descriptor
        return self
    
    def get(self, service_type: Type[T]) -> T:
        """获取服务实例"""
        key = self._get_service_key(service_type)
        
        if key not in self._services:
            raise ValueError(f"Service {service_type.__name__} not registered")
        
        descriptor = self._services[key]
        
        # 单例模式
        if descriptor.lifetime == ServiceLifetime.SINGLETON:
            if key in self._singletons:
                return cast(T, self._singletons[key])
            
            instance = self._create_instance(descriptor)
            self._singletons[key] = instance
            return cast(T, instance)
        
        # 作用域模式
        elif descriptor.lifetime == ServiceLifetime.SCOPED:
            if key in self._scoped_instances:
                return cast(T, self._scoped_instances[key])
            
            instance = self._create_instance(descriptor)
            self._scoped_instances[key] = instance
            return cast(T, instance)
        
        # 瞬态模式
        else:
            return cast(T, self._create_instance(descriptor))
    
    def _create_instance(self, descriptor: ServiceDescriptor) -> Any:
        """创建服务实例"""
        if descriptor.factory:
            return descriptor.factory()
        
        # 获取构造函数参数
        constructor = descriptor.implementation.__init__
        signature = inspect.signature(constructor)
        
        # 解析依赖
        dependencies = {}
        for param_name, param in signature.parameters.items():
            if param_name == 'self':
                continue
            
            if param.annotation == inspect.Parameter.empty:
                raise ValueError(
                    f"Parameter {param_name} in {descriptor.implementation.__name__} "
                    f"must have type annotation"
                )
            
            # 递归解析依赖
            dependency = self.get(param.annotation)
            dependencies[param_name] = dependency
        
        return descriptor.implementation(**dependencies)
    
    def _get_service_key(self, service_type: Type) -> str:
        """获取服务键"""
        return f"{service_type.__module__}.{service_type.__name__}"
    
    def clear_scoped(self) -> None:
        """清除作用域实例"""
        self._scoped_instances.clear()
    
    def is_registered(self, service_type: Type[T]) -> bool:
        """检查服务是否已注册"""
        key = self._get_service_key(service_type)
        return key in self._services


# 全局容器实例
_container: Optional[Container] = None


def get_container() -> Container:
    """获取全局容器实例"""
    global _container
    if _container is None:
        _container = Container()
    return _container


def configure_container() -> Container:
    """配置容器"""
    container = get_container()
    
    # 这里可以注册基础服务
    # container.register_singleton(ConfigManager)
    # container.register_singleton(EventBus)
    
    return container
