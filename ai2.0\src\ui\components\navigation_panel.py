"""导航面板

左侧导航面板组件。
"""

from typing import List, Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QListWidget, QListWidgetItem, QFrame, QScrollArea
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QFont

from src.ui.base import BaseWidget, ResponsiveWidget
from src.utils.logger import get_logger


class NavigationPanel(ResponsiveWidget):
    """导航面板"""
    
    # 信号定义
    page_changed = pyqtSignal(str)  # 页面变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        self._current_page = "dashboard"
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self) -> None:
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 标题区域
        title_frame = QFrame()
        title_frame.setProperty("class", "nav-header")
        title_layout = QVBoxLayout(title_frame)
        title_layout.setContentsMargins(16, 16, 16, 16)
        
        title_label = QLabel("AI视频生成器")
        title_label.setProperty("class", "nav-title")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        subtitle_label = QLabel("v2.0")
        subtitle_label.setProperty("class", "nav-subtitle")
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        
        layout.addWidget(title_frame)
        
        # 导航菜单
        self.nav_list = QListWidget()
        self.nav_list.setProperty("class", "nav-list")
        
        # 添加导航项
        self._add_nav_items()
        
        layout.addWidget(self.nav_list)
        
        # 底部区域
        bottom_frame = QFrame()
        bottom_frame.setProperty("class", "nav-footer")
        bottom_layout = QVBoxLayout(bottom_frame)
        bottom_layout.setContentsMargins(16, 16, 16, 16)
        
        # 主题切换按钮
        self.theme_button = QPushButton("切换主题")
        self.theme_button.setProperty("class", "nav-button")
        bottom_layout.addWidget(self.theme_button)
        
        layout.addWidget(bottom_frame)
    
    def _add_nav_items(self) -> None:
        """添加导航项"""
        nav_items = [
            {"name": "dashboard", "title": "仪表板", "icon": None},
            {"name": "projects", "title": "项目管理", "icon": None},
            {"name": "storyboard", "title": "分镜生成", "icon": None},
            {"name": "media", "title": "媒体库", "icon": None},
            {"name": "settings", "title": "设置", "icon": None},
        ]
        
        for item_data in nav_items:
            item = QListWidgetItem(item_data["title"])
            item.setData(Qt.ItemDataRole.UserRole, item_data["name"])
            self.nav_list.addItem(item)
        
        # 设置默认选中项
        self.nav_list.setCurrentRow(0)
    
    def _connect_signals(self) -> None:
        """连接信号"""
        self.nav_list.currentItemChanged.connect(self._on_nav_item_changed)
        self.theme_button.clicked.connect(self._on_theme_button_clicked)
    
    def _on_nav_item_changed(self, current, previous) -> None:
        """导航项变化处理"""
        if current:
            page_name = current.data(Qt.ItemDataRole.UserRole)
            if page_name != self._current_page:
                self._current_page = page_name
                self.page_changed.emit(page_name)
                self.logger.debug(f"Navigation changed to: {page_name}")
    
    def _on_theme_button_clicked(self) -> None:
        """主题按钮点击处理"""
        from src.ui.themes import get_theme_manager
        theme_manager = get_theme_manager()
        theme_manager.toggle_theme()
    
    def set_current_page(self, page_name: str) -> None:
        """设置当前页面"""
        for i in range(self.nav_list.count()):
            item = self.nav_list.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == page_name:
                self.nav_list.setCurrentItem(item)
                break
    
    def _on_breakpoint_changed(self, breakpoint: str) -> None:
        """响应式断点变化处理"""
        if self.is_mobile():
            # 移动端：隐藏文本，只显示图标
            self._set_compact_mode(True)
        else:
            # 桌面端：显示完整导航
            self._set_compact_mode(False)

    def _set_compact_mode(self, compact: bool) -> None:
        """设置紧凑模式"""
        if compact:
            # 紧凑模式：减小宽度，隐藏部分文本
            self.setFixedWidth(60)
            # 隐藏标题文本
            for i in range(self.nav_list.count()):
                item = self.nav_list.item(i)
                if item:
                    # 只显示图标或缩写
                    original_text = item.data(Qt.ItemDataRole.UserRole + 1)
                    if not original_text:
                        # 保存原始文本
                        item.setData(Qt.ItemDataRole.UserRole + 1, item.text())
                        original_text = item.text()

                    # 设置缩写文本
                    abbrev = self._get_text_abbreviation(original_text)
                    item.setText(abbrev)
        else:
            # 完整模式：恢复正常宽度和文本
            self.setFixedWidth(250)
            for i in range(self.nav_list.count()):
                item = self.nav_list.item(i)
                if item:
                    original_text = item.data(Qt.ItemDataRole.UserRole + 1)
                    if original_text:
                        item.setText(original_text)

    def _get_text_abbreviation(self, text: str) -> str:
        """获取文本缩写"""
        abbreviations = {
            "仪表板": "仪",
            "项目管理": "项",
            "分镜生成": "分",
            "媒体库": "媒",
            "设置": "设"
        }
        return abbreviations.get(text, text[:1])

    def _apply_theme(self) -> None:
        """应用主题"""
        # 这里可以根据主题更新样式
        pass
