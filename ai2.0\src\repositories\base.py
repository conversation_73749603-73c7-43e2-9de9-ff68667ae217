"""基础仓库模式

定义数据访问层的基础仓库模式和通用操作。
"""

from typing import TypeVar, Generic, List, Optional, Dict, Any, Type, Union
from abc import ABC, abstractmethod
from uuid import UUID
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.exc import SQLAlchemyError

from src.models.base import BaseEntity
from src.core.exceptions import DataAccessError, ValidationError

T = TypeVar('T', bound=BaseEntity)


class BaseRepository(Generic[T], ABC):
    """基础仓库抽象类"""
    
    def __init__(self, session: Union[Session, AsyncSession], model_class: Type[T]):
        self.session = session
        self.model_class = model_class
    
    @abstractmethod
    async def create(self, entity: T) -> T:
        """创建实体"""
        pass
    
    @abstractmethod
    async def get_by_id(self, entity_id: UUID) -> Optional[T]:
        """根据ID获取实体"""
        pass
    
    @abstractmethod
    async def get_all(self, skip: int = 0, limit: int = 100, include_deleted: bool = False) -> List[T]:
        """获取所有实体"""
        pass
    
    @abstractmethod
    async def update(self, entity: T) -> T:
        """更新实体"""
        pass
    
    @abstractmethod
    async def delete(self, entity_id: UUID, soft_delete: bool = True) -> bool:
        """删除实体"""
        pass
    
    @abstractmethod
    async def count(self, include_deleted: bool = False) -> int:
        """统计实体数量"""
        pass


class AsyncRepository(BaseRepository[T]):
    """异步仓库实现"""
    
    def __init__(self, session: AsyncSession, model_class: Type[T]):
        super().__init__(session, model_class)
    
    async def create(self, entity: T) -> T:
        """创建实体"""
        try:
            self.session.add(entity)
            await self.session.commit()
            await self.session.refresh(entity)
            return entity
        except SQLAlchemyError as e:
            await self.session.rollback()
            raise DataAccessError(f"Failed to create {self.model_class.__name__}: {str(e)}", cause=e)
    
    async def get_by_id(self, entity_id: UUID, include_deleted: bool = False) -> Optional[T]:
        """根据ID获取实体"""
        try:
            query = select(self.model_class).where(self.model_class.id == entity_id)
            
            if not include_deleted:
                query = query.where(self.model_class.is_deleted == False)
            
            result = await self.session.execute(query)
            return result.scalar_one_or_none()
        except SQLAlchemyError as e:
            raise DataAccessError(f"Failed to get {self.model_class.__name__} by ID: {str(e)}", cause=e)
    
    async def get_all(
        self, 
        skip: int = 0, 
        limit: int = 100, 
        include_deleted: bool = False,
        order_by: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[T]:
        """获取所有实体"""
        try:
            query = select(self.model_class)
            
            # 应用过滤器
            if not include_deleted:
                query = query.where(self.model_class.is_deleted == False)
            
            if filters:
                for key, value in filters.items():
                    if hasattr(self.model_class, key):
                        column = getattr(self.model_class, key)
                        if isinstance(value, list):
                            query = query.where(column.in_(value))
                        else:
                            query = query.where(column == value)
            
            # 排序
            if order_by:
                if order_by.startswith('-'):
                    # 降序
                    column_name = order_by[1:]
                    if hasattr(self.model_class, column_name):
                        column = getattr(self.model_class, column_name)
                        query = query.order_by(column.desc())
                else:
                    # 升序
                    if hasattr(self.model_class, order_by):
                        column = getattr(self.model_class, order_by)
                        query = query.order_by(column.asc())
            else:
                # 默认按创建时间降序
                query = query.order_by(self.model_class.created_at.desc())
            
            # 分页
            query = query.offset(skip).limit(limit)
            
            result = await self.session.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            raise DataAccessError(f"Failed to get all {self.model_class.__name__}: {str(e)}", cause=e)
    
    async def update(self, entity: T) -> T:
        """更新实体"""
        try:
            entity.updated_at = datetime.utcnow()
            await self.session.commit()
            await self.session.refresh(entity)
            return entity
        except SQLAlchemyError as e:
            await self.session.rollback()
            raise DataAccessError(f"Failed to update {self.model_class.__name__}: {str(e)}", cause=e)
    
    async def update_by_id(self, entity_id: UUID, data: Dict[str, Any]) -> Optional[T]:
        """根据ID更新实体"""
        try:
            # 添加更新时间
            data['updated_at'] = datetime.utcnow()
            
            query = (
                update(self.model_class)
                .where(and_(
                    self.model_class.id == entity_id,
                    self.model_class.is_deleted == False
                ))
                .values(**data)
            )
            
            result = await self.session.execute(query)
            await self.session.commit()
            
            if result.rowcount > 0:
                return await self.get_by_id(entity_id)
            return None
        except SQLAlchemyError as e:
            await self.session.rollback()
            raise DataAccessError(f"Failed to update {self.model_class.__name__} by ID: {str(e)}", cause=e)
    
    async def delete(self, entity_id: UUID, soft_delete: bool = True) -> bool:
        """删除实体"""
        try:
            if soft_delete:
                # 软删除
                data = {
                    'is_deleted': True,
                    'deleted_at': datetime.utcnow(),
                    'updated_at': datetime.utcnow()
                }
                result = await self.update_by_id(entity_id, data)
                return result is not None
            else:
                # 硬删除
                query = delete(self.model_class).where(self.model_class.id == entity_id)
                result = await self.session.execute(query)
                await self.session.commit()
                return result.rowcount > 0
        except SQLAlchemyError as e:
            await self.session.rollback()
            raise DataAccessError(f"Failed to delete {self.model_class.__name__}: {str(e)}", cause=e)
    
    async def restore(self, entity_id: UUID) -> Optional[T]:
        """恢复软删除的实体"""
        try:
            data = {
                'is_deleted': False,
                'deleted_at': None,
                'updated_at': datetime.utcnow()
            }
            
            query = (
                update(self.model_class)
                .where(and_(
                    self.model_class.id == entity_id,
                    self.model_class.is_deleted == True
                ))
                .values(**data)
            )
            
            result = await self.session.execute(query)
            await self.session.commit()
            
            if result.rowcount > 0:
                return await self.get_by_id(entity_id, include_deleted=False)
            return None
        except SQLAlchemyError as e:
            await self.session.rollback()
            raise DataAccessError(f"Failed to restore {self.model_class.__name__}: {str(e)}", cause=e)
    
    async def count(self, include_deleted: bool = False, filters: Optional[Dict[str, Any]] = None) -> int:
        """统计实体数量"""
        try:
            query = select(func.count(self.model_class.id))
            
            if not include_deleted:
                query = query.where(self.model_class.is_deleted == False)
            
            if filters:
                for key, value in filters.items():
                    if hasattr(self.model_class, key):
                        column = getattr(self.model_class, key)
                        if isinstance(value, list):
                            query = query.where(column.in_(value))
                        else:
                            query = query.where(column == value)
            
            result = await self.session.execute(query)
            return result.scalar()
        except SQLAlchemyError as e:
            raise DataAccessError(f"Failed to count {self.model_class.__name__}: {str(e)}", cause=e)
    
    async def exists(self, entity_id: UUID, include_deleted: bool = False) -> bool:
        """检查实体是否存在"""
        try:
            query = select(func.count(self.model_class.id)).where(self.model_class.id == entity_id)
            
            if not include_deleted:
                query = query.where(self.model_class.is_deleted == False)
            
            result = await self.session.execute(query)
            return result.scalar() > 0
        except SQLAlchemyError as e:
            raise DataAccessError(f"Failed to check existence of {self.model_class.__name__}: {str(e)}", cause=e)
    
    async def find_by(self, **kwargs) -> List[T]:
        """根据条件查找实体"""
        try:
            query = select(self.model_class)
            
            # 默认排除已删除的记录
            if 'include_deleted' not in kwargs or not kwargs['include_deleted']:
                query = query.where(self.model_class.is_deleted == False)
            
            # 移除特殊参数
            kwargs.pop('include_deleted', None)
            
            # 应用过滤条件
            for key, value in kwargs.items():
                if hasattr(self.model_class, key):
                    column = getattr(self.model_class, key)
                    if isinstance(value, list):
                        query = query.where(column.in_(value))
                    else:
                        query = query.where(column == value)
            
            result = await self.session.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            raise DataAccessError(f"Failed to find {self.model_class.__name__}: {str(e)}", cause=e)
    
    async def find_one_by(self, **kwargs) -> Optional[T]:
        """根据条件查找单个实体"""
        results = await self.find_by(**kwargs)
        return results[0] if results else None
