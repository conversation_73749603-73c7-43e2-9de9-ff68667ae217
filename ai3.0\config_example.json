{"_comment": "AI视频生成器2.0 配置示例文件", "_note": "请将此文件复制为实际配置文件，并填入真实的API密钥", "llm": {"routing_strategy": "quality_first", "clients": {"zhipu": {"api_key": "your_zhipu_api_key_here", "base_url": "https://open.bigmodel.cn/api/paas/v4/", "model": "glm-4-flash"}, "qwen": {"api_key": "your_qwen_api_key_here", "base_url": "https://dashscope.aliyuncs.com/api/v1/", "model": "qwen-turbo"}, "deepseek": {"api_key": "your_deepseek_api_key_here", "base_url": "https://api.deepseek.com/v1/", "model": "deepseek-chat"}, "gemini": {"api_key": "your_gemini_api_key_here", "model": "gemini-1.5-flash"}}}, "translation": {"routing_strategy": "quality_first", "clients": {"baidu": {"app_id": "your_baidu_app_id_here", "secret_key": "your_baidu_secret_key_here", "base_url": "https://fanyi-api.baidu.com/api/trans/vip/translate"}, "google": {"api_key": "your_google_translate_api_key_here"}}}, "tts": {"default_engine": "edge", "engines": {"edge_tts": {"voice": "zh-CN-XiaoxiaoNeural", "rate": "+0%", "pitch": "+0Hz"}, "siliconflow": {"api_key": "your_siliconflow_api_key_here", "base_url": "https://api.siliconflow.cn/v1/audio/speech", "model": "FishSpeech", "voice": "fishaudio_fish_speech_1"}}}, "image_generation": {"routing_strategy": "quality_first", "engines": {"pollinations": {"base_url": "https://image.pollinations.ai/prompt", "width": 1024, "height": 1024, "model": "flux"}, "comfyui_local": {"base_url": "http://127.0.0.1:8188", "workflow_file": "workflow_api.json"}, "comfyui_cloud": {"api_key": "your_comfyui_cloud_api_key_here", "base_url": "https://api.comfy.icu"}, "dalle": {"api_key": "your_openai_api_key_here", "model": "dall-e-3", "size": "1024x1024", "quality": "standard"}, "stability": {"api_key": "your_stability_api_key_here", "base_url": "https://api.stability.ai", "model": "stable-diffusion-xl-1024-v1-0"}, "google_imagen": {"api_key": "your_google_api_key_here", "model": "imagen-3.0-generate-001"}, "cogview": {"api_key": "your_zhipu_api_key_here", "base_url": "https://open.bigmodel.cn/api/paas/v4/", "model": "cogview-3-flash"}}}, "video_generation": {"routing_strategy": "quality_first", "engines": {"cogvideox": {"api_key": "your_zhipu_api_key_here", "base_url": "https://open.bigmodel.cn/api/paas/v4/", "model": "cogvideox-flash"}, "replicate_svd": {"api_key": "your_replicate_api_key_here", "model": "stability-ai/stable-video-diffusion:3f0457e4619daac51203dedb1a4919c746e16d22e4ccb1e87c1b5e5d7b4c5b1c"}, "pixverse": {"api_key": "your_pixverse_api_key_here", "base_url": "https://api.pixverse.ai"}, "haiper": {"api_key": "your_haiper_api_key_here", "base_url": "https://api.haiper.ai"}, "runway": {"api_key": "your_runway_api_key_here", "base_url": "https://api.runwayml.com"}, "pika": {"api_key": "your_pika_api_key_here", "base_url": "https://api.pika.art"}}}, "social_media": {"platforms": {"youtube": {"client_id": "your_youtube_client_id_here", "client_secret": "your_youtube_client_secret_here", "refresh_token": "your_youtube_refresh_token_here", "api_key": "your_youtube_api_key_here"}, "tiktok": {"client_key": "your_tiktok_client_key_here", "client_secret": "your_tiktok_client_secret_here", "access_token": "your_tiktok_access_token_here"}, "wechat_channels": {"app_id": "your_wechat_app_id_here", "app_secret": "your_wechat_app_secret_here", "access_token": "your_wechat_access_token_here"}, "douyin": {"client_key": "your_douyin_client_key_here", "client_secret": "your_douyin_client_secret_here", "access_token": "your_douyin_access_token_here"}, "bilibili": {"access_token": "your_bilibili_access_token_here", "refresh_token": "your_bilibili_refresh_token_here", "csrf": "your_bilibili_csrf_here"}}}, "workflow": {"max_retries": 3, "timeout": 300, "parallel_tasks": 4, "temp_dir": "./temp", "output_dir": "./output", "log_level": "INFO"}, "performance": {"enable_caching": true, "cache_ttl": 3600, "max_concurrent_requests": 10, "request_timeout": 60}, "security": {"encrypt_api_keys": false, "log_api_requests": false, "rate_limiting": {"enabled": true, "requests_per_minute": 60}}}