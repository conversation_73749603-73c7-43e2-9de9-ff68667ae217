"""性能监控和优化工具

提供性能监控、内存管理和资源优化功能。
"""

import time
import psutil
import asyncio
import functools
import threading
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque, defaultdict
from contextlib import asynccontextmanager
import gc
import weakref

from src.utils.logger import get_logger


@dataclass
class PerformanceMetrics:
    """性能指标"""
    operation: str
    start_time: float
    end_time: float
    duration: float
    memory_before: float
    memory_after: float
    memory_delta: float
    cpu_percent: float
    thread_count: int
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration_ms(self) -> float:
        """持续时间（毫秒）"""
        return self.duration * 1000


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_records: int = 1000):
        self.max_records = max_records
        self.metrics: deque = deque(maxlen=max_records)
        self.operation_stats: Dict[str, List[float]] = defaultdict(list)
        self.logger = get_logger(__name__)
        self._lock = threading.Lock()
        
        # 系统监控
        self.process = psutil.Process()
        self.start_time = time.time()
        
    def record_metrics(self, metrics: PerformanceMetrics) -> None:
        """记录性能指标"""
        with self._lock:
            self.metrics.append(metrics)
            self.operation_stats[metrics.operation].append(metrics.duration)
            
            # 限制每个操作的记录数量
            if len(self.operation_stats[metrics.operation]) > 100:
                self.operation_stats[metrics.operation] = \
                    self.operation_stats[metrics.operation][-100:]
    
    def get_operation_stats(self, operation: str) -> Dict[str, float]:
        """获取操作统计信息"""
        durations = self.operation_stats.get(operation, [])
        if not durations:
            return {}
        
        sorted_durations = sorted(durations)
        count = len(durations)
        
        return {
            "count": count,
            "min": min(durations),
            "max": max(durations),
            "mean": sum(durations) / count,
            "median": sorted_durations[count // 2],
            "p95": sorted_durations[int(count * 0.95)] if count > 0 else 0,
            "p99": sorted_durations[int(count * 0.99)] if count > 0 else 0
        }
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        try:
            memory_info = self.process.memory_info()
            cpu_percent = self.process.cpu_percent()
            
            return {
                "memory": {
                    "rss": memory_info.rss / 1024 / 1024,  # MB
                    "vms": memory_info.vms / 1024 / 1024,  # MB
                    "percent": self.process.memory_percent()
                },
                "cpu": {
                    "percent": cpu_percent,
                    "num_threads": self.process.num_threads()
                },
                "uptime": time.time() - self.start_time,
                "gc_stats": {
                    "collections": gc.get_stats(),
                    "objects": len(gc.get_objects())
                }
            }
        except Exception as e:
            self.logger.error(f"Failed to get system metrics: {e}")
            return {}
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        summary = {
            "total_operations": len(self.metrics),
            "operation_stats": {},
            "system_metrics": self.get_system_metrics(),
            "slow_operations": [],
            "memory_intensive_operations": []
        }
        
        # 操作统计
        for operation in self.operation_stats:
            summary["operation_stats"][operation] = self.get_operation_stats(operation)
        
        # 慢操作（超过5秒）
        slow_ops = [m for m in self.metrics if m.duration > 5.0]
        summary["slow_operations"] = [
            {
                "operation": m.operation,
                "duration": m.duration,
                "timestamp": m.start_time
            }
            for m in sorted(slow_ops, key=lambda x: x.duration, reverse=True)[:10]
        ]
        
        # 内存密集操作（内存增长超过100MB）
        memory_ops = [m for m in self.metrics if m.memory_delta > 100]
        summary["memory_intensive_operations"] = [
            {
                "operation": m.operation,
                "memory_delta": m.memory_delta,
                "timestamp": m.start_time
            }
            for m in sorted(memory_ops, key=lambda x: x.memory_delta, reverse=True)[:10]
        ]
        
        return summary


# 全局性能监控器
_performance_monitor: Optional[PerformanceMonitor] = None


def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控器"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor


def performance_monitor(operation_name: str = None):
    """性能监控装饰器"""
    def decorator(func: Callable) -> Callable:
        op_name = operation_name or f"{func.__module__}.{func.__name__}"
        
        if asyncio.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                monitor = get_performance_monitor()
                
                # 记录开始状态
                start_time = time.perf_counter()
                memory_before = psutil.Process().memory_info().rss / 1024 / 1024
                cpu_before = psutil.Process().cpu_percent()
                
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    # 记录结束状态
                    end_time = time.perf_counter()
                    memory_after = psutil.Process().memory_info().rss / 1024 / 1024
                    cpu_after = psutil.Process().cpu_percent()
                    
                    metrics = PerformanceMetrics(
                        operation=op_name,
                        start_time=start_time,
                        end_time=end_time,
                        duration=end_time - start_time,
                        memory_before=memory_before,
                        memory_after=memory_after,
                        memory_delta=memory_after - memory_before,
                        cpu_percent=(cpu_before + cpu_after) / 2,
                        thread_count=threading.active_count()
                    )
                    
                    monitor.record_metrics(metrics)
            
            return async_wrapper
        else:
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                monitor = get_performance_monitor()
                
                # 记录开始状态
                start_time = time.perf_counter()
                memory_before = psutil.Process().memory_info().rss / 1024 / 1024
                cpu_before = psutil.Process().cpu_percent()
                
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    # 记录结束状态
                    end_time = time.perf_counter()
                    memory_after = psutil.Process().memory_info().rss / 1024 / 1024
                    cpu_after = psutil.Process().cpu_percent()
                    
                    metrics = PerformanceMetrics(
                        operation=op_name,
                        start_time=start_time,
                        end_time=end_time,
                        duration=end_time - start_time,
                        memory_before=memory_before,
                        memory_after=memory_after,
                        memory_delta=memory_after - memory_before,
                        cpu_percent=(cpu_before + cpu_after) / 2,
                        thread_count=threading.active_count()
                    )
                    
                    monitor.record_metrics(metrics)
            
            return sync_wrapper
    
    return decorator


@asynccontextmanager
async def performance_context(operation_name: str):
    """性能监控上下文管理器"""
    monitor = get_performance_monitor()
    
    # 记录开始状态
    start_time = time.perf_counter()
    memory_before = psutil.Process().memory_info().rss / 1024 / 1024
    cpu_before = psutil.Process().cpu_percent()
    
    try:
        yield
    finally:
        # 记录结束状态
        end_time = time.perf_counter()
        memory_after = psutil.Process().memory_info().rss / 1024 / 1024
        cpu_after = psutil.Process().cpu_percent()
        
        metrics = PerformanceMetrics(
            operation=operation_name,
            start_time=start_time,
            end_time=end_time,
            duration=end_time - start_time,
            memory_before=memory_before,
            memory_after=memory_after,
            memory_delta=memory_after - memory_before,
            cpu_percent=(cpu_before + cpu_after) / 2,
            thread_count=threading.active_count()
        )
        
        monitor.record_metrics(metrics)


class ResourceManager:
    """资源管理器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._resources: Dict[str, Any] = {}
        self._weak_refs: Dict[str, weakref.ref] = {}
        self._cleanup_callbacks: Dict[str, Callable] = {}
    
    def register_resource(
        self, 
        name: str, 
        resource: Any, 
        cleanup_callback: Optional[Callable] = None
    ) -> None:
        """注册资源"""
        self._resources[name] = resource
        
        if cleanup_callback:
            self._cleanup_callbacks[name] = cleanup_callback
        
        # 为大型对象创建弱引用
        try:
            if hasattr(resource, '__sizeof__') and resource.__sizeof__() > 1024 * 1024:  # 1MB
                self._weak_refs[name] = weakref.ref(
                    resource, 
                    lambda ref: self._on_resource_deleted(name)
                )
        except Exception:
            pass  # 某些对象可能不支持弱引用
    
    def get_resource(self, name: str) -> Optional[Any]:
        """获取资源"""
        return self._resources.get(name)
    
    def release_resource(self, name: str) -> None:
        """释放资源"""
        if name in self._resources:
            resource = self._resources.pop(name)
            
            # 执行清理回调
            if name in self._cleanup_callbacks:
                try:
                    self._cleanup_callbacks[name](resource)
                except Exception as e:
                    self.logger.error(f"Error in cleanup callback for {name}: {e}")
                finally:
                    del self._cleanup_callbacks[name]
            
            # 移除弱引用
            if name in self._weak_refs:
                del self._weak_refs[name]
            
            # 显式删除引用
            del resource
            
            # 建议垃圾回收
            gc.collect()
            
            self.logger.debug(f"Released resource: {name}")
    
    def _on_resource_deleted(self, name: str) -> None:
        """资源被删除时的回调"""
        self.logger.debug(f"Resource {name} was garbage collected")
        if name in self._weak_refs:
            del self._weak_refs[name]
    
    def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况"""
        usage = {
            "total_resources": len(self._resources),
            "weak_refs": len(self._weak_refs),
            "resource_sizes": {}
        }
        
        for name, resource in self._resources.items():
            try:
                if hasattr(resource, '__sizeof__'):
                    size_mb = resource.__sizeof__() / 1024 / 1024
                    usage["resource_sizes"][name] = f"{size_mb:.2f} MB"
            except Exception:
                usage["resource_sizes"][name] = "Unknown"
        
        return usage
    
    def cleanup_all(self) -> None:
        """清理所有资源"""
        resource_names = list(self._resources.keys())
        for name in resource_names:
            self.release_resource(name)
        
        # 强制垃圾回收
        gc.collect()
        
        self.logger.info(f"Cleaned up {len(resource_names)} resources")


# 全局资源管理器
_resource_manager: Optional[ResourceManager] = None


def get_resource_manager() -> ResourceManager:
    """获取全局资源管理器"""
    global _resource_manager
    if _resource_manager is None:
        _resource_manager = ResourceManager()
    return _resource_manager


class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def optimize_memory(self) -> Dict[str, Any]:
        """优化内存使用"""
        before_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # 执行垃圾回收
        collected = gc.collect()
        
        # 清理弱引用
        gc.collect()
        
        after_memory = psutil.Process().memory_info().rss / 1024 / 1024
        freed_memory = before_memory - after_memory
        
        result = {
            "before_memory_mb": before_memory,
            "after_memory_mb": after_memory,
            "freed_memory_mb": freed_memory,
            "objects_collected": collected,
            "gc_stats": gc.get_stats()
        }
        
        self.logger.info(f"Memory optimization: freed {freed_memory:.2f} MB, collected {collected} objects")
        return result
    
    def get_memory_info(self) -> Dict[str, Any]:
        """获取内存信息"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            "rss_mb": memory_info.rss / 1024 / 1024,
            "vms_mb": memory_info.vms / 1024 / 1024,
            "percent": process.memory_percent(),
            "available_mb": psutil.virtual_memory().available / 1024 / 1024,
            "gc_objects": len(gc.get_objects()),
            "gc_stats": gc.get_stats()
        }


# 全局内存优化器
_memory_optimizer: Optional[MemoryOptimizer] = None


def get_memory_optimizer() -> MemoryOptimizer:
    """获取全局内存优化器"""
    global _memory_optimizer
    if _memory_optimizer is None:
        _memory_optimizer = MemoryOptimizer()
    return _memory_optimizer
