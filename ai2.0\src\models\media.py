"""媒体数据模型

定义媒体相关的数据模型和模式。
"""

from typing import Optional, Dict, Any
from datetime import datetime
from pathlib import Path

from sqlalchemy import Column, String, Text, Enum, Integer, Float, ForeignKey, JSON, Boolean, DateTime
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from pydantic import Field, validator

from .base import (
    BaseEntity, BaseEntitySchema, CreateEntitySchema, UpdateEntitySchema,
    MediaType, MediaStatus, GenerationProvider, Constants
)


class MediaItem(BaseEntity):
    """媒体项模型"""
    
    __tablename__ = "media_items"
    
    # 关联镜头
    shot_id = Column(
        UUID(as_uuid=True),
        ForeignKey("shots.id", ondelete="CASCADE"),
        nullable=False,
        comment="镜头ID"
    )
    
    # 媒体基本信息
    media_type = Column(
        Enum(MediaType),
        nullable=False,
        comment="媒体类型"
    )
    
    status = Column(
        Enum(MediaStatus),
        default=MediaStatus.PENDING,
        nullable=False,
        comment="媒体状态"
    )
    
    # 文件信息
    file_path = Column(
        String(500),
        nullable=True,
        comment="文件路径"
    )
    
    file_name = Column(
        String(255),
        nullable=True,
        comment="文件名"
    )
    
    file_size = Column(
        Integer,
        nullable=True,
        comment="文件大小(字节)"
    )
    
    file_format = Column(
        String(50),
        nullable=True,
        comment="文件格式"
    )
    
    # 媒体属性
    width = Column(
        Integer,
        nullable=True,
        comment="宽度"
    )
    
    height = Column(
        Integer,
        nullable=True,
        comment="高度"
    )
    
    duration = Column(
        Float,
        nullable=True,
        comment="时长(秒)"
    )
    
    frame_rate = Column(
        Float,
        nullable=True,
        comment="帧率"
    )
    
    # 生成信息
    generation_provider = Column(
        Enum(GenerationProvider),
        nullable=True,
        comment="生成服务提供商"
    )
    
    generation_prompt = Column(
        Text,
        nullable=True,
        comment="生成提示词"
    )
    
    generation_settings = Column(
        JSON,
        nullable=True,
        comment="生成设置JSON"
    )
    
    generation_metadata = Column(
        JSON,
        nullable=True,
        comment="生成元数据JSON"
    )
    
    # 处理信息
    processing_started_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="处理开始时间"
    )
    
    processing_completed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="处理完成时间"
    )
    
    error_message = Column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    # 质量评估
    quality_score = Column(
        Float,
        nullable=True,
        comment="质量评分(0-1)"
    )
    
    is_primary = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否为主要媒体"
    )
    
    # 关联关系
    shot = relationship(
        "Shot",
        back_populates="media_items"
    )
    
    def get_file_path(self) -> Optional[Path]:
        """获取文件路径对象"""
        return Path(self.file_path) if self.file_path else None
    
    def get_file_url(self, base_url: str = "") -> Optional[str]:
        """获取文件URL"""
        if self.file_path:
            return f"{base_url.rstrip('/')}/{self.file_path}"
        return None
    
    def start_processing(self) -> None:
        """开始处理"""
        self.status = MediaStatus.GENERATING
        self.processing_started_at = datetime.utcnow()
        self.processing_completed_at = None
        self.error_message = None
    
    def complete_processing(self, file_path: str, file_size: int = None) -> None:
        """完成处理"""
        self.status = MediaStatus.COMPLETED
        self.processing_completed_at = datetime.utcnow()
        self.file_path = file_path
        if file_size:
            self.file_size = file_size
        self.error_message = None
    
    def fail_processing(self, error_message: str) -> None:
        """处理失败"""
        self.status = MediaStatus.FAILED
        self.processing_completed_at = datetime.utcnow()
        self.error_message = error_message
    
    def get_processing_duration(self) -> Optional[float]:
        """获取处理时长"""
        if self.processing_started_at and self.processing_completed_at:
            delta = self.processing_completed_at - self.processing_started_at
            return delta.total_seconds()
        return None
    
    def set_as_primary(self) -> None:
        """设置为主要媒体"""
        self.is_primary = True
    
    def update_quality_score(self, score: float) -> None:
        """更新质量评分"""
        self.quality_score = max(0.0, min(1.0, score))


# Pydantic模式
class MediaItemSchema(BaseEntitySchema):
    """媒体项模式"""
    
    shot_id: str = Field(..., description="镜头ID")
    media_type: MediaType = Field(..., description="媒体类型")
    status: MediaStatus = Field(MediaStatus.PENDING, description="媒体状态")
    file_path: Optional[str] = Field(None, description="文件路径")
    file_name: Optional[str] = Field(None, description="文件名")
    file_size: Optional[int] = Field(None, ge=0, description="文件大小(字节)")
    file_format: Optional[str] = Field(None, description="文件格式")
    width: Optional[int] = Field(None, ge=1, description="宽度")
    height: Optional[int] = Field(None, ge=1, description="高度")
    duration: Optional[float] = Field(None, ge=0.0, description="时长(秒)")
    frame_rate: Optional[float] = Field(None, ge=0.0, description="帧率")
    generation_provider: Optional[GenerationProvider] = Field(None, description="生成服务提供商")
    generation_prompt: Optional[str] = Field(None, description="生成提示词")
    generation_settings: Optional[Dict[str, Any]] = Field(None, description="生成设置")
    generation_metadata: Optional[Dict[str, Any]] = Field(None, description="生成元数据")
    processing_started_at: Optional[datetime] = Field(None, description="处理开始时间")
    processing_completed_at: Optional[datetime] = Field(None, description="处理完成时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    quality_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="质量评分")
    is_primary: bool = Field(False, description="是否为主要媒体")


class CreateMediaItemSchema(CreateEntitySchema):
    """创建媒体项模式"""
    
    shot_id: str = Field(..., description="镜头ID")
    media_type: MediaType = Field(..., description="媒体类型")
    generation_provider: Optional[GenerationProvider] = Field(None, description="生成服务提供商")
    generation_prompt: Optional[str] = Field(None, description="生成提示词")
    generation_settings: Optional[Dict[str, Any]] = Field(None, description="生成设置")
    file_path: Optional[str] = Field(None, description="文件路径")
    file_name: Optional[str] = Field(None, description="文件名")
    width: Optional[int] = Field(None, ge=1, description="宽度")
    height: Optional[int] = Field(None, ge=1, description="高度")
    duration: Optional[float] = Field(None, ge=0.0, description="时长(秒)")
    frame_rate: Optional[float] = Field(None, ge=0.0, description="帧率")


class UpdateMediaItemSchema(UpdateEntitySchema):
    """更新媒体项模式"""
    
    status: Optional[MediaStatus] = Field(None, description="媒体状态")
    file_path: Optional[str] = Field(None, description="文件路径")
    file_name: Optional[str] = Field(None, description="文件名")
    file_size: Optional[int] = Field(None, ge=0, description="文件大小(字节)")
    file_format: Optional[str] = Field(None, description="文件格式")
    width: Optional[int] = Field(None, ge=1, description="宽度")
    height: Optional[int] = Field(None, ge=1, description="高度")
    duration: Optional[float] = Field(None, ge=0.0, description="时长(秒)")
    frame_rate: Optional[float] = Field(None, ge=0.0, description="帧率")
    generation_provider: Optional[GenerationProvider] = Field(None, description="生成服务提供商")
    generation_prompt: Optional[str] = Field(None, description="生成提示词")
    generation_settings: Optional[Dict[str, Any]] = Field(None, description="生成设置")
    generation_metadata: Optional[Dict[str, Any]] = Field(None, description="生成元数据")
    error_message: Optional[str] = Field(None, description="错误信息")
    quality_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="质量评分")
    is_primary: Optional[bool] = Field(None, description="是否为主要媒体")


class MediaGenerationRequest(BaseEntitySchema):
    """媒体生成请求模式"""
    
    shot_id: str = Field(..., description="镜头ID")
    media_type: MediaType = Field(..., description="媒体类型")
    generation_provider: GenerationProvider = Field(..., description="生成服务提供商")
    generation_prompt: str = Field(..., description="生成提示词")
    generation_settings: Optional[Dict[str, Any]] = Field(None, description="生成设置")
    
    @validator('generation_prompt')
    def validate_prompt(cls, v):
        if not v or not v.strip():
            raise ValueError('生成提示词不能为空')
        return v.strip()


class MediaGenerationResponse(BaseEntitySchema):
    """媒体生成响应模式"""
    
    media_id: str = Field(..., description="媒体ID")
    status: MediaStatus = Field(..., description="媒体状态")
    file_path: Optional[str] = Field(None, description="文件路径")
    generation_metadata: Optional[Dict[str, Any]] = Field(None, description="生成元数据")
    error_message: Optional[str] = Field(None, description="错误信息")


# 媒体相关的常量和配置
class MediaConstants:
    """媒体相关常量"""
    
    # 文件大小限制 (字节)
    MAX_FILE_SIZES = {
        MediaType.IMAGE: 50 * 1024 * 1024,    # 50MB
        MediaType.VIDEO: 500 * 1024 * 1024,   # 500MB
        MediaType.AUDIO: 100 * 1024 * 1024,   # 100MB
    }
    
    # 支持的文件格式
    SUPPORTED_FORMATS = {
        MediaType.IMAGE: [".jpg", ".jpeg", ".png", ".webp", ".bmp", ".gif"],
        MediaType.VIDEO: [".mp4", ".avi", ".mov", ".mkv", ".webm", ".flv"],
        MediaType.AUDIO: [".mp3", ".wav", ".flac", ".aac", ".ogg", ".m4a"]
    }
    
    # 默认生成设置
    DEFAULT_GENERATION_SETTINGS = {
        MediaType.IMAGE: {
            "width": 1024,
            "height": 1024,
            "quality": "high",
            "style": "realistic"
        },
        MediaType.VIDEO: {
            "width": 1920,
            "height": 1080,
            "fps": 30,
            "duration": 3.0,
            "quality": "high"
        },
        MediaType.AUDIO: {
            "sample_rate": 44100,
            "channels": 2,
            "format": "mp3",
            "quality": "high"
        }
    }
    
    # 质量评估阈值
    QUALITY_THRESHOLDS = {
        "excellent": 0.9,
        "good": 0.7,
        "fair": 0.5,
        "poor": 0.3
    }
