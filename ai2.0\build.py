#!/usr/bin/env python3
"""构建脚本

使用PyInstaller打包AI视频生成器2.0为可执行文件。
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def check_pyinstaller():
    """检查PyInstaller是否安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller已安装，版本：{PyInstaller.__version__}")
        return True
    except ImportError:
        print("✗ PyInstaller未安装")
        print("正在安装PyInstaller...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print("✓ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError:
            print("✗ PyInstaller安装失败")
            return False


def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('config', 'config'),
        ('resources', 'resources'),
        ('requirements.txt', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui', 
        'PyQt6.QtWidgets',
        'sqlalchemy.dialects.sqlite',
        'aiosqlite',
        'aiohttp',
        'aiofiles',
        'psutil',
        'pydantic',
        'yaml',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='AI视频生成器2.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='resources/icons/app_icon.ico' if Path('resources/icons/app_icon.ico').exists() else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='AI视频生成器2.0',
)
'''
    
    with open('ai_video_generator.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ PyInstaller规格文件已创建")


def create_icon():
    """创建应用程序图标"""
    icon_dir = Path("resources/icons")
    icon_dir.mkdir(parents=True, exist_ok=True)
    
    # 如果没有图标文件，创建一个简单的说明
    ico_path = icon_dir / "app_icon.ico"
    if not ico_path.exists():
        print("ℹ 未找到应用程序图标，建议添加 resources/icons/app_icon.ico")


def build_executable():
    """构建可执行文件"""
    print("正在构建可执行文件...")
    
    try:
        # 使用PyInstaller构建
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm", 
            "ai_video_generator.spec"
        ]
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 构建成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败：{e}")
        print("错误输出：")
        print(e.stderr)
        return False


def create_installer():
    """创建安装包（使用NSIS或Inno Setup）"""
    print("创建安装包...")
    
    # 这里可以添加NSIS或Inno Setup脚本
    installer_script = '''
; AI视频生成器2.0安装脚本
; 使用NSIS创建

!define APP_NAME "AI视频生成器2.0"
!define APP_VERSION "2.0.0"
!define APP_PUBLISHER "AI Video Generator Team"
!define APP_URL "https://github.com/your-repo/ai-video-generator"
!define APP_EXENAME "AI视频生成器2.0.exe"

; 安装程序设置
Name "${APP_NAME}"
OutFile "AI视频生成器2.0_Setup.exe"
InstallDir "$PROGRAMFILES\\${APP_NAME}"
InstallDirRegKey HKLM "Software\\${APP_NAME}" "Install_Dir"
RequestExecutionLevel admin

; 页面
Page components
Page directory
Page instfiles

UninstPage uninstConfirm
UninstPage instfiles

; 安装部分
Section "${APP_NAME} (required)"
  SectionIn RO
  
  ; 设置输出路径
  SetOutPath $INSTDIR
  
  ; 复制文件
  File /r "dist\\AI视频生成器2.0\\*.*"
  
  ; 写入注册表
  WriteRegStr HKLM "Software\\${APP_NAME}" "Install_Dir" "$INSTDIR"
  
  ; 写入卸载信息
  WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "DisplayName" "${APP_NAME}"
  WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "UninstallString" '"$INSTDIR\\uninstall.exe"'
  WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "NoModify" 1
  WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "NoRepair" 1
  WriteUninstaller "uninstall.exe"
  
SectionEnd

; 可选的开始菜单快捷方式
Section "Start Menu Shortcuts"
  CreateDirectory "$SMPROGRAMS\\${APP_NAME}"
  CreateShortcut "$SMPROGRAMS\\${APP_NAME}\\${APP_NAME}.lnk" "$INSTDIR\\${APP_EXENAME}"
  CreateShortcut "$SMPROGRAMS\\${APP_NAME}\\Uninstall.lnk" "$INSTDIR\\uninstall.exe"
SectionEnd

; 卸载部分
Section "Uninstall"
  ; 删除注册表键
  DeleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}"
  DeleteRegKey HKLM "Software\\${APP_NAME}"
  
  ; 删除文件和快捷方式
  Delete "$SMPROGRAMS\\${APP_NAME}\\*.*"
  RMDir "$SMPROGRAMS\\${APP_NAME}"
  
  ; 删除安装目录
  RMDir /r "$INSTDIR"
SectionEnd
'''
    
    with open('installer.nsi', 'w', encoding='utf-8') as f:
        f.write(installer_script)
    
    print("✓ 安装脚本已创建 (installer.nsi)")
    print("ℹ 需要安装NSIS来构建安装包")


def clean_build():
    """清理构建文件"""
    print("清理构建文件...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"✓ 已删除 {dir_name}")
    
    import glob
    for pattern in files_to_clean:
        for file_path in glob.glob(pattern):
            os.remove(file_path)
            print(f"✓ 已删除 {file_path}")


def main():
    """主函数"""
    print("AI视频生成器 2.0 - 构建脚本")
    print("=" * 50)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        return 1
    
    # 创建图标
    create_icon()
    
    # 创建规格文件
    create_spec_file()
    
    # 构建可执行文件
    if not build_executable():
        return 1
    
    # 创建安装包脚本
    create_installer()
    
    print("\n" + "=" * 50)
    print("✓ 构建完成！")
    print(f"可执行文件位置：dist/AI视频生成器2.0/")
    print(f"安装脚本位置：installer.nsi")
    print("\n使用说明：")
    print("1. 可执行文件可以直接运行")
    print("2. 使用NSIS编译installer.nsi创建安装包")
    print("3. 运行 python build.py --clean 清理构建文件")
    
    return 0


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--clean":
        clean_build()
    else:
        sys.exit(main())
