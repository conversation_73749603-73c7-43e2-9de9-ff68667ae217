# AI视频生成器 2.0

## 项目概述

**AI视频生成器 2.0** 是一个集成多种MCP工具的完整AI视频生成解决方案，实现从文本到视频的全自动化生成流程。系统整合了文本处理、语音合成、图像生成、视频生成和社交媒体发布等多种服务，提供端到端的视频创作体验。

## 核心功能

- **文章创作/改写**：利用多种LLM服务生成或改写文章内容
- **五阶段分镜**：自动将文章内容转换为分镜脚本
- **配音生成**：支持多种语音合成服务，生成自然流畅的配音
- **图像生成**：集成多种AI绘图引擎，根据分镜内容生成高质量图像
- **视频生成**：将静态图像转换为动态视频，支持多种视频生成引擎
- **视频合成**：将视频片段和音频合成为完整视频作品
- **多平台发布**：支持一键发布到多个社交媒体平台

## 技术特性

- **智能服务路由**：根据任务需求自动选择最佳服务
- **多语言支持**：支持中英双语内容生成和翻译
- **高度可配置**：灵活的配置系统，支持自定义服务参数
- **错误恢复**：工作流程中的错误处理和恢复机制
- **并行处理**：支持任务并行处理，提高生成效率

## 支持的MCP工具

### 文本处理与创作
- 智谱AI (ChatGLM)
- 通义千问
- Deepseek
- Google Gemini
- 百度翻译

### 语音合成
- Edge TTS
- SiliconFlow

### 图像生成
- Pollinations AI
- ComfyUI
- DALL-E
- Stability AI
- Google Imagen
- CogView-3 Flash

### 视频生成
- CogVideoX-Flash
- Replicate Stable Video Diffusion
- PixVerse AI
- Haiper AI
- Runway ML
- Pika Labs

### 社交媒体发布
- YouTube
- TikTok
- 微信视频号
- 抖音
- B站

## 快速开始

### 环境要求
- Python 3.8+
- Windows/Linux/macOS

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置API密钥

系统会自动从以下位置加载API密钥配置：
- `f:/DAI/config/llm_config.json`
- `f:/DAI/config/baidu_translate_config.py`
- `f:/DAI/config/tts_config.json`
- `f:/DAI/config/image_generation_config.py`
- `f:/DAI/config/video_generation_config.py`

### 运行程序

**快速启动**：
```bash
python start.py
```

**交互模式**：
```bash
python main.py --interactive
```

**命令行模式**：
```bash
python main.py --input "你的文本内容" --title "视频标题" --duration 30
```

**查看帮助**：
```bash
python main.py --help
```

## 使用示例

### 基本视频生成

```bash
python main.py --input "人工智能正在改变我们的世界。从自动驾驶汽车到智能语音助手，AI技术已经深入到我们生活的方方面面。" --title "AI改变世界" --duration 30
```

### 指定输出目录和视频风格

```bash
python main.py --input "探索太空的奥秘" --output "./output/space_video" --style "artistic" --duration 45
```

### 多平台发布

```bash
python main.py --input "最新科技趋势分析" --platforms "youtube,bilibili" --privacy "public"
```

## 工作流程

1. **文章创作/改写**：系统使用LLM服务生成或改写输入的文本内容
2. **五阶段分镜生成**：将文章内容转换为详细的分镜脚本
3. **配音生成**：为每个分镜生成对应的语音配音
4. **图像生成**：根据分镜描述生成相应的图像
5. **视频生成**：将静态图像转换为动态视频片段
6. **视频合成**：将视频片段和音频合成为完整视频
7. **多平台发布**：将生成的视频发布到指定的社交媒体平台

## 项目结构

```
AI视频生成器2.0/
├── main.py                 # 主程序入口
├── start.py               # 快速启动脚本
├── README.md              # 项目说明文档
├── src/
│   ├── core/              # 核心组件
│   │   └── mcp_service_manager.py  # MCP服务管理器
│   ├── services/          # 服务实现
│   │   ├── llm_service.py          # LLM服务
│   │   ├── translation_service.py  # 翻译服务
│   │   ├── tts_service.py          # 语音合成服务
│   │   ├── image_service.py        # 图像生成服务
│   │   ├── video_service.py        # 视频生成服务
│   │   └── social_media_service.py # 社交媒体服务
│   └── workflow/          # 工作流程
│       └── video_generation_workflow.py  # 视频生成工作流程
└── config/                # 配置文件目录
```

## 扩展与定制

系统设计为高度可扩展的架构，可以轻松添加新的服务和功能：

1. **添加新的LLM服务**：扩展 `LLMServiceManager` 类，实现新的LLM客户端
2. **添加新的图像生成引擎**：扩展 `ImageServiceManager` 类，实现新的图像生成客户端
3. **添加新的视频生成引擎**：扩展 `VideoServiceManager` 类，实现新的视频生成客户端
4. **添加新的社交媒体平台**：扩展 `SocialMediaServiceManager` 类，实现新的社交媒体客户端

## 注意事项

- 确保API密钥正确配置
- 视频生成过程可能需要较长时间，请耐心等待
- 部分服务可能需要付费API密钥
- 发布到社交媒体平台需要相应的账号授权

## 许可证

本项目仅供学习和研究使用，请勿用于商业目的。

## 联系方式

如有问题或建议，请提交Issue或联系项目维护者。