"""项目数据模型

定义项目相关的数据模型和模式。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from sqlalchemy import Column, String, Text, Enum, Integer, Float, JSON
from sqlalchemy.orm import relationship
from pydantic import Field, validator

from .base import (
    BaseEntity, BaseEntitySchema, CreateEntitySchema, UpdateEntitySchema,
    ProjectStatus, Constants
)


class Project(BaseEntity):
    """项目模型"""
    
    __tablename__ = "projects"
    
    # 项目基本信息
    title = Column(
        String(Constants.MAX_NAME_LENGTH),
        nullable=False,
        comment="项目标题"
    )
    
    content = Column(
        Text,
        nullable=True,
        comment="项目内容/脚本"
    )
    
    status = Column(
        Enum(ProjectStatus),
        default=ProjectStatus.DRAFT,
        nullable=False,
        comment="项目状态"
    )
    
    # 项目设置
    style = Column(
        String(100),
        default="cinematic",
        nullable=False,
        comment="视觉风格"
    )
    
    language = Column(
        String(10),
        default="zh-CN",
        nullable=False,
        comment="语言"
    )
    
    # 视频设置
    video_width = Column(
        Integer,
        default=1920,
        nullable=False,
        comment="视频宽度"
    )
    
    video_height = Column(
        Integer,
        default=1080,
        nullable=False,
        comment="视频高度"
    )
    
    video_fps = Column(
        Float,
        default=30.0,
        nullable=False,
        comment="视频帧率"
    )
    
    # 生成设置
    generation_settings = Column(
        JSON,
        nullable=True,
        comment="生成设置JSON"
    )
    
    # 统计信息
    total_shots = Column(
        Integer,
        default=0,
        nullable=False,
        comment="总镜头数"
    )
    
    total_duration = Column(
        Float,
        default=0.0,
        nullable=False,
        comment="总时长(秒)"
    )
    
    # 关联关系
    storyboards = relationship(
        "Storyboard",
        back_populates="project",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )
    
    def update_statistics(self) -> None:
        """更新统计信息"""
        from .storyboard import Storyboard
        
        # 计算总镜头数和总时长
        total_shots = 0
        total_duration = 0.0
        
        for storyboard in self.storyboards:
            if not storyboard.is_deleted:
                total_shots += storyboard.shot_count
                total_duration += storyboard.total_duration
        
        self.total_shots = total_shots
        self.total_duration = total_duration
    
    def get_active_storyboards(self) -> List['Storyboard']:
        """获取活跃的分镜"""
        return [sb for sb in self.storyboards if not sb.is_deleted]
    
    def can_add_storyboard(self) -> bool:
        """检查是否可以添加分镜"""
        active_count = len(self.get_active_storyboards())
        return active_count < Constants.MAX_SHOTS_PER_STORYBOARD


# Pydantic模式
class ProjectSchema(BaseEntitySchema):
    """项目模式"""
    
    title: str = Field(..., max_length=Constants.MAX_NAME_LENGTH, description="项目标题")
    content: Optional[str] = Field(None, description="项目内容/脚本")
    status: ProjectStatus = Field(ProjectStatus.DRAFT, description="项目状态")
    style: str = Field("cinematic", max_length=100, description="视觉风格")
    language: str = Field("zh-CN", max_length=10, description="语言")
    video_width: int = Field(1920, ge=480, le=4096, description="视频宽度")
    video_height: int = Field(1080, ge=360, le=2160, description="视频高度")
    video_fps: float = Field(30.0, ge=15.0, le=60.0, description="视频帧率")
    generation_settings: Optional[Dict[str, Any]] = Field(None, description="生成设置")
    total_shots: int = Field(0, ge=0, description="总镜头数")
    total_duration: float = Field(0.0, ge=0.0, description="总时长(秒)")


class CreateProjectSchema(CreateEntitySchema):
    """创建项目模式"""
    
    title: str = Field(..., max_length=Constants.MAX_NAME_LENGTH, description="项目标题")
    content: Optional[str] = Field(None, description="项目内容/脚本")
    style: str = Field("cinematic", max_length=100, description="视觉风格")
    language: str = Field("zh-CN", max_length=10, description="语言")
    video_width: int = Field(1920, ge=480, le=4096, description="视频宽度")
    video_height: int = Field(1080, ge=360, le=2160, description="视频高度")
    video_fps: float = Field(30.0, ge=15.0, le=60.0, description="视频帧率")
    generation_settings: Optional[Dict[str, Any]] = Field(None, description="生成设置")
    
    @validator('title')
    def validate_title(cls, v):
        if not v or not v.strip():
            raise ValueError('项目标题不能为空')
        return v.strip()
    
    @validator('content')
    def validate_content(cls, v):
        if v and len(v) > 10000:  # 限制内容长度
            raise ValueError('项目内容过长，最多10000字符')
        return v


class UpdateProjectSchema(UpdateEntitySchema):
    """更新项目模式"""
    
    title: Optional[str] = Field(None, max_length=Constants.MAX_NAME_LENGTH, description="项目标题")
    content: Optional[str] = Field(None, description="项目内容/脚本")
    status: Optional[ProjectStatus] = Field(None, description="项目状态")
    style: Optional[str] = Field(None, max_length=100, description="视觉风格")
    language: Optional[str] = Field(None, max_length=10, description="语言")
    video_width: Optional[int] = Field(None, ge=480, le=4096, description="视频宽度")
    video_height: Optional[int] = Field(None, ge=360, le=2160, description="视频高度")
    video_fps: Optional[float] = Field(None, ge=15.0, le=60.0, description="视频帧率")
    generation_settings: Optional[Dict[str, Any]] = Field(None, description="生成设置")
    
    @validator('title')
    def validate_title(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('项目标题不能为空')
        return v.strip() if v else v
    
    @validator('content')
    def validate_content(cls, v):
        if v and len(v) > 10000:
            raise ValueError('项目内容过长，最多10000字符')
        return v


class ProjectSummarySchema(BaseEntitySchema):
    """项目摘要模式（用于列表显示）"""
    
    title: str = Field(..., description="项目标题")
    status: ProjectStatus = Field(..., description="项目状态")
    style: str = Field(..., description="视觉风格")
    total_shots: int = Field(..., description="总镜头数")
    total_duration: float = Field(..., description="总时长(秒)")


class ProjectSettingsSchema(BaseEntitySchema):
    """项目设置模式"""
    
    style: str = Field(..., description="视觉风格")
    language: str = Field(..., description="语言")
    video_width: int = Field(..., description="视频宽度")
    video_height: int = Field(..., description="视频高度")
    video_fps: float = Field(..., description="视频帧率")
    generation_settings: Optional[Dict[str, Any]] = Field(None, description="生成设置")


# 项目相关的常量和配置
class ProjectConstants:
    """项目相关常量"""
    
    # 支持的视觉风格
    SUPPORTED_STYLES = [
        "cinematic",
        "anime",
        "realistic",
        "cartoon",
        "artistic",
        "documentary"
    ]
    
    # 支持的语言
    SUPPORTED_LANGUAGES = [
        "zh-CN",
        "en-US",
        "ja-JP",
        "ko-KR"
    ]
    
    # 视频分辨率预设
    VIDEO_RESOLUTIONS = {
        "720p": (1280, 720),
        "1080p": (1920, 1080),
        "1440p": (2560, 1440),
        "4K": (3840, 2160)
    }
    
    # 默认生成设置
    DEFAULT_GENERATION_SETTINGS = {
        "llm_provider": "openai",
        "image_provider": "pollinations",
        "video_provider": "cogvideox",
        "voice_provider": "edge-tts",
        "consistency_check": True,
        "auto_generate_images": True,
        "auto_generate_videos": False,
        "auto_generate_voice": False
    }
