"""分镜数据模型

定义分镜相关的数据模型和模式。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from sqlalchemy import Column, String, Text, Enum, Integer, Float, ForeignKey, JSON, DateTime
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from pydantic import Field, validator

from .base import (
    BaseEntity, BaseEntitySchema, CreateEntitySchema, UpdateEntitySchema,
    StoryboardStatus, Constants
)


class Storyboard(BaseEntity):
    """分镜模型"""
    
    __tablename__ = "storyboards"
    
    # 关联项目
    project_id = Column(
        UUID(as_uuid=True),
        ForeignKey("projects.id", ondelete="CASCADE"),
        nullable=False,
        comment="项目ID"
    )
    
    # 分镜基本信息
    content = Column(
        Text,
        nullable=False,
        comment="分镜内容/脚本"
    )
    
    status = Column(
        Enum(StoryboardStatus),
        default=StoryboardStatus.DRAFT,
        nullable=False,
        comment="分镜状态"
    )
    
    # 生成信息
    generation_prompt = Column(
        Text,
        nullable=True,
        comment="生成提示词"
    )
    
    generation_settings = Column(
        JSON,
        nullable=True,
        comment="生成设置JSON"
    )
    
    generation_metadata = Column(
        JSON,
        nullable=True,
        comment="生成元数据JSON"
    )
    
    # 统计信息
    shot_count = Column(
        Integer,
        default=0,
        nullable=False,
        comment="镜头数量"
    )
    
    total_duration = Column(
        Float,
        default=0.0,
        nullable=False,
        comment="总时长(秒)"
    )
    
    # 处理信息
    processing_started_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="处理开始时间"
    )
    
    processing_completed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="处理完成时间"
    )
    
    error_message = Column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    # 关联关系
    project = relationship(
        "Project",
        back_populates="storyboards"
    )
    
    shots = relationship(
        "Shot",
        back_populates="storyboard",
        cascade="all, delete-orphan",
        lazy="dynamic",
        order_by="Shot.sequence_number"
    )
    
    def update_statistics(self) -> None:
        """更新统计信息"""
        active_shots = [shot for shot in self.shots if not shot.is_deleted]
        self.shot_count = len(active_shots)
        self.total_duration = sum(shot.duration for shot in active_shots)
    
    def get_active_shots(self) -> List['Shot']:
        """获取活跃的镜头"""
        return [shot for shot in self.shots if not shot.is_deleted]
    
    def get_shots_by_sequence(self) -> List['Shot']:
        """按序号获取镜头"""
        return sorted(self.get_active_shots(), key=lambda x: x.sequence_number)
    
    def start_processing(self) -> None:
        """开始处理"""
        self.status = StoryboardStatus.GENERATING
        self.processing_started_at = datetime.utcnow()
        self.processing_completed_at = None
        self.error_message = None
    
    def complete_processing(self) -> None:
        """完成处理"""
        self.status = StoryboardStatus.COMPLETED
        self.processing_completed_at = datetime.utcnow()
        self.error_message = None
    
    def fail_processing(self, error_message: str) -> None:
        """处理失败"""
        self.status = StoryboardStatus.FAILED
        self.processing_completed_at = datetime.utcnow()
        self.error_message = error_message
    
    def get_processing_duration(self) -> Optional[float]:
        """获取处理时长"""
        if self.processing_started_at and self.processing_completed_at:
            delta = self.processing_completed_at - self.processing_started_at
            return delta.total_seconds()
        return None


# Pydantic模式
class StoryboardSchema(BaseEntitySchema):
    """分镜模式"""
    
    project_id: str = Field(..., description="项目ID")
    content: str = Field(..., description="分镜内容/脚本")
    status: StoryboardStatus = Field(StoryboardStatus.DRAFT, description="分镜状态")
    generation_prompt: Optional[str] = Field(None, description="生成提示词")
    generation_settings: Optional[Dict[str, Any]] = Field(None, description="生成设置")
    generation_metadata: Optional[Dict[str, Any]] = Field(None, description="生成元数据")
    shot_count: int = Field(0, ge=0, description="镜头数量")
    total_duration: float = Field(0.0, ge=0.0, description="总时长(秒)")
    processing_started_at: Optional[datetime] = Field(None, description="处理开始时间")
    processing_completed_at: Optional[datetime] = Field(None, description="处理完成时间")
    error_message: Optional[str] = Field(None, description="错误信息")


class CreateStoryboardSchema(CreateEntitySchema):
    """创建分镜模式"""
    
    project_id: str = Field(..., description="项目ID")
    content: str = Field(..., min_length=1, description="分镜内容/脚本")
    generation_settings: Optional[Dict[str, Any]] = Field(None, description="生成设置")
    
    @validator('content')
    def validate_content(cls, v):
        if not v or not v.strip():
            raise ValueError('分镜内容不能为空')
        if len(v) > 50000:  # 限制内容长度
            raise ValueError('分镜内容过长，最多50000字符')
        return v.strip()


class UpdateStoryboardSchema(UpdateEntitySchema):
    """更新分镜模式"""
    
    content: Optional[str] = Field(None, description="分镜内容/脚本")
    status: Optional[StoryboardStatus] = Field(None, description="分镜状态")
    generation_prompt: Optional[str] = Field(None, description="生成提示词")
    generation_settings: Optional[Dict[str, Any]] = Field(None, description="生成设置")
    generation_metadata: Optional[Dict[str, Any]] = Field(None, description="生成元数据")
    error_message: Optional[str] = Field(None, description="错误信息")
    
    @validator('content')
    def validate_content(cls, v):
        if v is not None:
            if not v or not v.strip():
                raise ValueError('分镜内容不能为空')
            if len(v) > 50000:
                raise ValueError('分镜内容过长，最多50000字符')
            return v.strip()
        return v


class StoryboardSummarySchema(BaseEntitySchema):
    """分镜摘要模式（用于列表显示）"""
    
    project_id: str = Field(..., description="项目ID")
    status: StoryboardStatus = Field(..., description="分镜状态")
    shot_count: int = Field(..., description="镜头数量")
    total_duration: float = Field(..., description="总时长(秒)")
    processing_started_at: Optional[datetime] = Field(None, description="处理开始时间")
    processing_completed_at: Optional[datetime] = Field(None, description="处理完成时间")


class StoryboardGenerationRequest(BaseEntitySchema):
    """分镜生成请求模式"""
    
    content: str = Field(..., description="分镜内容/脚本")
    style: str = Field("cinematic", description="视觉风格")
    language: str = Field("zh-CN", description="语言")
    generation_settings: Optional[Dict[str, Any]] = Field(None, description="生成设置")
    
    @validator('content')
    def validate_content(cls, v):
        if not v or not v.strip():
            raise ValueError('分镜内容不能为空')
        return v.strip()


class StoryboardGenerationResponse(BaseEntitySchema):
    """分镜生成响应模式"""
    
    storyboard_id: str = Field(..., description="分镜ID")
    status: StoryboardStatus = Field(..., description="分镜状态")
    shots: List[Dict[str, Any]] = Field(default_factory=list, description="镜头列表")
    generation_metadata: Optional[Dict[str, Any]] = Field(None, description="生成元数据")
    error_message: Optional[str] = Field(None, description="错误信息")


# 分镜相关的常量和配置
class StoryboardConstants:
    """分镜相关常量"""
    
    # 分镜解析阶段
    PARSING_STAGES = [
        "text_analysis",      # 文本分析
        "scene_extraction",   # 场景提取
        "shot_breakdown",     # 镜头分解
        "consistency_check",  # 一致性检查
        "optimization"        # 优化
    ]
    
    # 默认生成设置
    DEFAULT_GENERATION_SETTINGS = {
        "max_shots": 50,
        "default_shot_duration": 3.0,
        "enable_consistency_check": True,
        "auto_optimize": True,
        "include_transitions": True
    }
    
    # 支持的分镜格式
    SUPPORTED_FORMATS = [
        "plain_text",
        "screenplay",
        "novel",
        "outline"
    ]
