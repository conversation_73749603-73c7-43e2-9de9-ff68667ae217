#!/usr/bin/env python3
"""AI视频生成器 2.0 主程序

这是AI视频生成器2.0的主入口文件。
"""

import sys
import asyncio
import signal
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTimer, Qt
from PyQt6.QtGui import QIcon

from src.core.config import ConfigManager
from src.core.database import DatabaseManager
from src.ui.main_window import MainWindow
from src.ui.splash_screen import create_splash_screen
from src.ui.themes import get_theme_manager
from src.services.registry import get_service_registry
from src.utils.logger import configure_logging, get_logger
from src.utils.performance import get_performance_monitor
from src.core.exceptions import ConfigurationError, DatabaseError


class Application:
    """应用程序主类"""
    
    def __init__(self):
        self.app: Optional[QApplication] = None
        self.main_window: Optional[MainWindow] = None
        self.config: Optional[ConfigManager] = None
        self.db_manager: Optional[DatabaseManager] = None
        self.logger = None
        
        # 性能监控
        self.performance_monitor = get_performance_monitor()
    
    async def initialize(self) -> bool:
        """初始化应用程序"""
        try:
            # 1. 初始化配置
            self.config = ConfigManager()
            await self.config.load_config()
            
            # 2. 配置日志
            configure_logging(
                level=self.config.app.log_level,
                console_enabled=self.config.app.debug,
                file_enabled=True,
                log_dir=Path("logs")
            )
            self.logger = get_logger(__name__)
            self.logger.info("Starting AI Video Generator 2.0...")
            
            # 3. 初始化数据库
            self.db_manager = DatabaseManager(self.config.database.url)
            await self.db_manager.initialize()
            
            # 4. 初始化主题系统
            theme_manager = get_theme_manager()
            theme_manager.set_theme(self.config.ui.theme)
            
            # 5. 初始化服务注册表
            service_registry = get_service_registry()
            await service_registry.start_health_monitoring()
            
            self.logger.info("Application initialized successfully")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to initialize application: {e}")
            else:
                print(f"Failed to initialize application: {e}")
            return False
    
    def create_qt_application(self) -> QApplication:
        """创建Qt应用程序"""
        # 设置应用程序属性
        QApplication.setApplicationName("AI视频生成器")
        QApplication.setApplicationVersion("2.0.0")
        QApplication.setOrganizationName("AI Video Generator")
        QApplication.setOrganizationDomain("aivideogenerator.com")
        
        # 创建应用程序实例
        app = QApplication(sys.argv)
        
        # 设置应用程序图标
        icon_path = project_root / "resources" / "icons" / "app_icon.png"
        if icon_path.exists():
            app.setWindowIcon(QIcon(str(icon_path)))
        
        # 设置高DPI支持 (PyQt6中已默认启用)
        
        return app
    
    def show_error_dialog(self, title: str, message: str):
        """显示错误对话框"""
        if self.app:
            QMessageBox.critical(None, title, message)
        else:
            print(f"ERROR - {title}: {message}")
    
    async def run(self) -> int:
        """运行应用程序"""
        try:
            # 创建Qt应用程序
            self.app = self.create_qt_application()
            
            # 创建启动画面
            splash, loading_manager = create_splash_screen()
            splash.show()
            self.app.processEvents()
            
            # 初始化应用程序
            loading_manager.next_step("正在初始化配置...")
            self.app.processEvents()
            
            if not await self.initialize():
                splash.close()
                self.show_error_dialog(
                    "初始化失败", 
                    "应用程序初始化失败，请检查配置和日志文件。"
                )
                return 1
            
            # 创建主窗口
            loading_manager.next_step("正在创建主窗口...")
            self.app.processEvents()
            
            self.main_window = MainWindow(self.config)
            
            # 应用主题
            loading_manager.next_step("正在应用主题...")
            self.app.processEvents()
            
            theme_manager = get_theme_manager()
            self.app.setStyleSheet(theme_manager.current_theme.get_stylesheet())
            
            # 完成加载
            loading_manager.next_step("正在完成启动...")
            self.app.processEvents()
            
            # 等待启动画面完成
            await asyncio.sleep(0.5)
            loading_manager.finish()
            
            # 显示主窗口
            self.main_window.show()
            splash.close()
            
            # 设置信号处理
            self._setup_signal_handlers()
            
            # 启动事件循环
            self.logger.info("Application started successfully")
            return self.app.exec()
            
        except Exception as e:
            error_msg = f"Failed to start application: {e}"
            if self.logger:
                self.logger.error(error_msg)
            self.show_error_dialog("启动失败", error_msg)
            return 1
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, shutting down...")
            self.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 设置定时器处理Ctrl+C
        timer = QTimer()
        timer.start(500)
        timer.timeout.connect(lambda: None)
    
    def shutdown(self):
        """关闭应用程序"""
        if self.logger:
            self.logger.info("Shutting down application...")
        
        try:
            # 关闭主窗口
            if self.main_window:
                self.main_window.close()
            
            # 关闭数据库连接
            if self.db_manager:
                asyncio.create_task(self.db_manager.close())
            
            # 停止服务监控
            service_registry = get_service_registry()
            asyncio.create_task(service_registry.stop_health_monitoring())
            
            # 退出应用程序
            if self.app:
                self.app.quit()
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error during shutdown: {e}")


async def main():
    """主函数"""
    app = Application()
    return await app.run()


def run_app():
    """同步运行应用程序"""
    try:
        # 在Windows上设置事件循环策略
        if sys.platform == "win32":
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        # 运行应用程序
        return asyncio.run(main())
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        return 0
    except Exception as e:
        print(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(run_app())
