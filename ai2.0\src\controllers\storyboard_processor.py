"""分镜生成处理器

实现分镜生成的核心业务逻辑。
"""

from typing import Dict, Any, List, Optional
from uuid import UUID
import json
import asyncio
from datetime import datetime

from src.models import (
    Project, Storyboard, Shot, StoryboardStatus, ShotType,
    CreateStoryboardSchema, CreateShotSchema
)
from src.repositories import ProjectRepository, StoryboardRepository
from src.services import LLMService, ServiceResponse
from src.core.events import get_event_bus, Event, StoryboardEvents
from src.core.exceptions import BusinessLogicError, InvalidStoryboardError
from src.utils.logger import get_logger


class StoryboardProcessor:
    """分镜生成处理器"""
    
    def __init__(
        self,
        project_repo: ProjectRepository,
        storyboard_repo: StoryboardRepository,
        llm_service: LLMService
    ):
        self.project_repo = project_repo
        self.storyboard_repo = storyboard_repo
        self.llm_service = llm_service
        self.event_bus = get_event_bus()
        self.logger = get_logger(__name__)
    
    async def generate_storyboard(
        self,
        project_id: UUID,
        content: str,
        generation_settings: Optional[Dict[str, Any]] = None
    ) -> Storyboard:
        """生成分镜"""
        try:
            # 获取项目信息
            project = await self.project_repo.get_by_id(project_id)
            if not project:
                raise BusinessLogicError(f"Project not found: {project_id}")
            
            # 创建分镜记录
            storyboard_data = CreateStoryboardSchema(
                project_id=str(project_id),
                name=f"分镜 - {datetime.now().strftime('%Y%m%d_%H%M%S')}",
                content=content,
                generation_settings=generation_settings or {}
            )
            
            storyboard = Storyboard(**storyboard_data.dict())
            storyboard = await self.storyboard_repo.create(storyboard)
            
            # 发布开始事件
            await self.event_bus.publish(Event(
                name=StoryboardEvents.GENERATION_STARTED,
                data={
                    'storyboard_id': str(storyboard.id),
                    'project_id': str(project_id),
                    'content_length': len(content)
                }
            ))
            
            # 异步处理分镜生成
            asyncio.create_task(self._process_storyboard_generation(storyboard, project))
            
            return storyboard
            
        except Exception as e:
            self.logger.error(f"Failed to start storyboard generation: {e}")
            raise BusinessLogicError(f"Failed to start storyboard generation: {str(e)}", cause=e)
    
    async def _process_storyboard_generation(self, storyboard: Storyboard, project: Project) -> None:
        """处理分镜生成的异步流程"""
        try:
            # 开始处理
            await self.storyboard_repo.start_processing(storyboard.id)
            
            # 五阶段分镜生成流程
            stages = [
                self._stage_1_text_analysis,
                self._stage_2_scene_extraction,
                self._stage_3_shot_breakdown,
                self._stage_4_consistency_check,
                self._stage_5_optimization
            ]
            
            context = {
                'storyboard': storyboard,
                'project': project,
                'content': storyboard.content,
                'shots': [],
                'metadata': {}
            }
            
            # 逐阶段处理
            for i, stage in enumerate(stages, 1):
                self.logger.info(f"Processing stage {i} for storyboard {storyboard.id}")
                context = await stage(context)
                
                # 更新进度
                progress = (i / len(stages)) * 100
                context['metadata']['progress'] = progress
            
            # 保存生成的镜头
            await self._save_generated_shots(storyboard, context['shots'])
            
            # 完成处理
            await self.storyboard_repo.complete_processing(storyboard.id)
            
            # 更新统计信息
            await self.storyboard_repo.update_statistics(storyboard.id)
            await self.project_repo.update_statistics(project.id)
            
            # 发布完成事件
            await self.event_bus.publish(Event(
                name=StoryboardEvents.GENERATION_COMPLETED,
                data={
                    'storyboard_id': str(storyboard.id),
                    'project_id': str(project.id),
                    'shots_count': len(context['shots']),
                    'metadata': context['metadata']
                }
            ))
            
        except Exception as e:
            self.logger.error(f"Storyboard generation failed: {e}")
            
            # 处理失败
            await self.storyboard_repo.fail_processing(storyboard.id, str(e))
            
            # 发布失败事件
            await self.event_bus.publish(Event(
                name=StoryboardEvents.GENERATION_FAILED,
                data={
                    'storyboard_id': str(storyboard.id),
                    'project_id': str(project.id),
                    'error': str(e)
                }
            ))
    
    async def _stage_1_text_analysis(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """阶段1：文本分析"""
        content = context['content']
        project = context['project']
        
        prompt = f"""
        请分析以下文本内容，提取关键信息：
        
        文本内容：
        {content}
        
        项目风格：{project.style}
        项目语言：{project.language}
        
        请返回JSON格式的分析结果，包含：
        1. 主要角色列表
        2. 主要场景列表
        3. 情节结构
        4. 情感基调
        5. 视觉风格建议
        
        JSON格式：
        {{
            "characters": [
                {{"name": "角色名", "description": "角色描述", "appearance": "外观描述"}}
            ],
            "scenes": [
                {{"name": "场景名", "description": "场景描述", "mood": "情绪氛围"}}
            ],
            "plot_structure": {{"beginning": "开头", "middle": "中间", "end": "结尾"}},
            "emotional_tone": "整体情感基调",
            "visual_style": "视觉风格建议"
        }}
        """
        
        response = await self.llm_service.text_completion(prompt, temperature=0.3)
        
        if response.success:
            try:
                analysis = json.loads(response.data)
                context['metadata']['text_analysis'] = analysis
                self.logger.info(f"Text analysis completed: {len(analysis.get('characters', []))} characters, {len(analysis.get('scenes', []))} scenes")
            except json.JSONDecodeError:
                self.logger.warning("Failed to parse text analysis JSON, using raw response")
                context['metadata']['text_analysis'] = {'raw_response': response.data}
        else:
            raise BusinessLogicError(f"Text analysis failed: {response.error_message}")
        
        return context
    
    async def _stage_2_scene_extraction(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """阶段2：场景提取"""
        content = context['content']
        analysis = context['metadata'].get('text_analysis', {})
        
        prompt = f"""
        基于文本内容和分析结果，提取具体的场景序列：
        
        文本内容：
        {content}
        
        分析结果：
        {json.dumps(analysis, ensure_ascii=False, indent=2)}
        
        请将内容分解为具体的场景序列，每个场景包含：
        1. 场景编号
        2. 场景描述
        3. 主要角色
        4. 关键动作
        5. 对话内容
        6. 情绪氛围
        
        返回JSON格式：
        {{
            "scenes": [
                {{
                    "sequence": 1,
                    "description": "场景描述",
                    "characters": ["角色1", "角色2"],
                    "actions": ["动作1", "动作2"],
                    "dialogue": "对话内容",
                    "mood": "情绪氛围",
                    "duration_estimate": 5.0
                }}
            ]
        }}
        """
        
        response = await self.llm_service.text_completion(prompt, temperature=0.3)
        
        if response.success:
            try:
                scenes = json.loads(response.data)
                context['metadata']['scenes'] = scenes
                self.logger.info(f"Scene extraction completed: {len(scenes.get('scenes', []))} scenes")
            except json.JSONDecodeError:
                self.logger.warning("Failed to parse scene extraction JSON")
                context['metadata']['scenes'] = {'raw_response': response.data}
        else:
            raise BusinessLogicError(f"Scene extraction failed: {response.error_message}")
        
        return context
    
    async def _stage_3_shot_breakdown(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """阶段3：镜头分解"""
        scenes = context['metadata'].get('scenes', {}).get('scenes', [])
        project = context['project']
        
        shots = []
        sequence_number = 1
        
        for scene in scenes:
            prompt = f"""
            将以下场景分解为具体的镜头：
            
            场景信息：
            {json.dumps(scene, ensure_ascii=False, indent=2)}
            
            项目设置：
            - 视觉风格：{project.style}
            - 视频尺寸：{project.video_width}x{project.video_height}
            - 帧率：{project.video_fps}
            
            请将场景分解为2-5个镜头，每个镜头包含：
            1. 镜头类型（wide_shot, medium_shot, close_up等）
            2. 镜头描述
            3. 角色描述
            4. 动作描述
            5. 对话内容
            6. 镜头运动
            7. 光照设置
            8. 色调
            9. 时长
            
            返回JSON格式：
            {{
                "shots": [
                    {{
                        "shot_type": "medium_shot",
                        "content": "镜头内容描述",
                        "character_description": "角色描述",
                        "action": "动作描述",
                        "dialogue": "对话内容",
                        "camera_movement": "镜头运动",
                        "lighting": "光照设置",
                        "color_tone": "色调",
                        "duration": 3.0,
                        "scene_description": "场景描述",
                        "mood_description": "情绪描述"
                    }}
                ]
            }}
            """
            
            response = await self.llm_service.text_completion(prompt, temperature=0.4)
            
            if response.success:
                try:
                    shot_data = json.loads(response.data)
                    for shot in shot_data.get('shots', []):
                        shot['sequence_number'] = sequence_number
                        shots.append(shot)
                        sequence_number += 1
                except json.JSONDecodeError:
                    self.logger.warning(f"Failed to parse shot breakdown for scene {scene.get('sequence')}")
        
        context['shots'] = shots
        context['metadata']['total_shots'] = len(shots)
        self.logger.info(f"Shot breakdown completed: {len(shots)} shots")
        
        return context
    
    async def _stage_4_consistency_check(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """阶段4：一致性检查"""
        shots = context['shots']
        analysis = context['metadata'].get('text_analysis', {})
        
        # 检查角色一致性
        characters = analysis.get('characters', [])
        character_names = [char.get('name') for char in characters]
        
        # 检查场景一致性
        scenes = analysis.get('scenes', [])
        scene_names = [scene.get('name') for scene in scenes]
        
        # 为每个镜头添加一致性标签
        for shot in shots:
            consistency_tags = {
                'characters': [],
                'scenes': [],
                'visual_style': context['project'].style,
                'checked_at': datetime.utcnow().isoformat()
            }
            
            # 检查角色一致性
            for char_name in character_names:
                if char_name.lower() in shot.get('character_description', '').lower():
                    consistency_tags['characters'].append(char_name)
            
            # 检查场景一致性
            for scene_name in scene_names:
                if scene_name.lower() in shot.get('scene_description', '').lower():
                    consistency_tags['scenes'].append(scene_name)
            
            shot['consistency_tags'] = consistency_tags
        
        context['metadata']['consistency_check'] = {
            'total_characters': len(character_names),
            'total_scenes': len(scene_names),
            'checked_shots': len(shots)
        }
        
        self.logger.info("Consistency check completed")
        return context
    
    async def _stage_5_optimization(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """阶段5：优化"""
        shots = context['shots']
        project = context['project']
        
        # 优化镜头时长
        total_duration = sum(shot.get('duration', 3.0) for shot in shots)
        if total_duration > 300:  # 如果总时长超过5分钟，进行压缩
            compression_ratio = 300 / total_duration
            for shot in shots:
                shot['duration'] = max(1.0, shot.get('duration', 3.0) * compression_ratio)
        
        # 生成图像提示词
        for shot in shots:
            image_prompt = self._generate_image_prompt(shot, project)
            shot['image_prompt'] = image_prompt
            
            video_prompt = self._generate_video_prompt(shot, project)
            shot['video_prompt'] = video_prompt
            
            if shot.get('dialogue'):
                voice_prompt = shot['dialogue']
                shot['voice_prompt'] = voice_prompt
        
        context['metadata']['optimization'] = {
            'total_duration': sum(shot.get('duration', 3.0) for shot in shots),
            'optimized_shots': len(shots)
        }
        
        self.logger.info("Optimization completed")
        return context
    
    def _generate_image_prompt(self, shot: Dict[str, Any], project: Project) -> str:
        """生成图像提示词"""
        elements = []
        
        # 基础描述
        if shot.get('content'):
            elements.append(shot['content'])
        
        # 角色描述
        if shot.get('character_description'):
            elements.append(f"Characters: {shot['character_description']}")
        
        # 场景描述
        if shot.get('scene_description'):
            elements.append(f"Scene: {shot['scene_description']}")
        
        # 情绪描述
        if shot.get('mood_description'):
            elements.append(f"Mood: {shot['mood_description']}")
        
        # 光照和色调
        if shot.get('lighting'):
            elements.append(f"Lighting: {shot['lighting']}")
        
        if shot.get('color_tone'):
            elements.append(f"Color tone: {shot['color_tone']}")
        
        # 项目风格
        elements.append(f"Style: {project.style}")
        
        # 镜头类型
        shot_type = shot.get('shot_type', 'medium_shot')
        elements.append(f"Camera: {shot_type.replace('_', ' ')}")
        
        return ", ".join(elements)
    
    def _generate_video_prompt(self, shot: Dict[str, Any], project: Project) -> str:
        """生成视频提示词"""
        elements = []
        
        # 基础描述
        if shot.get('content'):
            elements.append(shot['content'])
        
        # 动作描述
        if shot.get('action'):
            elements.append(f"Action: {shot['action']}")
        
        # 镜头运动
        if shot.get('camera_movement'):
            elements.append(f"Camera movement: {shot['camera_movement']}")
        
        # 项目风格
        elements.append(f"Style: {project.style}")
        
        return ", ".join(elements)
    
    async def _save_generated_shots(self, storyboard: Storyboard, shots: List[Dict[str, Any]]) -> None:
        """保存生成的镜头"""
        from src.repositories.base import AsyncRepository
        from src.models import Shot
        
        # 这里需要注入Shot仓库，暂时直接创建
        # 在实际实现中应该通过依赖注入获取
        shot_repo = AsyncRepository(self.storyboard_repo.session, Shot)
        
        for shot_data in shots:
            shot_schema = CreateShotSchema(
                storyboard_id=str(storyboard.id),
                name=f"镜头 {shot_data.get('sequence_number', 1)}",
                **shot_data
            )
            
            shot = Shot(**shot_schema.dict())
            await shot_repo.create(shot)
