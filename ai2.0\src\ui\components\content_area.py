"""内容区域

中央内容显示区域组件。
"""

from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QStackedWidget,
    QLabel, QPushButton, QFrame, QTextEdit, QScrollArea
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from src.ui.base import BaseWidget, LoadingWidget, ResponsiveWidget
from src.utils.logger import get_logger


class ContentArea(ResponsiveWidget):
    """内容区域"""
    
    # 信号定义
    status_changed = pyqtSignal(str)  # 状态变化信号
    
    def __init__(self, parent=None):
        self._current_page = "dashboard"
        self._pages: Dict[str, QWidget] = {}
        self.stacked_widget = None
        self.loading_widget = None
        self.text_input = None
        super().__init__(parent)
        self.logger = get_logger(__name__)
    
    def _setup_ui(self) -> None:
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建堆叠widget
        self.stacked_widget = QStackedWidget()
        layout.addWidget(self.stacked_widget)
        
        # 创建页面
        self._create_pages()
        
        # 加载指示器
        self.loading_widget = LoadingWidget(self)
        self.loading_widget.hide()
    
    def _create_pages(self) -> None:
        """创建页面"""
        # 仪表板页面
        dashboard_page = self._create_dashboard_page()
        self._add_page("dashboard", dashboard_page)
        
        # 项目管理页面
        projects_page = self._create_projects_page()
        self._add_page("projects", projects_page)
        
        # 分镜生成页面
        storyboard_page = self._create_storyboard_page()
        self._add_page("storyboard", storyboard_page)
        
        # 媒体库页面
        media_page = self._create_media_page()
        self._add_page("media", media_page)
        
        # 设置页面
        settings_page = self._create_settings_page()
        self._add_page("settings", settings_page)
    
    def _add_page(self, name: str, widget: QWidget) -> None:
        """添加页面"""
        self._pages[name] = widget
        self.stacked_widget.addWidget(widget)
    
    def _create_dashboard_page(self) -> QWidget:
        """创建仪表板页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        
        # 标题
        title = QLabel("仪表板")
        title.setProperty("class", "page-title")
        font = QFont()
        font.setPointSize(24)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # 欢迎信息
        welcome_frame = QFrame()
        welcome_frame.setProperty("class", "card")
        welcome_layout = QVBoxLayout(welcome_frame)
        welcome_layout.setContentsMargins(24, 24, 24, 24)
        
        welcome_title = QLabel("欢迎使用AI视频生成器 2.0")
        welcome_title.setProperty("class", "card-title")
        welcome_layout.addWidget(welcome_title)
        
        welcome_text = QLabel(
            "这是一个基于人工智能的视频生成工具，支持智能分镜、"
            "图像生成、语音合成和视频制作。开始您的创作之旅吧！"
        )
        welcome_text.setWordWrap(True)
        welcome_layout.addWidget(welcome_text)
        
        # 快速操作按钮
        button_layout = QHBoxLayout()
        
        new_project_btn = QPushButton("新建项目")
        new_project_btn.setProperty("class", "primary")
        new_project_btn.clicked.connect(self.show_new_project_dialog)
        button_layout.addWidget(new_project_btn)
        
        open_project_btn = QPushButton("打开项目")
        open_project_btn.setProperty("class", "secondary")
        open_project_btn.clicked.connect(self.show_open_project_dialog)
        button_layout.addWidget(open_project_btn)
        
        button_layout.addStretch()
        welcome_layout.addLayout(button_layout)
        
        layout.addWidget(welcome_frame)
        layout.addStretch()
        
        return page
    
    def _create_projects_page(self) -> QWidget:
        """创建项目管理页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        
        title = QLabel("项目管理")
        title.setProperty("class", "page-title")
        font = QFont()
        font.setPointSize(24)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # 项目列表区域
        projects_frame = QFrame()
        projects_frame.setProperty("class", "card")
        projects_layout = QVBoxLayout(projects_frame)
        
        projects_title = QLabel("最近的项目")
        projects_title.setProperty("class", "card-title")
        projects_layout.addWidget(projects_title)
        
        # TODO: 添加项目列表组件
        placeholder = QLabel("暂无项目，点击新建项目开始创作")
        placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder.setProperty("class", "placeholder")
        projects_layout.addWidget(placeholder)
        
        layout.addWidget(projects_frame)
        
        return page
    
    def _create_storyboard_page(self) -> QWidget:
        """创建分镜生成页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        
        title = QLabel("分镜生成")
        title.setProperty("class", "page-title")
        font = QFont()
        font.setPointSize(24)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # 分镜生成区域
        storyboard_frame = QFrame()
        storyboard_frame.setProperty("class", "card")
        storyboard_layout = QVBoxLayout(storyboard_frame)
        
        storyboard_title = QLabel("智能分镜生成")
        storyboard_title.setProperty("class", "card-title")
        storyboard_layout.addWidget(storyboard_title)
        
        # 文本输入区域
        input_label = QLabel("请输入您的故事文本：")
        storyboard_layout.addWidget(input_label)
        
        self.text_input = QTextEdit()
        self.text_input.setPlaceholderText("在这里输入您的故事内容...")
        self.text_input.setMinimumHeight(200)
        storyboard_layout.addWidget(self.text_input)
        
        # 生成按钮
        generate_btn = QPushButton("生成分镜")
        generate_btn.setProperty("class", "primary")
        generate_btn.clicked.connect(self.generate_storyboard)
        storyboard_layout.addWidget(generate_btn)
        
        layout.addWidget(storyboard_frame)
        
        return page
    
    def _create_media_page(self) -> QWidget:
        """创建媒体库页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        
        title = QLabel("媒体库")
        title.setProperty("class", "page-title")
        font = QFont()
        font.setPointSize(24)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # 媒体库区域
        media_frame = QFrame()
        media_frame.setProperty("class", "card")
        media_layout = QVBoxLayout(media_frame)
        
        media_title = QLabel("生成的媒体文件")
        media_title.setProperty("class", "card-title")
        media_layout.addWidget(media_title)
        
        # TODO: 添加媒体文件列表组件
        placeholder = QLabel("暂无媒体文件")
        placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder.setProperty("class", "placeholder")
        media_layout.addWidget(placeholder)
        
        layout.addWidget(media_frame)
        
        return page
    
    def _create_settings_page(self) -> QWidget:
        """创建设置页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        
        title = QLabel("设置")
        title.setProperty("class", "page-title")
        font = QFont()
        font.setPointSize(24)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # 设置区域
        settings_frame = QFrame()
        settings_frame.setProperty("class", "card")
        settings_layout = QVBoxLayout(settings_frame)
        
        settings_title = QLabel("应用程序设置")
        settings_title.setProperty("class", "card-title")
        settings_layout.addWidget(settings_title)
        
        # TODO: 添加设置选项
        placeholder = QLabel("设置选项开发中...")
        placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder.setProperty("class", "placeholder")
        settings_layout.addWidget(placeholder)
        
        layout.addWidget(settings_frame)
        
        return page
    
    def show_page(self, page_name: str) -> None:
        """显示页面"""
        if page_name in self._pages:
            widget = self._pages[page_name]
            self.stacked_widget.setCurrentWidget(widget)
            self._current_page = page_name
            self.logger.debug(f"Switched to page: {page_name}")
    
    def show_new_project_dialog(self) -> None:
        """显示新建项目对话框"""
        self.status_changed.emit("新建项目...")
        # TODO: 实现新建项目对话框
        self.logger.info("New project dialog requested")
    
    def show_open_project_dialog(self) -> None:
        """显示打开项目对话框"""
        self.status_changed.emit("打开项目...")
        # TODO: 实现打开项目对话框
        self.logger.info("Open project dialog requested")
    
    def save_current_project(self) -> None:
        """保存当前项目"""
        self.status_changed.emit("保存项目...")
        # TODO: 实现保存项目功能
        self.logger.info("Save project requested")
    
    def generate_storyboard(self) -> None:
        """生成分镜"""
        text = self.text_input.toPlainText().strip()
        if not text:
            self.status_changed.emit("请输入故事文本")
            return
        
        self.status_changed.emit("正在生成分镜...")
        self.loading_widget.start_loading()
        
        # TODO: 实现分镜生成功能
        self.logger.info(f"Generating storyboard for text: {text[:50]}...")
        
        # 模拟异步处理
        from PyQt6.QtCore import QTimer
        QTimer.singleShot(3000, self._on_storyboard_generated)
    
    def _on_storyboard_generated(self) -> None:
        """分镜生成完成"""
        self.loading_widget.stop_loading()
        self.status_changed.emit("分镜生成完成")
        self.logger.info("Storyboard generation completed")
    
    def _on_breakpoint_changed(self, breakpoint: str) -> None:
        """响应式断点变化处理"""
        if self.is_mobile():
            # 移动端：调整布局为单列
            self._set_mobile_layout()
        else:
            # 桌面端：使用多列布局
            self._set_desktop_layout()

    def _set_mobile_layout(self) -> None:
        """设置移动端布局"""
        # 调整页面内边距
        for page_name, page_widget in self._pages.items():
            if hasattr(page_widget, 'layout'):
                layout = page_widget.layout()
                if layout:
                    layout.setContentsMargins(12, 12, 12, 12)

        # 调整按钮布局为垂直排列
        self._adjust_button_layouts(vertical=True)

    def _set_desktop_layout(self) -> None:
        """设置桌面端布局"""
        # 恢复正常内边距
        for page_name, page_widget in self._pages.items():
            if hasattr(page_widget, 'layout'):
                layout = page_widget.layout()
                if layout:
                    layout.setContentsMargins(24, 24, 24, 24)

        # 调整按钮布局为水平排列
        self._adjust_button_layouts(vertical=False)

    def _adjust_button_layouts(self, vertical: bool) -> None:
        """调整按钮布局方向"""
        # 这里可以根据需要调整具体页面中的按钮布局
        # 例如仪表板页面的快速操作按钮
        pass

    def resizeEvent(self, event) -> None:
        """重写尺寸变化事件"""
        super().resizeEvent(event)
        # 调整加载指示器位置
        if hasattr(self, 'loading_widget'):
            self.loading_widget.resize(self.size())
            self.loading_widget.move(0, 0)
