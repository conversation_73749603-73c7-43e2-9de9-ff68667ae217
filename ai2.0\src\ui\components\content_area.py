"""内容区域

中央内容显示区域组件。
"""

from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QStackedWidget,
    QLabel, QPushButton, QFrame, QTextEdit, QScrollArea,
    QDialog, QLineEdit, QTextEdit, QFormLayout, QDialogButtonBox,
    QFileDialog, QMessageBox, QCheckBox, QComboBox, QSpinBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from src.ui.base import BaseWidget, LoadingWidget, ResponsiveWidget
from src.utils.logger import get_logger


class ContentArea(ResponsiveWidget):
    """内容区域"""
    
    # 信号定义
    status_changed = pyqtSignal(str)  # 状态变化信号
    
    def __init__(self, parent=None):
        self._current_page = "dashboard"
        self._pages: Dict[str, QWidget] = {}
        self.stacked_widget = None
        self.loading_widget = None
        self.text_input = None
        super().__init__(parent)
        self.logger = get_logger(__name__)
    
    def _setup_ui(self) -> None:
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建堆叠widget
        self.stacked_widget = QStackedWidget()
        layout.addWidget(self.stacked_widget)
        
        # 创建页面
        self._create_pages()
        
        # 加载指示器
        self.loading_widget = LoadingWidget(self)
        self.loading_widget.hide()
    
    def _create_pages(self) -> None:
        """创建页面"""
        # 仪表板页面
        dashboard_page = self._create_dashboard_page()
        self._add_page("dashboard", dashboard_page)
        
        # 项目管理页面
        projects_page = self._create_projects_page()
        self._add_page("projects", projects_page)
        
        # 分镜生成页面
        storyboard_page = self._create_storyboard_page()
        self._add_page("storyboard", storyboard_page)
        
        # 媒体库页面
        media_page = self._create_media_page()
        self._add_page("media", media_page)
        
        # 设置页面
        settings_page = self._create_settings_page()
        self._add_page("settings", settings_page)
    
    def _add_page(self, name: str, widget: QWidget) -> None:
        """添加页面"""
        self._pages[name] = widget
        self.stacked_widget.addWidget(widget)
    
    def _create_dashboard_page(self) -> QWidget:
        """创建仪表板页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        
        # 标题
        title = QLabel("仪表板")
        title.setProperty("class", "page-title")
        font = QFont()
        font.setPointSize(24)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # 欢迎信息
        welcome_frame = QFrame()
        welcome_frame.setProperty("class", "card")
        welcome_layout = QVBoxLayout(welcome_frame)
        welcome_layout.setContentsMargins(24, 24, 24, 24)
        
        welcome_title = QLabel("欢迎使用AI视频生成器 2.0")
        welcome_title.setProperty("class", "card-title")
        welcome_layout.addWidget(welcome_title)
        
        welcome_text = QLabel(
            "这是一个基于人工智能的视频生成工具，支持智能分镜、"
            "图像生成、语音合成和视频制作。开始您的创作之旅吧！"
        )
        welcome_text.setWordWrap(True)
        welcome_layout.addWidget(welcome_text)
        
        # 快速操作按钮
        button_layout = QHBoxLayout()
        
        new_project_btn = QPushButton("新建项目")
        new_project_btn.setProperty("class", "primary")
        new_project_btn.clicked.connect(self.show_new_project_dialog)
        button_layout.addWidget(new_project_btn)
        
        open_project_btn = QPushButton("打开项目")
        open_project_btn.setProperty("class", "secondary")
        open_project_btn.clicked.connect(self.show_open_project_dialog)
        button_layout.addWidget(open_project_btn)
        
        button_layout.addStretch()
        welcome_layout.addLayout(button_layout)
        
        layout.addWidget(welcome_frame)
        layout.addStretch()
        
        return page
    
    def _create_projects_page(self) -> QWidget:
        """创建项目管理页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        
        title = QLabel("项目管理")
        title.setProperty("class", "page-title")
        font = QFont()
        font.setPointSize(24)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # 项目列表区域
        projects_frame = QFrame()
        projects_frame.setProperty("class", "card")
        projects_layout = QVBoxLayout(projects_frame)
        
        projects_title = QLabel("最近的项目")
        projects_title.setProperty("class", "card-title")
        projects_layout.addWidget(projects_title)
        
        # TODO: 添加项目列表组件
        placeholder = QLabel("暂无项目，点击新建项目开始创作")
        placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder.setProperty("class", "placeholder")
        projects_layout.addWidget(placeholder)
        
        layout.addWidget(projects_frame)
        
        return page
    
    def _create_storyboard_page(self) -> QWidget:
        """创建分镜生成页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        
        title = QLabel("分镜生成")
        title.setProperty("class", "page-title")
        font = QFont()
        font.setPointSize(24)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # 分镜生成区域
        storyboard_frame = QFrame()
        storyboard_frame.setProperty("class", "card")
        storyboard_layout = QVBoxLayout(storyboard_frame)
        
        storyboard_title = QLabel("智能分镜生成")
        storyboard_title.setProperty("class", "card-title")
        storyboard_layout.addWidget(storyboard_title)
        
        # 文本输入区域
        input_label = QLabel("请输入您的故事文本：")
        storyboard_layout.addWidget(input_label)
        
        self.text_input = QTextEdit()
        self.text_input.setPlaceholderText("在这里输入您的故事内容...")
        self.text_input.setMinimumHeight(200)
        storyboard_layout.addWidget(self.text_input)
        
        # 生成按钮
        generate_btn = QPushButton("生成分镜")
        generate_btn.setProperty("class", "primary")
        generate_btn.clicked.connect(self.generate_storyboard)
        storyboard_layout.addWidget(generate_btn)
        
        layout.addWidget(storyboard_frame)
        
        return page
    
    def _create_media_page(self) -> QWidget:
        """创建媒体库页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        
        title = QLabel("媒体库")
        title.setProperty("class", "page-title")
        font = QFont()
        font.setPointSize(24)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # 媒体库区域
        media_frame = QFrame()
        media_frame.setProperty("class", "card")
        media_layout = QVBoxLayout(media_frame)
        
        media_title = QLabel("生成的媒体文件")
        media_title.setProperty("class", "card-title")
        media_layout.addWidget(media_title)
        
        # TODO: 添加媒体文件列表组件
        placeholder = QLabel("暂无媒体文件")
        placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder.setProperty("class", "placeholder")
        media_layout.addWidget(placeholder)
        
        layout.addWidget(media_frame)
        
        return page
    
    def _create_settings_page(self) -> QWidget:
        """创建设置页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        
        title = QLabel("设置")
        title.setProperty("class", "page-title")
        font = QFont()
        font.setPointSize(24)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # 设置区域
        settings_frame = QFrame()
        settings_frame.setProperty("class", "card")
        settings_layout = QVBoxLayout(settings_frame)

        settings_title = QLabel("应用程序设置")
        settings_title.setProperty("class", "card-title")
        settings_layout.addWidget(settings_title)

        # 设置表单
        form_layout = QFormLayout()

        # 主题设置
        theme_combo = QComboBox()
        theme_combo.addItems(["浅色主题", "深色主题"])
        theme_combo.currentTextChanged.connect(self._on_theme_changed)
        form_layout.addRow("界面主题:", theme_combo)

        # 语言设置
        language_combo = QComboBox()
        language_combo.addItems(["中文", "English"])
        form_layout.addRow("界面语言:", language_combo)

        # 自动保存
        auto_save_check = QCheckBox("启用自动保存")
        auto_save_check.setChecked(True)
        form_layout.addRow("", auto_save_check)

        # 保存间隔
        save_interval_spin = QSpinBox()
        save_interval_spin.setRange(1, 60)
        save_interval_spin.setValue(5)
        save_interval_spin.setSuffix(" 分钟")
        form_layout.addRow("保存间隔:", save_interval_spin)

        settings_layout.addLayout(form_layout)

        # 保存按钮
        save_settings_btn = QPushButton("保存设置")
        save_settings_btn.setProperty("class", "primary")
        save_settings_btn.clicked.connect(self._save_settings)
        settings_layout.addWidget(save_settings_btn)

        layout.addWidget(settings_frame)
        
        return page
    
    def show_page(self, page_name: str) -> None:
        """显示页面"""
        if page_name in self._pages:
            widget = self._pages[page_name]
            self.stacked_widget.setCurrentWidget(widget)
            self._current_page = page_name
            self.logger.debug(f"Switched to page: {page_name}")
    
    def show_new_project_dialog(self) -> None:
        """显示新建项目对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle("新建项目")
        dialog.setModal(True)
        dialog.resize(400, 300)

        layout = QVBoxLayout(dialog)

        # 表单布局
        form_layout = QFormLayout()

        # 项目名称
        name_edit = QLineEdit()
        name_edit.setPlaceholderText("请输入项目名称")
        form_layout.addRow("项目名称:", name_edit)

        # 项目描述
        desc_edit = QTextEdit()
        desc_edit.setPlaceholderText("请输入项目描述（可选）")
        desc_edit.setMaximumHeight(80)
        form_layout.addRow("项目描述:", desc_edit)

        # 故事内容
        content_edit = QTextEdit()
        content_edit.setPlaceholderText("请输入故事内容或脚本")
        form_layout.addRow("故事内容:", content_edit)

        layout.addLayout(form_layout)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # 显示对话框
        if dialog.exec() == QDialog.DialogCode.Accepted:
            project_name = name_edit.text().strip()
            if project_name:
                self.status_changed.emit(f"创建项目: {project_name}")
                self.logger.info(f"Creating new project: {project_name}")
                QMessageBox.information(self, "成功", f"项目 '{project_name}' 创建成功！")
            else:
                QMessageBox.warning(self, "警告", "请输入项目名称！")

        self.logger.info("New project dialog completed")
    
    def show_open_project_dialog(self) -> None:
        """显示打开项目对话框"""
        file_dialog = QFileDialog(self)
        file_dialog.setWindowTitle("打开项目")
        file_dialog.setFileMode(QFileDialog.FileMode.ExistingFile)
        file_dialog.setNameFilter("项目文件 (*.json *.avp);;所有文件 (*)")
        file_dialog.setDirectory("./projects")  # 默认项目目录

        if file_dialog.exec() == QDialog.DialogCode.Accepted:
            selected_files = file_dialog.selectedFiles()
            if selected_files:
                project_file = selected_files[0]
                self.status_changed.emit(f"打开项目: {project_file}")
                self.logger.info(f"Opening project: {project_file}")
                QMessageBox.information(self, "成功", f"项目文件 '{project_file}' 打开成功！")

        self.logger.info("Open project dialog completed")
    
    def save_current_project(self) -> None:
        """保存当前项目"""
        self.status_changed.emit("保存项目...")
        # TODO: 实现保存项目功能
        self.logger.info("Save project requested")
    
    def generate_storyboard(self) -> None:
        """生成分镜"""
        text = self.text_input.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "警告", "请输入故事文本！")
            self.status_changed.emit("请输入故事文本")
            return

        if len(text) < 10:
            QMessageBox.warning(self, "警告", "故事文本太短，请输入更详细的内容！")
            return

        self.status_changed.emit("正在分析故事内容...")
        self.loading_widget.start_loading()

        # 显示进度信息
        QMessageBox.information(self, "开始生成", f"开始为您的故事生成分镜脚本...\n\n故事长度: {len(text)} 字符")

        self.logger.info(f"Generating storyboard for text: {text[:50]}...")

        # 模拟异步处理
        from PyQt6.QtCore import QTimer
        QTimer.singleShot(3000, self._on_storyboard_generated)
    
    def _on_storyboard_generated(self) -> None:
        """分镜生成完成"""
        self.loading_widget.stop_loading()
        self.status_changed.emit("分镜生成完成")

        # 显示生成结果
        result_text = """分镜生成完成！

生成的分镜包括：
• 场景1: 开场介绍 (5秒)
• 场景2: 主要内容 (20秒)
• 场景3: 结尾总结 (5秒)

您可以在媒体库中查看生成的内容，或继续编辑分镜脚本。"""

        QMessageBox.information(self, "生成完成", result_text)
        self.logger.info("Storyboard generation completed")

    def _on_theme_changed(self, theme_text: str) -> None:
        """主题变化处理"""
        theme_name = "light" if theme_text == "浅色主题" else "dark"
        try:
            from src.ui.themes import get_theme_manager
            theme_manager = get_theme_manager()
            theme_manager.set_theme(theme_name)
            self.status_changed.emit(f"已切换到{theme_text}")
            self.logger.info(f"Theme changed to: {theme_name}")
        except Exception as e:
            self.logger.error(f"Failed to change theme: {e}")
            QMessageBox.warning(self, "错误", f"主题切换失败: {e}")

    def _save_settings(self) -> None:
        """保存设置"""
        self.status_changed.emit("正在保存设置...")
        # TODO: 实现设置保存功能
        QMessageBox.information(self, "成功", "设置已保存！")
        self.logger.info("Settings saved")
    
    def _on_breakpoint_changed(self, breakpoint: str) -> None:
        """响应式断点变化处理"""
        if self.is_mobile():
            # 移动端：调整布局为单列
            self._set_mobile_layout()
        else:
            # 桌面端：使用多列布局
            self._set_desktop_layout()

    def _set_mobile_layout(self) -> None:
        """设置移动端布局"""
        # 调整页面内边距
        for page_name, page_widget in self._pages.items():
            if hasattr(page_widget, 'layout'):
                layout = page_widget.layout()
                if layout:
                    layout.setContentsMargins(12, 12, 12, 12)

        # 调整按钮布局为垂直排列
        self._adjust_button_layouts(vertical=True)

    def _set_desktop_layout(self) -> None:
        """设置桌面端布局"""
        # 恢复正常内边距
        for page_name, page_widget in self._pages.items():
            if hasattr(page_widget, 'layout'):
                layout = page_widget.layout()
                if layout:
                    layout.setContentsMargins(24, 24, 24, 24)

        # 调整按钮布局为水平排列
        self._adjust_button_layouts(vertical=False)

    def _adjust_button_layouts(self, vertical: bool) -> None:
        """调整按钮布局方向"""
        # 这里可以根据需要调整具体页面中的按钮布局
        # 例如仪表板页面的快速操作按钮
        pass

    def resizeEvent(self, event) -> None:
        """重写尺寸变化事件"""
        super().resizeEvent(event)
        # 调整加载指示器位置
        if hasattr(self, 'loading_widget'):
            self.loading_widget.resize(self.size())
            self.loading_widget.move(0, 0)
