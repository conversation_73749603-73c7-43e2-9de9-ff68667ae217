# -*- coding: utf-8 -*-
"""
AI视频生成器2.0 - 服务测试脚本
用于测试各个MCP服务的连接状态和功能
"""

import asyncio
import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入服务管理器
from src.services.llm_service import LLMServiceManager
from src.services.translation_service import TranslationServiceManager
from src.services.tts_service import TTSServiceManager
from src.services.image_service import ImageServiceManager
from src.services.video_service import VideoServiceManager
from src.services.social_media_service import SocialMediaServiceManager
from main import AIVideoGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class ServiceTester:
    """服务测试器"""
    
    def __init__(self):
        self.generator = AIVideoGenerator()
        self.test_results = {}
    
    async def test_all_services(self) -> Dict[str, Any]:
        """测试所有服务"""
        print("🧪 开始测试AI视频生成器2.0服务...\n")
        
        # 初始化生成器
        print("📋 正在初始化服务...")
        init_success = await self.generator.initialize()
        
        if not init_success:
            print("❌ 服务初始化失败")
            return {'initialized': False, 'error': '服务初始化失败'}
        
        print("✅ 服务初始化成功\n")
        
        # 测试各个服务
        await self.test_llm_service()
        await self.test_translation_service()
        await self.test_tts_service()
        await self.test_image_service()
        await self.test_video_service()
        await self.test_social_media_service()
        
        # 清理资源
        await self.generator.cleanup()
        
        return self.test_results
    
    async def test_llm_service(self):
        """测试LLM服务"""
        print("🤖 测试LLM服务...")
        
        try:
            if not hasattr(self.generator.workflow, 'llm_manager') or not self.generator.workflow.llm_manager:
                self.test_results['llm'] = {'status': 'not_configured', 'message': 'LLM服务未配置'}
                print("  ⚠️  LLM服务未配置")
                return
            
            llm_manager = self.generator.workflow.llm_manager
            clients = llm_manager.get_all_clients()
            
            if not clients:
                self.test_results['llm'] = {'status': 'no_clients', 'message': '没有可用的LLM客户端'}
                print("  ⚠️  没有可用的LLM客户端")
                return
            
            # 测试文本生成
            test_prompt = "请简单介绍一下人工智能。"
            
            for client in clients[:2]:  # 只测试前两个客户端
                try:
                    print(f"  🔍 测试 {client.name}...")
                    
                    from src.services.llm_service import LLMRequest
                    request = LLMRequest(
                        prompt=test_prompt,
                        max_tokens=100,
                        temperature=0.7
                    )
                    
                    response = await client.process_request(request)
                    
                    if response.success and response.content:
                        print(f"    ✅ {client.name} 测试成功")
                        print(f"    📝 响应: {response.content[:50]}...")
                    else:
                        print(f"    ❌ {client.name} 测试失败: {response.error}")
                        
                except Exception as e:
                    print(f"    ❌ {client.name} 测试异常: {e}")
            
            self.test_results['llm'] = {
                'status': 'tested',
                'available_clients': len(clients),
                'client_names': [client.name for client in clients]
            }
            
        except Exception as e:
            self.test_results['llm'] = {'status': 'error', 'error': str(e)}
            print(f"  ❌ LLM服务测试异常: {e}")
        
        print()
    
    async def test_translation_service(self):
        """测试翻译服务"""
        print("🌐 测试翻译服务...")
        
        try:
            if not hasattr(self.generator.workflow, 'translation_manager') or not self.generator.workflow.translation_manager:
                self.test_results['translation'] = {'status': 'not_configured', 'message': '翻译服务未配置'}
                print("  ⚠️  翻译服务未配置")
                return
            
            translation_manager = self.generator.workflow.translation_manager
            clients = translation_manager.get_all_clients()
            
            if not clients:
                self.test_results['translation'] = {'status': 'no_clients', 'message': '没有可用的翻译客户端'}
                print("  ⚠️  没有可用的翻译客户端")
                return
            
            # 测试翻译
            test_text = "Hello, world!"
            
            for client in clients[:1]:  # 只测试第一个客户端
                try:
                    print(f"  🔍 测试 {client.name}...")
                    
                    from src.services.translation_service import TranslationRequest
                    request = TranslationRequest(
                        text=test_text,
                        source_language="en",
                        target_language="zh"
                    )
                    
                    response = await client.process_request(request)
                    
                    if response.success and response.translated_text:
                        print(f"    ✅ {client.name} 测试成功")
                        print(f"    📝 翻译结果: {response.translated_text}")
                    else:
                        print(f"    ❌ {client.name} 测试失败: {response.error}")
                        
                except Exception as e:
                    print(f"    ❌ {client.name} 测试异常: {e}")
            
            self.test_results['translation'] = {
                'status': 'tested',
                'available_clients': len(clients),
                'client_names': [client.name for client in clients]
            }
            
        except Exception as e:
            self.test_results['translation'] = {'status': 'error', 'error': str(e)}
            print(f"  ❌ 翻译服务测试异常: {e}")
        
        print()
    
    async def test_tts_service(self):
        """测试TTS服务"""
        print("🔊 测试TTS服务...")
        
        try:
            if not hasattr(self.generator.workflow, 'tts_manager') or not self.generator.workflow.tts_manager:
                self.test_results['tts'] = {'status': 'not_configured', 'message': 'TTS服务未配置'}
                print("  ⚠️  TTS服务未配置")
                return
            
            tts_manager = self.generator.workflow.tts_manager
            clients = tts_manager.get_all_clients()
            
            if not clients:
                self.test_results['tts'] = {'status': 'no_clients', 'message': '没有可用的TTS客户端'}
                print("  ⚠️  没有可用的TTS客户端")
                return
            
            # 测试语音合成
            test_text = "这是一个测试语音。"
            
            for client in clients[:1]:  # 只测试第一个客户端
                try:
                    print(f"  🔍 测试 {client.name}...")
                    
                    from src.services.tts_service import TTSRequest
                    request = TTSRequest(
                        text=test_text,
                        voice="zh-CN-XiaoxiaoNeural",
                        output_file="test_audio.wav"
                    )
                    
                    response = await client.process_request(request)
                    
                    if response.success and response.audio_file:
                        print(f"    ✅ {client.name} 测试成功")
                        print(f"    🎵 音频文件: {response.audio_file}")
                        
                        # 清理测试文件
                        import os
                        if os.path.exists(response.audio_file):
                            os.remove(response.audio_file)
                    else:
                        print(f"    ❌ {client.name} 测试失败: {response.error}")
                        
                except Exception as e:
                    print(f"    ❌ {client.name} 测试异常: {e}")
            
            self.test_results['tts'] = {
                'status': 'tested',
                'available_clients': len(clients),
                'client_names': [client.name for client in clients]
            }
            
        except Exception as e:
            self.test_results['tts'] = {'status': 'error', 'error': str(e)}
            print(f"  ❌ TTS服务测试异常: {e}")
        
        print()
    
    async def test_image_service(self):
        """测试图像生成服务"""
        print("🎨 测试图像生成服务...")
        
        try:
            if not hasattr(self.generator.workflow, 'image_manager') or not self.generator.workflow.image_manager:
                self.test_results['image'] = {'status': 'not_configured', 'message': '图像生成服务未配置'}
                print("  ⚠️  图像生成服务未配置")
                return
            
            image_manager = self.generator.workflow.image_manager
            clients = image_manager.get_all_clients()
            
            if not clients:
                self.test_results['image'] = {'status': 'no_clients', 'message': '没有可用的图像生成客户端'}
                print("  ⚠️  没有可用的图像生成客户端")
                return
            
            # 测试图像生成（仅测试连接，不实际生成）
            for client in clients[:2]:  # 只测试前两个客户端
                try:
                    print(f"  🔍 测试 {client.name} 连接...")
                    
                    # 这里只测试客户端是否正确初始化，不实际生成图像
                    if hasattr(client, 'name') and client.name:
                        print(f"    ✅ {client.name} 连接正常")
                    else:
                        print(f"    ❌ {client.name} 连接异常")
                        
                except Exception as e:
                    print(f"    ❌ {client.name} 测试异常: {e}")
            
            self.test_results['image'] = {
                'status': 'tested',
                'available_clients': len(clients),
                'client_names': [client.name for client in clients]
            }
            
        except Exception as e:
            self.test_results['image'] = {'status': 'error', 'error': str(e)}
            print(f"  ❌ 图像生成服务测试异常: {e}")
        
        print()
    
    async def test_video_service(self):
        """测试视频生成服务"""
        print("🎬 测试视频生成服务...")
        
        try:
            if not hasattr(self.generator.workflow, 'video_manager') or not self.generator.workflow.video_manager:
                self.test_results['video'] = {'status': 'not_configured', 'message': '视频生成服务未配置'}
                print("  ⚠️  视频生成服务未配置")
                return
            
            video_manager = self.generator.workflow.video_manager
            clients = video_manager.get_all_clients()
            
            if not clients:
                self.test_results['video'] = {'status': 'no_clients', 'message': '没有可用的视频生成客户端'}
                print("  ⚠️  没有可用的视频生成客户端")
                return
            
            # 测试视频生成（仅测试连接，不实际生成）
            for client in clients[:1]:  # 只测试第一个客户端
                try:
                    print(f"  🔍 测试 {client.name} 连接...")
                    
                    # 这里只测试客户端是否正确初始化，不实际生成视频
                    if hasattr(client, 'name') and client.name:
                        print(f"    ✅ {client.name} 连接正常")
                    else:
                        print(f"    ❌ {client.name} 连接异常")
                        
                except Exception as e:
                    print(f"    ❌ {client.name} 测试异常: {e}")
            
            self.test_results['video'] = {
                'status': 'tested',
                'available_clients': len(clients),
                'client_names': [client.name for client in clients]
            }
            
        except Exception as e:
            self.test_results['video'] = {'status': 'error', 'error': str(e)}
            print(f"  ❌ 视频生成服务测试异常: {e}")
        
        print()
    
    async def test_social_media_service(self):
        """测试社交媒体服务"""
        print("📱 测试社交媒体服务...")
        
        try:
            if not hasattr(self.generator.workflow, 'social_media_manager') or not self.generator.workflow.social_media_manager:
                self.test_results['social_media'] = {'status': 'not_configured', 'message': '社交媒体服务未配置'}
                print("  ⚠️  社交媒体服务未配置")
                return
            
            social_media_manager = self.generator.workflow.social_media_manager
            platforms = social_media_manager.get_available_platforms()
            
            if not platforms:
                self.test_results['social_media'] = {'status': 'no_platforms', 'message': '没有配置的社交媒体平台'}
                print("  ⚠️  没有配置的社交媒体平台")
                return
            
            print(f"  📋 可用平台: {', '.join(platforms)}")
            
            self.test_results['social_media'] = {
                'status': 'tested',
                'available_platforms': platforms
            }
            
        except Exception as e:
            self.test_results['social_media'] = {'status': 'error', 'error': str(e)}
            print(f"  ❌ 社交媒体服务测试异常: {e}")
        
        print()
    
    def print_summary(self):
        """打印测试摘要"""
        print("📊 测试摘要:")
        print("=" * 50)
        
        for service_name, result in self.test_results.items():
            status = result.get('status', 'unknown')
            
            if status == 'tested':
                print(f"✅ {service_name.upper()}: 测试完成")
                if 'available_clients' in result:
                    print(f"   可用客户端: {result['available_clients']} 个")
                    print(f"   客户端列表: {', '.join(result['client_names'])}")
                elif 'available_platforms' in result:
                    print(f"   可用平台: {', '.join(result['available_platforms'])}")
            elif status == 'not_configured':
                print(f"⚠️  {service_name.upper()}: 未配置")
            elif status == 'no_clients':
                print(f"⚠️  {service_name.upper()}: 没有可用客户端")
            elif status == 'no_platforms':
                print(f"⚠️  {service_name.upper()}: 没有配置平台")
            elif status == 'error':
                print(f"❌ {service_name.upper()}: 测试失败")
                print(f"   错误: {result.get('error', '未知错误')}")
            
            print()

async def main():
    """主函数"""
    print("🧪 AI视频生成器2.0 服务测试")
    print("=" * 50)
    
    tester = ServiceTester()
    
    try:
        results = await tester.test_all_services()
        tester.print_summary()
        
        # 保存测试结果
        with open('test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print("📄 测试结果已保存到 test_results.json")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行测试
    asyncio.run(main())