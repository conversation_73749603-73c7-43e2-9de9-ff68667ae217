"""镜头数据模型

定义镜头相关的数据模型和模式。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from sqlalchemy import Column, String, Text, Enum, Integer, Float, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from pydantic import Field, validator

from .base import (
    BaseEntity, BaseEntitySchema, CreateEntitySchema, UpdateEntitySchema,
    ShotType, Constants
)


class Shot(BaseEntity):
    """镜头模型"""
    
    __tablename__ = "shots"
    
    # 关联分镜
    storyboard_id = Column(
        UUID(as_uuid=True),
        ForeignKey("storyboards.id", ondelete="CASCADE"),
        nullable=False,
        comment="分镜ID"
    )
    
    # 镜头基本信息
    sequence_number = Column(
        Integer,
        nullable=False,
        comment="序号"
    )
    
    shot_type = Column(
        Enum(ShotType),
        default=ShotType.MEDIUM_SHOT,
        nullable=False,
        comment="镜头类型"
    )
    
    duration = Column(
        Float,
        default=Constants.DEFAULT_SHOT_DURATION,
        nullable=False,
        comment="时长(秒)"
    )
    
    # 镜头内容
    content = Column(
        Text,
        nullable=False,
        comment="镜头内容描述"
    )
    
    dialogue = Column(
        Text,
        nullable=True,
        comment="对话内容"
    )
    
    action = Column(
        Text,
        nullable=True,
        comment="动作描述"
    )
    
    # 视觉描述
    scene_description = Column(
        Text,
        nullable=True,
        comment="场景描述"
    )
    
    character_description = Column(
        Text,
        nullable=True,
        comment="角色描述"
    )
    
    mood_description = Column(
        Text,
        nullable=True,
        comment="情绪描述"
    )
    
    # 技术参数
    camera_movement = Column(
        String(100),
        nullable=True,
        comment="镜头运动"
    )
    
    lighting = Column(
        String(100),
        nullable=True,
        comment="光照"
    )
    
    color_tone = Column(
        String(100),
        nullable=True,
        comment="色调"
    )
    
    # 生成信息
    image_prompt = Column(
        Text,
        nullable=True,
        comment="图像生成提示词"
    )
    
    video_prompt = Column(
        Text,
        nullable=True,
        comment="视频生成提示词"
    )
    
    voice_prompt = Column(
        Text,
        nullable=True,
        comment="语音生成提示词"
    )
    
    # 一致性信息
    consistency_tags = Column(
        JSON,
        nullable=True,
        comment="一致性标签JSON"
    )
    
    character_references = Column(
        JSON,
        nullable=True,
        comment="角色参考JSON"
    )
    
    scene_references = Column(
        JSON,
        nullable=True,
        comment="场景参考JSON"
    )
    
    # 元数据
    generation_metadata = Column(
        JSON,
        nullable=True,
        comment="生成元数据JSON"
    )
    
    # 关联关系
    storyboard = relationship(
        "Storyboard",
        back_populates="shots"
    )
    
    media_items = relationship(
        "MediaItem",
        back_populates="shot",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )
    
    def get_media_by_type(self, media_type: str) -> List['MediaItem']:
        """根据类型获取媒体项"""
        return [item for item in self.media_items if item.media_type == media_type and not item.is_deleted]
    
    def get_primary_image(self) -> Optional['MediaItem']:
        """获取主要图像"""
        images = self.get_media_by_type("image")
        return images[0] if images else None
    
    def get_primary_video(self) -> Optional['MediaItem']:
        """获取主要视频"""
        videos = self.get_media_by_type("video")
        return videos[0] if videos else None
    
    def get_primary_audio(self) -> Optional['MediaItem']:
        """获取主要音频"""
        audios = self.get_media_by_type("audio")
        return audios[0] if audios else None
    
    def update_consistency_tags(self, tags: Dict[str, Any]) -> None:
        """更新一致性标签"""
        if self.consistency_tags:
            self.consistency_tags.update(tags)
        else:
            self.consistency_tags = tags
    
    def add_character_reference(self, character_name: str, reference_data: Dict[str, Any]) -> None:
        """添加角色参考"""
        if not self.character_references:
            self.character_references = {}
        self.character_references[character_name] = reference_data
    
    def add_scene_reference(self, scene_name: str, reference_data: Dict[str, Any]) -> None:
        """添加场景参考"""
        if not self.scene_references:
            self.scene_references = {}
        self.scene_references[scene_name] = reference_data


# Pydantic模式
class ShotSchema(BaseEntitySchema):
    """镜头模式"""
    
    storyboard_id: str = Field(..., description="分镜ID")
    sequence_number: int = Field(..., ge=1, description="序号")
    shot_type: ShotType = Field(ShotType.MEDIUM_SHOT, description="镜头类型")
    duration: float = Field(Constants.DEFAULT_SHOT_DURATION, ge=0.1, le=60.0, description="时长(秒)")
    content: str = Field(..., description="镜头内容描述")
    dialogue: Optional[str] = Field(None, description="对话内容")
    action: Optional[str] = Field(None, description="动作描述")
    scene_description: Optional[str] = Field(None, description="场景描述")
    character_description: Optional[str] = Field(None, description="角色描述")
    mood_description: Optional[str] = Field(None, description="情绪描述")
    camera_movement: Optional[str] = Field(None, description="镜头运动")
    lighting: Optional[str] = Field(None, description="光照")
    color_tone: Optional[str] = Field(None, description="色调")
    image_prompt: Optional[str] = Field(None, description="图像生成提示词")
    video_prompt: Optional[str] = Field(None, description="视频生成提示词")
    voice_prompt: Optional[str] = Field(None, description="语音生成提示词")
    consistency_tags: Optional[Dict[str, Any]] = Field(None, description="一致性标签")
    character_references: Optional[Dict[str, Any]] = Field(None, description="角色参考")
    scene_references: Optional[Dict[str, Any]] = Field(None, description="场景参考")
    generation_metadata: Optional[Dict[str, Any]] = Field(None, description="生成元数据")


class CreateShotSchema(CreateEntitySchema):
    """创建镜头模式"""
    
    storyboard_id: str = Field(..., description="分镜ID")
    sequence_number: int = Field(..., ge=1, description="序号")
    shot_type: ShotType = Field(ShotType.MEDIUM_SHOT, description="镜头类型")
    duration: float = Field(Constants.DEFAULT_SHOT_DURATION, ge=0.1, le=60.0, description="时长(秒)")
    content: str = Field(..., min_length=1, description="镜头内容描述")
    dialogue: Optional[str] = Field(None, description="对话内容")
    action: Optional[str] = Field(None, description="动作描述")
    scene_description: Optional[str] = Field(None, description="场景描述")
    character_description: Optional[str] = Field(None, description="角色描述")
    mood_description: Optional[str] = Field(None, description="情绪描述")
    camera_movement: Optional[str] = Field(None, description="镜头运动")
    lighting: Optional[str] = Field(None, description="光照")
    color_tone: Optional[str] = Field(None, description="色调")
    
    @validator('content')
    def validate_content(cls, v):
        if not v or not v.strip():
            raise ValueError('镜头内容不能为空')
        if len(v) > 2000:
            raise ValueError('镜头内容过长，最多2000字符')
        return v.strip()


class UpdateShotSchema(UpdateEntitySchema):
    """更新镜头模式"""
    
    sequence_number: Optional[int] = Field(None, ge=1, description="序号")
    shot_type: Optional[ShotType] = Field(None, description="镜头类型")
    duration: Optional[float] = Field(None, ge=0.1, le=60.0, description="时长(秒)")
    content: Optional[str] = Field(None, description="镜头内容描述")
    dialogue: Optional[str] = Field(None, description="对话内容")
    action: Optional[str] = Field(None, description="动作描述")
    scene_description: Optional[str] = Field(None, description="场景描述")
    character_description: Optional[str] = Field(None, description="角色描述")
    mood_description: Optional[str] = Field(None, description="情绪描述")
    camera_movement: Optional[str] = Field(None, description="镜头运动")
    lighting: Optional[str] = Field(None, description="光照")
    color_tone: Optional[str] = Field(None, description="色调")
    image_prompt: Optional[str] = Field(None, description="图像生成提示词")
    video_prompt: Optional[str] = Field(None, description="视频生成提示词")
    voice_prompt: Optional[str] = Field(None, description="语音生成提示词")
    consistency_tags: Optional[Dict[str, Any]] = Field(None, description="一致性标签")
    character_references: Optional[Dict[str, Any]] = Field(None, description="角色参考")
    scene_references: Optional[Dict[str, Any]] = Field(None, description="场景参考")
    generation_metadata: Optional[Dict[str, Any]] = Field(None, description="生成元数据")
    
    @validator('content')
    def validate_content(cls, v):
        if v is not None:
            if not v or not v.strip():
                raise ValueError('镜头内容不能为空')
            if len(v) > 2000:
                raise ValueError('镜头内容过长，最多2000字符')
            return v.strip()
        return v


class ShotSummarySchema(BaseEntitySchema):
    """镜头摘要模式（用于列表显示）"""
    
    storyboard_id: str = Field(..., description="分镜ID")
    sequence_number: int = Field(..., description="序号")
    shot_type: ShotType = Field(..., description="镜头类型")
    duration: float = Field(..., description="时长(秒)")
    content: str = Field(..., description="镜头内容描述")


# 镜头相关的常量和配置
class ShotConstants:
    """镜头相关常量"""
    
    # 镜头运动类型
    CAMERA_MOVEMENTS = [
        "static",           # 静止
        "pan_left",         # 左摇
        "pan_right",        # 右摇
        "tilt_up",          # 上仰
        "tilt_down",        # 下俯
        "zoom_in",          # 推进
        "zoom_out",         # 拉远
        "dolly_in",         # 推轨
        "dolly_out",        # 拉轨
        "tracking",         # 跟踪
        "crane_up",         # 升降机上升
        "crane_down",       # 升降机下降
        "handheld",         # 手持
        "steadicam"         # 斯坦尼康
    ]
    
    # 光照类型
    LIGHTING_TYPES = [
        "natural",          # 自然光
        "soft",             # 柔光
        "hard",             # 硬光
        "dramatic",         # 戏剧性
        "low_key",          # 低调
        "high_key",         # 高调
        "backlit",          # 逆光
        "side_lit",         # 侧光
        "golden_hour",      # 黄金时刻
        "blue_hour",        # 蓝调时刻
        "neon",             # 霓虹灯
        "candlelight"       # 烛光
    ]
    
    # 色调类型
    COLOR_TONES = [
        "warm",             # 暖色调
        "cool",             # 冷色调
        "neutral",          # 中性色调
        "desaturated",      # 去饱和
        "vibrant",          # 鲜艳
        "monochrome",       # 单色
        "sepia",            # 棕褐色
        "vintage",          # 复古
        "cyberpunk",        # 赛博朋克
        "noir"              # 黑色电影
    ]
