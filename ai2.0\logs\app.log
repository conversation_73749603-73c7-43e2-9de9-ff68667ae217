{"timestamp": "2025-06-29T16:37:39.303047", "level": "INFO", "logger": "src.main", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 35}
{"timestamp": "2025-06-29T16:37:39.329133", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-29T16:37:39.329133", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-29T16:37:39.329133", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 83}
{"timestamp": "2025-06-29T16:37:39.665458", "level": "ERROR", "logger": "src.main", "message": "Failed to initialize application: 'ContentArea' object has no attribute '_pages'", "module": "main", "function": "initialize", "line": 54}
{"timestamp": "2025-06-29T16:38:58.070397", "level": "INFO", "logger": "src.main", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 35}
{"timestamp": "2025-06-29T16:38:58.093000", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-29T16:38:58.093000", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-29T16:38:58.094006", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 83}
{"timestamp": "2025-06-29T16:38:58.337810", "level": "ERROR", "logger": "src.main", "message": "Failed to initialize application: 'ContentArea' object has no attribute '_pages'", "module": "main", "function": "initialize", "line": 54}
{"timestamp": "2025-06-29T16:40:07.042804", "level": "INFO", "logger": "src.main", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 35}
{"timestamp": "2025-06-29T16:40:07.064911", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-29T16:40:07.066199", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-29T16:40:07.066199", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 83}
{"timestamp": "2025-06-29T16:40:07.320283", "level": "ERROR", "logger": "src.main", "message": "Failed to initialize application: 'LoadingWidget' object has no attribute 'message'", "module": "main", "function": "initialize", "line": 54}
{"timestamp": "2025-06-29T16:40:40.062943", "level": "INFO", "logger": "src.main", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 35}
{"timestamp": "2025-06-29T16:40:40.086682", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-29T16:40:40.086682", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-29T16:40:40.087681", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 83}
{"timestamp": "2025-06-29T16:40:40.358840", "level": "INFO", "logger": "src.main", "message": "Application initialized successfully", "module": "main", "function": "initialize", "line": 49}
{"timestamp": "2025-06-29T16:40:54.815422", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog requested", "module": "content_area", "function": "show_new_project_dialog", "line": 283}
{"timestamp": "2025-06-29T16:40:55.759818", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog requested", "module": "content_area", "function": "show_new_project_dialog", "line": 283}
{"timestamp": "2025-06-29T16:40:57.721548", "level": "INFO", "logger": "src.ui.main_window", "message": "Generate storyboard requested", "module": "main_window", "function": "_on_generate_storyboard", "line": 286}
{"timestamp": "2025-06-29T16:41:11.392026", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from light to dark", "module": "theme_manager", "function": "set_theme", "line": 83}
{"timestamp": "2025-06-29T16:41:21.637393", "level": "INFO", "logger": "src.ui.main_window", "message": "Main window closing", "module": "main_window", "function": "closeEvent", "line": 391}
{"timestamp": "2025-06-30T09:46:25.876278", "level": "INFO", "logger": "src.main", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 35}
{"timestamp": "2025-06-30T09:46:25.929933", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T09:46:25.929933", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T09:46:25.930932", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 83}
{"timestamp": "2025-06-30T09:46:26.255709", "level": "INFO", "logger": "src.main", "message": "Application initialized successfully", "module": "main", "function": "initialize", "line": 49}
{"timestamp": "2025-06-30T09:46:50.981592", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog requested", "module": "content_area", "function": "show_new_project_dialog", "line": 283}
{"timestamp": "2025-06-30T09:46:51.726132", "level": "INFO", "logger": "src.ui.components.content_area", "message": "Open project dialog requested", "module": "content_area", "function": "show_open_project_dialog", "line": 289}
{"timestamp": "2025-06-30T09:46:56.989041", "level": "INFO", "logger": "src.ui.main_window", "message": "New project requested", "module": "main_window", "function": "_on_new_project", "line": 268}
{"timestamp": "2025-06-30T09:46:56.989041", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog requested", "module": "content_area", "function": "show_new_project_dialog", "line": 283}
{"timestamp": "2025-06-30T09:46:57.805953", "level": "INFO", "logger": "src.ui.main_window", "message": "New project requested", "module": "main_window", "function": "_on_new_project", "line": 268}
{"timestamp": "2025-06-30T09:46:57.805953", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog requested", "module": "content_area", "function": "show_new_project_dialog", "line": 283}
{"timestamp": "2025-06-30T09:46:58.334213", "level": "INFO", "logger": "src.ui.main_window", "message": "Open project requested", "module": "main_window", "function": "_on_open_project", "line": 274}
{"timestamp": "2025-06-30T09:46:58.335231", "level": "INFO", "logger": "src.ui.components.content_area", "message": "Open project dialog requested", "module": "content_area", "function": "show_open_project_dialog", "line": 289}
{"timestamp": "2025-06-30T09:46:58.805521", "level": "INFO", "logger": "src.ui.main_window", "message": "Save project requested", "module": "main_window", "function": "_on_save_project", "line": 280}
{"timestamp": "2025-06-30T09:46:58.808341", "level": "INFO", "logger": "src.ui.components.content_area", "message": "Save project requested", "module": "content_area", "function": "save_current_project", "line": 295}
{"timestamp": "2025-06-30T09:46:59.333549", "level": "INFO", "logger": "src.ui.main_window", "message": "Generate storyboard requested", "module": "main_window", "function": "_on_generate_storyboard", "line": 286}
{"timestamp": "2025-06-30T09:47:00.958261", "level": "INFO", "logger": "src.ui.main_window", "message": "New project requested", "module": "main_window", "function": "_on_new_project", "line": 268}
{"timestamp": "2025-06-30T09:47:00.958261", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog requested", "module": "content_area", "function": "show_new_project_dialog", "line": 283}
{"timestamp": "2025-06-30T09:47:04.007178", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from light to dark", "module": "theme_manager", "function": "set_theme", "line": 83}
{"timestamp": "2025-06-30T09:47:04.007354", "level": "INFO", "logger": "src.ui.main_window", "message": "Theme toggled to: dark", "module": "main_window", "function": "_on_toggle_theme", "line": 293}
{"timestamp": "2025-06-30T09:47:28.662482", "level": "INFO", "logger": "src.ui.main_window", "message": "Main window closing", "module": "main_window", "function": "closeEvent", "line": 391}
{"timestamp": "2025-06-30T10:25:17.218815", "level": "INFO", "logger": "__main__", "message": "Application started successfully", "module": "main_simple", "function": "run", "line": 247}
{"timestamp": "2025-06-30T10:27:44.537030", "level": "INFO", "logger": "main", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 60}
{"timestamp": "2025-06-30T10:27:44.550168", "level": "DEBUG", "logger": "aiosqlite", "message": "executing <function connect.<locals>.connector at 0x00000193B8FE6520>", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.560465", "level": "DEBUG", "logger": "aiosqlite", "message": "operation <function connect.<locals>.connector at 0x00000193B8FE6520> completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.561069", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method create_function of sqlite3.Connection object at 0x00000193B8FF18A0>, 'regexp', 2, <function SQLiteDialect_pysqlite.on_connect.<locals>.regexp at 0x00000193B8FE4E00>, deterministic=True)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.561069", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method create_function of sqlite3.Connection object at 0x00000193B8FF18A0>, 'regexp', 2, <function SQLiteDialect_pysqlite.on_connect.<locals>.regexp at 0x00000193B8FE4E00>, deterministic=True) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.561069", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method create_function of sqlite3.Connection object at 0x00000193B8FF18A0>, 'floor', 1, <built-in function floor>, deterministic=True)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.561069", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method create_function of sqlite3.Connection object at 0x00000193B8FF18A0>, 'floor', 1, <built-in function floor>, deterministic=True) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.561069", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.561069", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.561069", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE940>, 'PRAGMA read_uncommitted', [])", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.561069", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE940>, 'PRAGMA read_uncommitted', []) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.561069", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE940>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.561069", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE940>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.561069", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE940>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.561069", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE940>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.561069", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method rollback of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.561069", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method rollback of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.563657", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.563657", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.563657", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, 'PRAGMA main.table_info(\"projects\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.563657", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, 'PRAGMA main.table_info(\"projects\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.563657", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.563657", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.564888", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.564888", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.564888", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.564888", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.564888", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, 'PRAGMA temp.table_info(\"projects\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.564888", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, 'PRAGMA temp.table_info(\"projects\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.565888", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.565888", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.565888", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.565888", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.565888", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.565888", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.565888", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, 'PRAGMA main.table_info(\"storyboards\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.566888", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, 'PRAGMA main.table_info(\"storyboards\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.566888", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.566888", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.566888", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.566888", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.566888", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.566888", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.566888", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, 'PRAGMA temp.table_info(\"storyboards\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.567888", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, 'PRAGMA temp.table_info(\"storyboards\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.567888", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.567888", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, 'PRAGMA main.table_info(\"shots\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, 'PRAGMA main.table_info(\"shots\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, 'PRAGMA temp.table_info(\"shots\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, 'PRAGMA temp.table_info(\"shots\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, 'PRAGMA main.table_info(\"media_items\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, 'PRAGMA main.table_info(\"media_items\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.568249", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.572474", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.573036", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, 'PRAGMA temp.table_info(\"media_items\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.573036", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, 'PRAGMA temp.table_info(\"media_items\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.573036", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.573036", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.574068", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.574068", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.575076", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.575076", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.575076", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, '\\nCREATE TABLE projects (\\n\\ttitle VARCHAR(255) NOT NULL, \\n\\tcontent TEXT, \\n\\tstatus VARCHAR(11) NOT NULL, \\n\\tstyle VARCHAR(100) NOT NULL, \\n\\tlanguage VARCHAR(10) NOT NULL, \\n\\tvideo_width INTEGER NOT NULL, \\n\\tvideo_height INTEGER NOT NULL, \\n\\tvideo_fps FLOAT NOT NULL, \\n\\tgeneration_settings JSON, \\n\\ttotal_shots INTEGER NOT NULL, \\n\\ttotal_duration FLOAT NOT NULL, \\n\\tid UUID NOT NULL, \\n\\tname VARCHAR(255) NOT NULL, \\n\\tdescription TEXT, \\n\\tmetadata_json TEXT, \\n\\tcreated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, \\n\\tupdated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, \\n\\tdeleted_at DATETIME, \\n\\tis_deleted BOOLEAN NOT NULL, \\n\\tPRIMARY KEY (id)\\n)\\n\\n', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.592913", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, '\\nCREATE TABLE projects (\\n\\ttitle VARCHAR(255) NOT NULL, \\n\\tcontent TEXT, \\n\\tstatus VARCHAR(11) NOT NULL, \\n\\tstyle VARCHAR(100) NOT NULL, \\n\\tlanguage VARCHAR(10) NOT NULL, \\n\\tvideo_width INTEGER NOT NULL, \\n\\tvideo_height INTEGER NOT NULL, \\n\\tvideo_fps FLOAT NOT NULL, \\n\\tgeneration_settings JSON, \\n\\ttotal_shots INTEGER NOT NULL, \\n\\ttotal_duration FLOAT NOT NULL, \\n\\tid UUID NOT NULL, \\n\\tname VARCHAR(255) NOT NULL, \\n\\tdescription TEXT, \\n\\tmetadata_json TEXT, \\n\\tcreated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, \\n\\tupdated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, \\n\\tdeleted_at DATETIME, \\n\\tis_deleted BOOLEAN NOT NULL, \\n\\tPRIMARY KEY (id)\\n)\\n\\n', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.594416", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.594416", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.594745", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.594745", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.594745", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, '\\nCREATE TABLE storyboards (\\n\\tproject_id UUID NOT NULL, \\n\\tcontent TEXT NOT NULL, \\n\\tstatus VARCHAR(10) NOT NULL, \\n\\tgeneration_prompt TEXT, \\n\\tgeneration_settings JSON, \\n\\tgeneration_metadata JSON, \\n\\tshot_count INTEGER NOT NULL, \\n\\ttotal_duration FLOAT NOT NULL, \\n\\tprocessing_started_at DATETIME, \\n\\tprocessing_completed_at DATETIME, \\n\\terror_message TEXT, \\n\\tid UUID NOT NULL, \\n\\tname VARCHAR(255) NOT NULL, \\n\\tdescription TEXT, \\n\\tmetadata_json TEXT, \\n\\tcreated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, \\n\\tupdated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, \\n\\tdeleted_at DATETIME, \\n\\tis_deleted BOOLEAN NOT NULL, \\n\\tPRIMARY KEY (id), \\n\\tFOREIGN KEY(project_id) REFERENCES projects (id) ON DELETE CASCADE\\n)\\n\\n', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.609655", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, '\\nCREATE TABLE storyboards (\\n\\tproject_id UUID NOT NULL, \\n\\tcontent TEXT NOT NULL, \\n\\tstatus VARCHAR(10) NOT NULL, \\n\\tgeneration_prompt TEXT, \\n\\tgeneration_settings JSON, \\n\\tgeneration_metadata JSON, \\n\\tshot_count INTEGER NOT NULL, \\n\\ttotal_duration FLOAT NOT NULL, \\n\\tprocessing_started_at DATETIME, \\n\\tprocessing_completed_at DATETIME, \\n\\terror_message TEXT, \\n\\tid UUID NOT NULL, \\n\\tname VARCHAR(255) NOT NULL, \\n\\tdescription TEXT, \\n\\tmetadata_json TEXT, \\n\\tcreated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, \\n\\tupdated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, \\n\\tdeleted_at DATETIME, \\n\\tis_deleted BOOLEAN NOT NULL, \\n\\tPRIMARY KEY (id), \\n\\tFOREIGN KEY(project_id) REFERENCES projects (id) ON DELETE CASCADE\\n)\\n\\n', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.609655", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.609655", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.610808", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.610808", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.610808", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, '\\nCREATE TABLE shots (\\n\\tstoryboard_id UUID NOT NULL, \\n\\tsequence_number INTEGER NOT NULL, \\n\\tshot_type VARCHAR(17) NOT NULL, \\n\\tduration FLOAT NOT NULL, \\n\\tcontent TEXT NOT NULL, \\n\\tdialogue TEXT, \\n\\taction TEXT, \\n\\tscene_description TEXT, \\n\\tcharacter_description TEXT, \\n\\tmood_description TEXT, \\n\\tcamera_movement VARCHAR(100), \\n\\tlighting VARCHAR(100), \\n\\tcolor_tone VARCHAR(100), \\n\\timage_prompt TEXT, \\n\\tvideo_prompt TEXT, \\n\\tvoice_prompt TEXT, \\n\\tconsistency_tags JSON, \\n\\tcharacter_references JSON, \\n\\tscene_references JSON, \\n\\tgeneration_metadata JSON, \\n\\tid UUID NOT NULL, \\n\\tname VARCHAR(255) NOT NULL, \\n\\tdescription TEXT, \\n\\tmetadata_json TEXT, \\n\\tcreated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, \\n\\tupdated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, \\n\\tdeleted_at DATETIME, \\n\\tis_deleted BOOLEAN NOT NULL, \\n\\tPRIMARY KEY (id), \\n\\tFOREIGN KEY(storyboard_id) REFERENCES storyboards (id) ON DELETE CASCADE\\n)\\n\\n', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.624029", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, '\\nCREATE TABLE shots (\\n\\tstoryboard_id UUID NOT NULL, \\n\\tsequence_number INTEGER NOT NULL, \\n\\tshot_type VARCHAR(17) NOT NULL, \\n\\tduration FLOAT NOT NULL, \\n\\tcontent TEXT NOT NULL, \\n\\tdialogue TEXT, \\n\\taction TEXT, \\n\\tscene_description TEXT, \\n\\tcharacter_description TEXT, \\n\\tmood_description TEXT, \\n\\tcamera_movement VARCHAR(100), \\n\\tlighting VARCHAR(100), \\n\\tcolor_tone VARCHAR(100), \\n\\timage_prompt TEXT, \\n\\tvideo_prompt TEXT, \\n\\tvoice_prompt TEXT, \\n\\tconsistency_tags JSON, \\n\\tcharacter_references JSON, \\n\\tscene_references JSON, \\n\\tgeneration_metadata JSON, \\n\\tid UUID NOT NULL, \\n\\tname VARCHAR(255) NOT NULL, \\n\\tdescription TEXT, \\n\\tmetadata_json TEXT, \\n\\tcreated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, \\n\\tupdated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, \\n\\tdeleted_at DATETIME, \\n\\tis_deleted BOOLEAN NOT NULL, \\n\\tPRIMARY KEY (id), \\n\\tFOREIGN KEY(storyboard_id) REFERENCES storyboards (id) ON DELETE CASCADE\\n)\\n\\n', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.625046", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.625046", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.625046", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.625046", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.626046", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, '\\nCREATE TABLE media_items (\\n\\tshot_id UUID NOT NULL, \\n\\tmedia_type VARCHAR(5) NOT NULL, \\n\\tstatus VARCHAR(10) NOT NULL, \\n\\tfile_path VARCHAR(500), \\n\\tfile_name VARCHAR(255), \\n\\tfile_size INTEGER, \\n\\tfile_format VARCHAR(50), \\n\\twidth INTEGER, \\n\\theight INTEGER, \\n\\tduration FLOAT, \\n\\tframe_rate FLOAT, \\n\\tgeneration_provider VARCHAR(12), \\n\\tgeneration_prompt TEXT, \\n\\tgeneration_settings JSON, \\n\\tgeneration_metadata JSON, \\n\\tprocessing_started_at DATETIME, \\n\\tprocessing_completed_at DATETIME, \\n\\terror_message TEXT, \\n\\tquality_score FLOAT, \\n\\tis_primary BOOLEAN NOT NULL, \\n\\tid UUID NOT NULL, \\n\\tname VARCHAR(255) NOT NULL, \\n\\tdescription TEXT, \\n\\tmetadata_json TEXT, \\n\\tcreated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, \\n\\tupdated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, \\n\\tdeleted_at DATETIME, \\n\\tis_deleted BOOLEAN NOT NULL, \\n\\tPRIMARY KEY (id), \\n\\tFOREIGN KEY(shot_id) REFERENCES shots (id) ON DELETE CASCADE\\n)\\n\\n', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.639911", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000193B8FCE9C0>, '\\nCREATE TABLE media_items (\\n\\tshot_id UUID NOT NULL, \\n\\tmedia_type VARCHAR(5) NOT NULL, \\n\\tstatus VARCHAR(10) NOT NULL, \\n\\tfile_path VARCHAR(500), \\n\\tfile_name VARCHAR(255), \\n\\tfile_size INTEGER, \\n\\tfile_format VARCHAR(50), \\n\\twidth INTEGER, \\n\\theight INTEGER, \\n\\tduration FLOAT, \\n\\tframe_rate FLOAT, \\n\\tgeneration_provider VARCHAR(12), \\n\\tgeneration_prompt TEXT, \\n\\tgeneration_settings JSON, \\n\\tgeneration_metadata JSON, \\n\\tprocessing_started_at DATETIME, \\n\\tprocessing_completed_at DATETIME, \\n\\terror_message TEXT, \\n\\tquality_score FLOAT, \\n\\tis_primary BOOLEAN NOT NULL, \\n\\tid UUID NOT NULL, \\n\\tname VARCHAR(255) NOT NULL, \\n\\tdescription TEXT, \\n\\tmetadata_json TEXT, \\n\\tcreated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, \\n\\tupdated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, \\n\\tdeleted_at DATETIME, \\n\\tis_deleted BOOLEAN NOT NULL, \\n\\tPRIMARY KEY (id), \\n\\tFOREIGN KEY(shot_id) REFERENCES shots (id) ON DELETE CASCADE\\n)\\n\\n', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.639911", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.640589", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000193B8FCE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.641323", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method commit of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.641323", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method commit of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.641323", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method rollback of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:27:44.641323", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method rollback of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:27:44.641323", "level": "INFO", "logger": "src.core.database", "message": "Database tables created successfully", "module": "database", "function": "_create_tables", "line": 84}
{"timestamp": "2025-06-30T10:27:44.641323", "level": "INFO", "logger": "src.core.database", "message": "Database initialized successfully", "module": "database", "function": "initialize", "line": 73}
{"timestamp": "2025-06-30T10:27:44.642328", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T10:27:44.642328", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T10:27:44.642328", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: light", "module": "theme_manager", "function": "_apply_theme", "line": 95}
{"timestamp": "2025-06-30T10:27:44.642328", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 83}
{"timestamp": "2025-06-30T10:27:44.642328", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: light", "module": "theme_manager", "function": "_apply_theme", "line": 95}
{"timestamp": "2025-06-30T10:27:44.642328", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from light to light", "module": "theme_manager", "function": "set_theme", "line": 83}
{"timestamp": "2025-06-30T10:27:44.642328", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_llm (llm)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:27:44.642328", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: zhipu_llm (llm)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:27:44.642328", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: pollinations_image (image)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:27:44.642328", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_image (image)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:27:44.642328", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: edge_tts (voice)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:27:44.642328", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_tts (voice)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:27:44.642328", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: cogvideox (video)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:27:44.642328", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: local_composer (video)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:27:44.642328", "level": "INFO", "logger": "src.services.registry", "message": "Started health monitoring", "module": "registry", "function": "start_health_monitoring", "line": 254}
{"timestamp": "2025-06-30T10:27:44.642328", "level": "INFO", "logger": "main", "message": "Application initialized successfully", "module": "main", "function": "initialize", "line": 78}
{"timestamp": "2025-06-30T10:27:44.869472", "level": "WARNING", "logger": "main", "message": "Failed to apply theme: 'ThemeManager' object has no attribute 'current_theme'", "module": "main", "function": "run", "line": 140}
{"timestamp": "2025-06-30T10:27:44.994306", "level": "INFO", "logger": "main", "message": "Application started successfully", "module": "main", "function": "run", "line": 152}
{"timestamp": "2025-06-30T10:28:04.653401", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: projects", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:28:04.653401", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: projects", "module": "content_area", "function": "show_page", "line": 277}
{"timestamp": "2025-06-30T10:28:04.653401", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: projects", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 110}
{"timestamp": "2025-06-30T10:28:08.990803", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: storyboard", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:28:08.990803", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: storyboard", "module": "content_area", "function": "show_page", "line": 277}
{"timestamp": "2025-06-30T10:28:08.990803", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: storyboard", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 110}
{"timestamp": "2025-06-30T10:28:11.365171", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: media", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:28:11.365171", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: media", "module": "content_area", "function": "show_page", "line": 277}
{"timestamp": "2025-06-30T10:28:11.365171", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: media", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 110}
{"timestamp": "2025-06-30T10:28:12.357228", "level": "INFO", "logger": "main", "message": "Received signal 2, shutting down...", "module": "main", "function": "signal_handler", "line": 168}
{"timestamp": "2025-06-30T10:28:12.357228", "level": "INFO", "logger": "main", "message": "Shutting down application...", "module": "main", "function": "shutdown", "line": 182}
{"timestamp": "2025-06-30T10:28:12.357228", "level": "INFO", "logger": "src.ui.main_window", "message": "Main window closing", "module": "main_window", "function": "closeEvent", "line": 392}
{"timestamp": "2025-06-30T10:28:12.367280", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: settings", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:28:12.367280", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: settings", "module": "content_area", "function": "show_page", "line": 277}
{"timestamp": "2025-06-30T10:28:12.367280", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: settings", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 110}
{"timestamp": "2025-06-30T10:28:12.367280", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Connection object at 0x00000193B8FF18A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:28:12.369977", "level": "ERROR", "logger": "sqlalchemy.pool.impl.AsyncAdaptedQueuePool", "message": "Exception closing connection <AdaptedConnection <Connection(Thread-1, started daemon 8908)>>", "module": "base", "function": "_close_connection", "line": 378, "exception": {"type": "CancelledError", "message": "", "traceback": "Traceback (most recent call last):\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 376, in _close_connection\n    self._dialect.do_close(connection)\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py\", line 712, in do_close\n    dbapi_connection.close()\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py\", line 277, in close\n    self.await_(self._connection.close())\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py\", line 132, in await_only\n    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py\", line 196, in greenlet_spawn\n    value = await result\n            ^^^^^^^^^^^^\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\aiosqlite\\core.py\", line 168, in close\n    await self._execute(self._conn.close)\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\aiosqlite\\core.py\", line 122, in _execute\n    return await future\n           ^^^^^^^^^^^^\nasyncio.exceptions.CancelledError"}}
{"timestamp": "2025-06-30T10:28:12.369977", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Connection object at 0x00000193B8FF18A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:28:12.373982", "level": "INFO", "logger": "src.services.registry", "message": "Stopped health monitoring", "module": "registry", "function": "stop_health_monitoring", "line": 265}
{"timestamp": "2025-06-30T10:29:53.994132", "level": "INFO", "logger": "__main__", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 60}
{"timestamp": "2025-06-30T10:29:54.003603", "level": "DEBUG", "logger": "aiosqlite", "message": "executing <function connect.<locals>.connector at 0x00000269C4F4E520>", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.003603", "level": "DEBUG", "logger": "aiosqlite", "message": "operation <function connect.<locals>.connector at 0x00000269C4F4E520> completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.004661", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method create_function of sqlite3.Connection object at 0x00000269C4F598A0>, 'regexp', 2, <function SQLiteDialect_pysqlite.on_connect.<locals>.regexp at 0x00000269C4F4CE00>, deterministic=True)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.004661", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method create_function of sqlite3.Connection object at 0x00000269C4F598A0>, 'regexp', 2, <function SQLiteDialect_pysqlite.on_connect.<locals>.regexp at 0x00000269C4F4CE00>, deterministic=True) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.004661", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method create_function of sqlite3.Connection object at 0x00000269C4F598A0>, 'floor', 1, <built-in function floor>, deterministic=True)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.004661", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method create_function of sqlite3.Connection object at 0x00000269C4F598A0>, 'floor', 1, <built-in function floor>, deterministic=True) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.004661", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000269C4F598A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.004661", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000269C4F598A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.004661", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000269C4F2A940>, 'PRAGMA read_uncommitted', [])", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.005804", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000269C4F2A940>, 'PRAGMA read_uncommitted', []) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.005804", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000269C4F2A940>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.005804", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000269C4F2A940>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.005804", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000269C4F2A940>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.005804", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000269C4F2A940>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.005804", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method rollback of sqlite3.Connection object at 0x00000269C4F598A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.005804", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method rollback of sqlite3.Connection object at 0x00000269C4F598A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.006479", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000269C4F598A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.007004", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000269C4F598A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.007004", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000269C4F2A9C0>, 'PRAGMA main.table_info(\"projects\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.007004", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000269C4F2A9C0>, 'PRAGMA main.table_info(\"projects\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.007004", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000269C4F2A9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.007004", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000269C4F2A9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.008304", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000269C4F2A9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.008304", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000269C4F2A9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.008304", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000269C4F598A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.008304", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000269C4F598A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.008304", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000269C4F2A9C0>, 'PRAGMA main.table_info(\"storyboards\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.008304", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000269C4F2A9C0>, 'PRAGMA main.table_info(\"storyboards\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.008304", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000269C4F2A9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.008304", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000269C4F2A9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.008304", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000269C4F2A9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.008304", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000269C4F2A9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.010280", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000269C4F598A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.010280", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000269C4F598A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.010280", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000269C4F2A9C0>, 'PRAGMA main.table_info(\"shots\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.010280", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000269C4F2A9C0>, 'PRAGMA main.table_info(\"shots\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.010280", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000269C4F2A9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.010280", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000269C4F2A9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.011285", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000269C4F2A9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.011285", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000269C4F2A9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.011285", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000269C4F598A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.011285", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x00000269C4F598A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.011285", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000269C4F2A9C0>, 'PRAGMA main.table_info(\"media_items\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.011285", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x00000269C4F2A9C0>, 'PRAGMA main.table_info(\"media_items\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.012285", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000269C4F2A9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.012285", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x00000269C4F2A9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.012285", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000269C4F2A9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.012285", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x00000269C4F2A9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.012285", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method commit of sqlite3.Connection object at 0x00000269C4F598A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.012285", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method commit of sqlite3.Connection object at 0x00000269C4F598A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.013285", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method rollback of sqlite3.Connection object at 0x00000269C4F598A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:29:54.013285", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method rollback of sqlite3.Connection object at 0x00000269C4F598A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:29:54.013285", "level": "INFO", "logger": "src.core.database", "message": "Database tables created successfully", "module": "database", "function": "_create_tables", "line": 84}
{"timestamp": "2025-06-30T10:29:54.013285", "level": "INFO", "logger": "src.core.database", "message": "Database initialized successfully", "module": "database", "function": "initialize", "line": 73}
{"timestamp": "2025-06-30T10:29:54.013285", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T10:29:54.014080", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T10:29:54.014080", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: light", "module": "theme_manager", "function": "_apply_theme", "line": 100}
{"timestamp": "2025-06-30T10:29:54.014080", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 88}
{"timestamp": "2025-06-30T10:29:54.014080", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: light", "module": "theme_manager", "function": "_apply_theme", "line": 100}
{"timestamp": "2025-06-30T10:29:54.014751", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from light to light", "module": "theme_manager", "function": "set_theme", "line": 88}
{"timestamp": "2025-06-30T10:29:54.014751", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_llm (llm)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:29:54.014751", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: zhipu_llm (llm)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:29:54.014751", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: pollinations_image (image)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:29:54.014751", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_image (image)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:29:54.014751", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: edge_tts (voice)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:29:54.014751", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_tts (voice)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:29:54.014751", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: cogvideox (video)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:29:54.014751", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: local_composer (video)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:29:54.014751", "level": "INFO", "logger": "src.services.registry", "message": "Started health monitoring", "module": "registry", "function": "start_health_monitoring", "line": 254}
{"timestamp": "2025-06-30T10:29:54.014751", "level": "INFO", "logger": "__main__", "message": "Application initialized successfully", "module": "main", "function": "initialize", "line": 78}
{"timestamp": "2025-06-30T10:29:54.376493", "level": "INFO", "logger": "__main__", "message": "Application started successfully", "module": "main", "function": "run", "line": 152}
{"timestamp": "2025-06-30T10:30:02.928420", "level": "INFO", "logger": "src.ui.main_window", "message": "New project requested", "module": "main_window", "function": "_on_new_project", "line": 269}
{"timestamp": "2025-06-30T10:30:02.928420", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog requested", "module": "content_area", "function": "show_new_project_dialog", "line": 283}
{"timestamp": "2025-06-30T10:30:03.703217", "level": "INFO", "logger": "src.ui.main_window", "message": "New project requested", "module": "main_window", "function": "_on_new_project", "line": 269}
{"timestamp": "2025-06-30T10:30:03.703217", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog requested", "module": "content_area", "function": "show_new_project_dialog", "line": 283}
{"timestamp": "2025-06-30T10:30:04.170958", "level": "INFO", "logger": "src.ui.main_window", "message": "Open project requested", "module": "main_window", "function": "_on_open_project", "line": 275}
{"timestamp": "2025-06-30T10:30:04.170958", "level": "INFO", "logger": "src.ui.components.content_area", "message": "Open project dialog requested", "module": "content_area", "function": "show_open_project_dialog", "line": 289}
{"timestamp": "2025-06-30T10:30:04.625163", "level": "INFO", "logger": "src.ui.main_window", "message": "Save project requested", "module": "main_window", "function": "_on_save_project", "line": 281}
{"timestamp": "2025-06-30T10:30:04.625163", "level": "INFO", "logger": "src.ui.components.content_area", "message": "Save project requested", "module": "content_area", "function": "save_current_project", "line": 295}
{"timestamp": "2025-06-30T10:30:05.088382", "level": "INFO", "logger": "src.ui.main_window", "message": "Generate storyboard requested", "module": "main_window", "function": "_on_generate_storyboard", "line": 287}
{"timestamp": "2025-06-30T10:30:06.945718", "level": "INFO", "logger": "src.ui.main_window", "message": "New project requested", "module": "main_window", "function": "_on_new_project", "line": 269}
{"timestamp": "2025-06-30T10:30:06.945718", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog requested", "module": "content_area", "function": "show_new_project_dialog", "line": 283}
{"timestamp": "2025-06-30T10:30:08.981076", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: projects", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:30:08.981076", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: projects", "module": "content_area", "function": "show_page", "line": 277}
{"timestamp": "2025-06-30T10:30:08.981076", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: projects", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 110}
{"timestamp": "2025-06-30T10:30:12.360327", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: storyboard", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:30:12.360327", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: storyboard", "module": "content_area", "function": "show_page", "line": 277}
{"timestamp": "2025-06-30T10:30:12.360327", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: storyboard", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 110}
{"timestamp": "2025-06-30T10:30:12.994784", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: media", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:30:12.994784", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: media", "module": "content_area", "function": "show_page", "line": 277}
{"timestamp": "2025-06-30T10:30:12.994784", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: media", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 110}
{"timestamp": "2025-06-30T10:30:13.744471", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: storyboard", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:30:13.753703", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: storyboard", "module": "content_area", "function": "show_page", "line": 277}
{"timestamp": "2025-06-30T10:30:13.755076", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: storyboard", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 110}
{"timestamp": "2025-06-30T10:30:20.882236", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: dark", "module": "theme_manager", "function": "_apply_theme", "line": 100}
{"timestamp": "2025-06-30T10:30:20.882236", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from light to dark", "module": "theme_manager", "function": "set_theme", "line": 88}
{"timestamp": "2025-06-30T10:31:56.894538", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: media", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:31:56.894538", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: media", "module": "content_area", "function": "show_page", "line": 277}
{"timestamp": "2025-06-30T10:31:56.894538", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: media", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 110}
{"timestamp": "2025-06-30T10:32:03.508975", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: settings", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:32:03.508975", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: settings", "module": "content_area", "function": "show_page", "line": 277}
{"timestamp": "2025-06-30T10:32:03.508975", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: settings", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 110}
{"timestamp": "2025-06-30T10:32:08.155924", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: light", "module": "theme_manager", "function": "_apply_theme", "line": 100}
{"timestamp": "2025-06-30T10:32:08.155924", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from dark to light", "module": "theme_manager", "function": "set_theme", "line": 88}
{"timestamp": "2025-06-30T10:32:08.155924", "level": "INFO", "logger": "src.ui.main_window", "message": "Theme toggled to: light", "module": "main_window", "function": "_on_toggle_theme", "line": 294}
{"timestamp": "2025-06-30T10:32:15.848113", "level": "INFO", "logger": "src.ui.main_window", "message": "Main window closing", "module": "main_window", "function": "closeEvent", "line": 392}
{"timestamp": "2025-06-30T10:39:53.840474", "level": "INFO", "logger": "main", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 60}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing <function connect.<locals>.connector at 0x0000024D54BDE8E0>", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation <function connect.<locals>.connector at 0x0000024D54BDE8E0> completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method create_function of sqlite3.Connection object at 0x0000024D54BE9A80>, 'regexp', 2, <function SQLiteDialect_pysqlite.on_connect.<locals>.regexp at 0x0000024D54BDD1C0>, deterministic=True)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method create_function of sqlite3.Connection object at 0x0000024D54BE9A80>, 'regexp', 2, <function SQLiteDialect_pysqlite.on_connect.<locals>.regexp at 0x0000024D54BDD1C0>, deterministic=True) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method create_function of sqlite3.Connection object at 0x0000024D54BE9A80>, 'floor', 1, <built-in function floor>, deterministic=True)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method create_function of sqlite3.Connection object at 0x0000024D54BE9A80>, 'floor', 1, <built-in function floor>, deterministic=True) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000024D54BE9A80>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000024D54BE9A80>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000024D54BBEA40>, 'PRAGMA read_uncommitted', [])", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000024D54BBEA40>, 'PRAGMA read_uncommitted', []) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000024D54BBEA40>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000024D54BBEA40>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000024D54BBEA40>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000024D54BBEA40>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method rollback of sqlite3.Connection object at 0x0000024D54BE9A80>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method rollback of sqlite3.Connection object at 0x0000024D54BE9A80>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000024D54BE9A80>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000024D54BE9A80>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000024D54BBEAC0>, 'PRAGMA main.table_info(\"projects\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000024D54BBEAC0>, 'PRAGMA main.table_info(\"projects\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000024D54BBEAC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000024D54BBEAC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000024D54BBEAC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000024D54BBEAC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000024D54BE9A80>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000024D54BE9A80>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000024D54BBEAC0>, 'PRAGMA main.table_info(\"storyboards\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000024D54BBEAC0>, 'PRAGMA main.table_info(\"storyboards\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000024D54BBEAC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000024D54BBEAC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000024D54BBEAC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000024D54BBEAC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000024D54BE9A80>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000024D54BE9A80>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000024D54BBEAC0>, 'PRAGMA main.table_info(\"shots\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.851522", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000024D54BBEAC0>, 'PRAGMA main.table_info(\"shots\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.857959", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000024D54BBEAC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.858295", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000024D54BBEAC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.858295", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000024D54BBEAC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.858295", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000024D54BBEAC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.858295", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000024D54BE9A80>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.859261", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000024D54BE9A80>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.859261", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000024D54BBEAC0>, 'PRAGMA main.table_info(\"media_items\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.859765", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000024D54BBEAC0>, 'PRAGMA main.table_info(\"media_items\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.859765", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000024D54BBEAC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.859765", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000024D54BBEAC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.860283", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000024D54BBEAC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.860283", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000024D54BBEAC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.860283", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method commit of sqlite3.Connection object at 0x0000024D54BE9A80>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.860283", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method commit of sqlite3.Connection object at 0x0000024D54BE9A80>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.861288", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method rollback of sqlite3.Connection object at 0x0000024D54BE9A80>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:39:53.861288", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method rollback of sqlite3.Connection object at 0x0000024D54BE9A80>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:39:53.861288", "level": "INFO", "logger": "src.core.database", "message": "Database tables created successfully", "module": "database", "function": "_create_tables", "line": 84}
{"timestamp": "2025-06-30T10:39:53.861288", "level": "INFO", "logger": "src.core.database", "message": "Database initialized successfully", "module": "database", "function": "initialize", "line": 73}
{"timestamp": "2025-06-30T10:39:53.861288", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T10:39:53.861288", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T10:39:53.862300", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: light", "module": "theme_manager", "function": "_apply_theme", "line": 100}
{"timestamp": "2025-06-30T10:39:53.862300", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 88}
{"timestamp": "2025-06-30T10:39:53.862300", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: light", "module": "theme_manager", "function": "_apply_theme", "line": 100}
{"timestamp": "2025-06-30T10:39:53.862300", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from light to light", "module": "theme_manager", "function": "set_theme", "line": 88}
{"timestamp": "2025-06-30T10:39:53.862300", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_llm (llm)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:39:53.863006", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: zhipu_llm (llm)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:39:53.863006", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: pollinations_image (image)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:39:53.863006", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_image (image)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:39:53.863006", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: edge_tts (voice)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:39:53.863006", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_tts (voice)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:39:53.863006", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: cogvideox (video)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:39:53.863006", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: local_composer (video)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:39:53.863006", "level": "INFO", "logger": "src.services.registry", "message": "Started health monitoring", "module": "registry", "function": "start_health_monitoring", "line": 254}
{"timestamp": "2025-06-30T10:39:53.863006", "level": "INFO", "logger": "main", "message": "Application initialized successfully", "module": "main", "function": "initialize", "line": 78}
{"timestamp": "2025-06-30T10:39:54.244075", "level": "INFO", "logger": "main", "message": "Application started successfully", "module": "main", "function": "run", "line": 152}
{"timestamp": "2025-06-30T10:39:56.468586", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: projects", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:39:56.468586", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: projects", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:39:56.468586", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: projects", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 120}
{"timestamp": "2025-06-30T10:39:57.369240", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: storyboard", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:39:57.369240", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: storyboard", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:39:57.369240", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: storyboard", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 120}
{"timestamp": "2025-06-30T10:39:58.002024", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: media", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:39:58.002024", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: media", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:39:58.002024", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: media", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 120}
{"timestamp": "2025-06-30T10:39:59.726031", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: settings", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:39:59.726031", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: settings", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:39:59.726031", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: settings", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 120}
{"timestamp": "2025-06-30T10:40:00.711322", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: dashboard", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:40:00.711322", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: dashboard", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:40:00.711322", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: dashboard", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 120}
{"timestamp": "2025-06-30T10:40:27.294028", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: projects", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:40:27.294028", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: projects", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:40:27.294028", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: projects", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 120}
{"timestamp": "2025-06-30T10:40:28.119428", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: storyboard", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:40:28.119428", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: storyboard", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:40:28.119428", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: storyboard", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 120}
{"timestamp": "2025-06-30T10:40:28.895684", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: media", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:40:28.895684", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: media", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:40:28.895684", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: media", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 120}
{"timestamp": "2025-06-30T10:40:29.510909", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: settings", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:40:29.510909", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: settings", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:40:29.510909", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: settings", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 120}
{"timestamp": "2025-06-30T10:40:30.721951", "level": "INFO", "logger": "src.ui.main_window", "message": "Open project requested", "module": "main_window", "function": "_on_open_project", "line": 275}
{"timestamp": "2025-06-30T10:40:34.177052", "level": "INFO", "logger": "src.ui.components.content_area", "message": "Open project dialog completed", "module": "content_area", "function": "show_open_project_dialog", "line": 375}
{"timestamp": "2025-06-30T10:40:35.488794", "level": "INFO", "logger": "src.ui.main_window", "message": "Generate storyboard requested", "module": "main_window", "function": "_on_generate_storyboard", "line": 287}
{"timestamp": "2025-06-30T10:40:45.091257", "level": "INFO", "logger": "src.ui.components.content_area", "message": "Settings saved", "module": "content_area", "function": "_save_settings", "line": 443}
{"timestamp": "2025-06-30T10:40:49.137534", "level": "INFO", "logger": "main", "message": "Received signal 2, shutting down...", "module": "main", "function": "signal_handler", "line": 168}
{"timestamp": "2025-06-30T10:40:49.137534", "level": "INFO", "logger": "main", "message": "Shutting down application...", "module": "main", "function": "shutdown", "line": 182}
{"timestamp": "2025-06-30T10:40:49.137534", "level": "INFO", "logger": "src.ui.main_window", "message": "Main window closing", "module": "main_window", "function": "closeEvent", "line": 392}
{"timestamp": "2025-06-30T10:40:49.154722", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Connection object at 0x0000024D54BE9A80>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:40:49.155239", "level": "ERROR", "logger": "sqlalchemy.pool.impl.AsyncAdaptedQueuePool", "message": "Exception closing connection <AdaptedConnection <Connection(Thread-1, started daemon 20492)>>", "module": "base", "function": "_close_connection", "line": 378, "exception": {"type": "CancelledError", "message": "", "traceback": "Traceback (most recent call last):\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 376, in _close_connection\n    self._dialect.do_close(connection)\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py\", line 712, in do_close\n    dbapi_connection.close()\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py\", line 277, in close\n    self.await_(self._connection.close())\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py\", line 132, in await_only\n    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py\", line 196, in greenlet_spawn\n    value = await result\n            ^^^^^^^^^^^^\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\aiosqlite\\core.py\", line 168, in close\n    await self._execute(self._conn.close)\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\aiosqlite\\core.py\", line 122, in _execute\n    return await future\n           ^^^^^^^^^^^^\nasyncio.exceptions.CancelledError"}}
{"timestamp": "2025-06-30T10:40:49.156245", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Connection object at 0x0000024D54BE9A80>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:40:49.162187", "level": "INFO", "logger": "src.services.registry", "message": "Stopped health monitoring", "module": "registry", "function": "stop_health_monitoring", "line": 265}
{"timestamp": "2025-06-30T10:42:09.105825", "level": "INFO", "logger": "__main__", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 60}
{"timestamp": "2025-06-30T10:42:09.117494", "level": "DEBUG", "logger": "aiosqlite", "message": "executing <function connect.<locals>.connector at 0x000002C5685F25C0>", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.117494", "level": "DEBUG", "logger": "aiosqlite", "message": "operation <function connect.<locals>.connector at 0x000002C5685F25C0> completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.121033", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method create_function of sqlite3.Connection object at 0x000002C5685F98A0>, 'regexp', 2, <function SQLiteDialect_pysqlite.on_connect.<locals>.regexp at 0x000002C5685F0EA0>, deterministic=True)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.121033", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method create_function of sqlite3.Connection object at 0x000002C5685F98A0>, 'regexp', 2, <function SQLiteDialect_pysqlite.on_connect.<locals>.regexp at 0x000002C5685F0EA0>, deterministic=True) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.121558", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method create_function of sqlite3.Connection object at 0x000002C5685F98A0>, 'floor', 1, <built-in function floor>, deterministic=True)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.121558", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method create_function of sqlite3.Connection object at 0x000002C5685F98A0>, 'floor', 1, <built-in function floor>, deterministic=True) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.121558", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002C5685F98A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.121558", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002C5685F98A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.121558", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002C5685CE9C0>, 'PRAGMA read_uncommitted', [])", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.122566", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002C5685CE9C0>, 'PRAGMA read_uncommitted', []) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.122566", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002C5685CE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.122566", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002C5685CE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.122566", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002C5685CE9C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.122566", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002C5685CE9C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.123566", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method rollback of sqlite3.Connection object at 0x000002C5685F98A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.123566", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method rollback of sqlite3.Connection object at 0x000002C5685F98A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.123566", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002C5685F98A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.123566", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002C5685F98A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.124567", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002C5685CEA40>, 'PRAGMA main.table_info(\"projects\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.124567", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002C5685CEA40>, 'PRAGMA main.table_info(\"projects\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.124567", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002C5685CEA40>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.124567", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002C5685CEA40>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.124567", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002C5685CEA40>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.124567", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002C5685CEA40>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.125566", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002C5685F98A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.125566", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002C5685F98A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.125566", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002C5685CEA40>, 'PRAGMA main.table_info(\"storyboards\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.126367", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002C5685CEA40>, 'PRAGMA main.table_info(\"storyboards\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.126367", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002C5685CEA40>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.126367", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002C5685CEA40>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.126367", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002C5685CEA40>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.126367", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002C5685CEA40>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.126367", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002C5685F98A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.126367", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002C5685F98A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.128263", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002C5685CEA40>, 'PRAGMA main.table_info(\"shots\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.128263", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002C5685CEA40>, 'PRAGMA main.table_info(\"shots\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.128263", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002C5685CEA40>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.128263", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002C5685CEA40>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.128263", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002C5685CEA40>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.128263", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002C5685CEA40>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.128263", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002C5685F98A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.129474", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002C5685F98A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.129474", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002C5685CEA40>, 'PRAGMA main.table_info(\"media_items\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.129474", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002C5685CEA40>, 'PRAGMA main.table_info(\"media_items\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.129474", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002C5685CEA40>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.129474", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002C5685CEA40>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.129474", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002C5685CEA40>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.129474", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002C5685CEA40>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.130479", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method commit of sqlite3.Connection object at 0x000002C5685F98A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.130479", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method commit of sqlite3.Connection object at 0x000002C5685F98A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.130479", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method rollback of sqlite3.Connection object at 0x000002C5685F98A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:42:09.130479", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method rollback of sqlite3.Connection object at 0x000002C5685F98A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:42:09.131282", "level": "INFO", "logger": "src.core.database", "message": "Database tables created successfully", "module": "database", "function": "_create_tables", "line": 84}
{"timestamp": "2025-06-30T10:42:09.131282", "level": "INFO", "logger": "src.core.database", "message": "Database initialized successfully", "module": "database", "function": "initialize", "line": 73}
{"timestamp": "2025-06-30T10:42:09.131282", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T10:42:09.131282", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T10:42:09.131282", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: light", "module": "theme_manager", "function": "_apply_theme", "line": 100}
{"timestamp": "2025-06-30T10:42:09.131282", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 88}
{"timestamp": "2025-06-30T10:42:09.131282", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: light", "module": "theme_manager", "function": "_apply_theme", "line": 100}
{"timestamp": "2025-06-30T10:42:09.131282", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from light to light", "module": "theme_manager", "function": "set_theme", "line": 88}
{"timestamp": "2025-06-30T10:42:09.131282", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_llm (llm)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:42:09.131282", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: zhipu_llm (llm)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:42:09.132639", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: pollinations_image (image)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:42:09.132639", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_image (image)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:42:09.132639", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: edge_tts (voice)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:42:09.132639", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_tts (voice)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:42:09.132639", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: cogvideox (video)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:42:09.132639", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: local_composer (video)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:42:09.132639", "level": "INFO", "logger": "src.services.registry", "message": "Started health monitoring", "module": "registry", "function": "start_health_monitoring", "line": 254}
{"timestamp": "2025-06-30T10:42:09.132639", "level": "INFO", "logger": "__main__", "message": "Application initialized successfully", "module": "main", "function": "initialize", "line": 78}
{"timestamp": "2025-06-30T10:42:09.516263", "level": "INFO", "logger": "__main__", "message": "Application started successfully", "module": "main", "function": "run", "line": 152}
{"timestamp": "2025-06-30T10:43:24.377950", "level": "INFO", "logger": "__main__", "message": "Received signal 2, shutting down...", "module": "main", "function": "signal_handler", "line": 168}
{"timestamp": "2025-06-30T10:43:24.377950", "level": "INFO", "logger": "__main__", "message": "Shutting down application...", "module": "main", "function": "shutdown", "line": 182}
{"timestamp": "2025-06-30T10:43:24.377950", "level": "INFO", "logger": "src.ui.main_window", "message": "Main window closing", "module": "main_window", "function": "closeEvent", "line": 392}
{"timestamp": "2025-06-30T10:43:24.390099", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Connection object at 0x000002C5685F98A0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:43:24.390099", "level": "ERROR", "logger": "sqlalchemy.pool.impl.AsyncAdaptedQueuePool", "message": "Exception closing connection <AdaptedConnection <Connection(Thread-1, started daemon 16352)>>", "module": "base", "function": "_close_connection", "line": 378, "exception": {"type": "CancelledError", "message": "", "traceback": "Traceback (most recent call last):\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 376, in _close_connection\n    self._dialect.do_close(connection)\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py\", line 712, in do_close\n    dbapi_connection.close()\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py\", line 277, in close\n    self.await_(self._connection.close())\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py\", line 132, in await_only\n    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py\", line 196, in greenlet_spawn\n    value = await result\n            ^^^^^^^^^^^^\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\aiosqlite\\core.py\", line 168, in close\n    await self._execute(self._conn.close)\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\aiosqlite\\core.py\", line 122, in _execute\n    return await future\n           ^^^^^^^^^^^^\nasyncio.exceptions.CancelledError"}}
{"timestamp": "2025-06-30T10:43:24.391100", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Connection object at 0x000002C5685F98A0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:43:24.394625", "level": "INFO", "logger": "src.services.registry", "message": "Stopped health monitoring", "module": "registry", "function": "stop_health_monitoring", "line": 265}
{"timestamp": "2025-06-30T10:44:00.018361", "level": "INFO", "logger": "__main__", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 60}
{"timestamp": "2025-06-30T10:44:00.027417", "level": "DEBUG", "logger": "aiosqlite", "message": "executing <function connect.<locals>.connector at 0x000002B6BB3467A0>", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "operation <function connect.<locals>.connector at 0x000002B6BB3467A0> completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method create_function of sqlite3.Connection object at 0x000002B6BB349990>, 'regexp', 2, <function SQLiteDialect_pysqlite.on_connect.<locals>.regexp at 0x000002B6BB345080>, deterministic=True)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method create_function of sqlite3.Connection object at 0x000002B6BB349990>, 'regexp', 2, <function SQLiteDialect_pysqlite.on_connect.<locals>.regexp at 0x000002B6BB345080>, deterministic=True) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method create_function of sqlite3.Connection object at 0x000002B6BB349990>, 'floor', 1, <built-in function floor>, deterministic=True)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method create_function of sqlite3.Connection object at 0x000002B6BB349990>, 'floor', 1, <built-in function floor>, deterministic=True) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002B6BB349990>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002B6BB349990>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002B6BB31EA40>, 'PRAGMA read_uncommitted', [])", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002B6BB31EA40>, 'PRAGMA read_uncommitted', []) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002B6BB31EA40>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002B6BB31EA40>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002B6BB31EA40>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002B6BB31EA40>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method rollback of sqlite3.Connection object at 0x000002B6BB349990>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method rollback of sqlite3.Connection object at 0x000002B6BB349990>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002B6BB349990>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002B6BB349990>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002B6BB31EAC0>, 'PRAGMA main.table_info(\"projects\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002B6BB31EAC0>, 'PRAGMA main.table_info(\"projects\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002B6BB31EAC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002B6BB31EAC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002B6BB31EAC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002B6BB31EAC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002B6BB349990>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002B6BB349990>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002B6BB31EAC0>, 'PRAGMA main.table_info(\"storyboards\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.028416", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002B6BB31EAC0>, 'PRAGMA main.table_info(\"storyboards\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.033445", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002B6BB31EAC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.033445", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002B6BB31EAC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.033445", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002B6BB31EAC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.033445", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002B6BB31EAC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.033445", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002B6BB349990>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.033445", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002B6BB349990>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.033445", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002B6BB31EAC0>, 'PRAGMA main.table_info(\"shots\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.033445", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002B6BB31EAC0>, 'PRAGMA main.table_info(\"shots\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.033445", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002B6BB31EAC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.033445", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002B6BB31EAC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.033445", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002B6BB31EAC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.033445", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002B6BB31EAC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.033445", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002B6BB349990>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.033445", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002B6BB349990>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.035683", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002B6BB31EAC0>, 'PRAGMA main.table_info(\"media_items\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.035683", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002B6BB31EAC0>, 'PRAGMA main.table_info(\"media_items\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.035683", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002B6BB31EAC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.035683", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002B6BB31EAC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.035683", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002B6BB31EAC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.035683", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002B6BB31EAC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.036862", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method commit of sqlite3.Connection object at 0x000002B6BB349990>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.036862", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method commit of sqlite3.Connection object at 0x000002B6BB349990>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.036862", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method rollback of sqlite3.Connection object at 0x000002B6BB349990>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:44:00.036862", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method rollback of sqlite3.Connection object at 0x000002B6BB349990>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:44:00.036862", "level": "INFO", "logger": "src.core.database", "message": "Database tables created successfully", "module": "database", "function": "_create_tables", "line": 84}
{"timestamp": "2025-06-30T10:44:00.036862", "level": "INFO", "logger": "src.core.database", "message": "Database initialized successfully", "module": "database", "function": "initialize", "line": 73}
{"timestamp": "2025-06-30T10:44:00.037862", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T10:44:00.037862", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T10:44:00.037862", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: light", "module": "theme_manager", "function": "_apply_theme", "line": 100}
{"timestamp": "2025-06-30T10:44:00.037862", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 88}
{"timestamp": "2025-06-30T10:44:00.037862", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: light", "module": "theme_manager", "function": "_apply_theme", "line": 100}
{"timestamp": "2025-06-30T10:44:00.037862", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from light to light", "module": "theme_manager", "function": "set_theme", "line": 88}
{"timestamp": "2025-06-30T10:44:00.037862", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_llm (llm)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:44:00.037862", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: zhipu_llm (llm)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:44:00.037862", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: pollinations_image (image)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:44:00.038862", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_image (image)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:44:00.038862", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: edge_tts (voice)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:44:00.038862", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_tts (voice)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:44:00.038862", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: cogvideox (video)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:44:00.038862", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: local_composer (video)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:44:00.038862", "level": "INFO", "logger": "src.services.registry", "message": "Started health monitoring", "module": "registry", "function": "start_health_monitoring", "line": 254}
{"timestamp": "2025-06-30T10:44:00.038862", "level": "INFO", "logger": "__main__", "message": "Application initialized successfully", "module": "main", "function": "initialize", "line": 78}
{"timestamp": "2025-06-30T10:44:00.414484", "level": "INFO", "logger": "__main__", "message": "Application started successfully", "module": "main", "function": "run", "line": 152}
{"timestamp": "2025-06-30T10:46:27.727858", "level": "INFO", "logger": "__main__", "message": "Received signal 2, shutting down...", "module": "main", "function": "signal_handler", "line": 168}
{"timestamp": "2025-06-30T10:46:27.727858", "level": "INFO", "logger": "__main__", "message": "Shutting down application...", "module": "main", "function": "shutdown", "line": 182}
{"timestamp": "2025-06-30T10:46:27.740849", "level": "INFO", "logger": "src.ui.main_window", "message": "Main window closing", "module": "main_window", "function": "closeEvent", "line": 392}
{"timestamp": "2025-06-30T10:46:27.740849", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Connection object at 0x000002B6BB349990>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:27.741848", "level": "ERROR", "logger": "sqlalchemy.pool.impl.AsyncAdaptedQueuePool", "message": "Exception closing connection <AdaptedConnection <Connection(Thread-1, started daemon 24312)>>", "module": "base", "function": "_close_connection", "line": 378, "exception": {"type": "CancelledError", "message": "", "traceback": "Traceback (most recent call last):\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 376, in _close_connection\n    self._dialect.do_close(connection)\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py\", line 712, in do_close\n    dbapi_connection.close()\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py\", line 277, in close\n    self.await_(self._connection.close())\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py\", line 132, in await_only\n    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py\", line 196, in greenlet_spawn\n    value = await result\n            ^^^^^^^^^^^^\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\aiosqlite\\core.py\", line 168, in close\n    await self._execute(self._conn.close)\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\aiosqlite\\core.py\", line 122, in _execute\n    return await future\n           ^^^^^^^^^^^^\nasyncio.exceptions.CancelledError"}}
{"timestamp": "2025-06-30T10:46:27.741848", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Connection object at 0x000002B6BB349990>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:27.745355", "level": "INFO", "logger": "src.services.registry", "message": "Stopped health monitoring", "module": "registry", "function": "stop_health_monitoring", "line": 265}
{"timestamp": "2025-06-30T10:46:34.494155", "level": "INFO", "logger": "main", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 60}
{"timestamp": "2025-06-30T10:46:34.506430", "level": "DEBUG", "logger": "aiosqlite", "message": "executing <function connect.<locals>.connector at 0x000002DE0006B9C0>", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.506430", "level": "DEBUG", "logger": "aiosqlite", "message": "operation <function connect.<locals>.connector at 0x000002DE0006B9C0> completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.506430", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method create_function of sqlite3.Connection object at 0x000002DE000746D0>, 'regexp', 2, <function SQLiteDialect_pysqlite.on_connect.<locals>.regexp at 0x000002DE0006A2A0>, deterministic=True)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.506430", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method create_function of sqlite3.Connection object at 0x000002DE000746D0>, 'regexp', 2, <function SQLiteDialect_pysqlite.on_connect.<locals>.regexp at 0x000002DE0006A2A0>, deterministic=True) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.506430", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method create_function of sqlite3.Connection object at 0x000002DE000746D0>, 'floor', 1, <built-in function floor>, deterministic=True)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.506430", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method create_function of sqlite3.Connection object at 0x000002DE000746D0>, 'floor', 1, <built-in function floor>, deterministic=True) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.509271", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002DE000746D0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.509271", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002DE000746D0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.509271", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002DE00046940>, 'PRAGMA read_uncommitted', [])", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.509271", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002DE00046940>, 'PRAGMA read_uncommitted', []) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.509271", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002DE00046940>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.510280", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002DE00046940>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.510280", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002DE00046940>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.510280", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002DE00046940>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.510280", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method rollback of sqlite3.Connection object at 0x000002DE000746D0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.510280", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method rollback of sqlite3.Connection object at 0x000002DE000746D0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.511278", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002DE000746D0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.511278", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002DE000746D0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.511278", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002DE000468C0>, 'PRAGMA main.table_info(\"projects\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.511278", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002DE000468C0>, 'PRAGMA main.table_info(\"projects\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.511278", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002DE000468C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.512279", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002DE000468C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.512279", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002DE000468C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.512279", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002DE000468C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.512279", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002DE000746D0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.512279", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002DE000746D0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.512279", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002DE00046940>, 'PRAGMA main.table_info(\"storyboards\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002DE00046940>, 'PRAGMA main.table_info(\"storyboards\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002DE00046940>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002DE00046940>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002DE00046940>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002DE00046940>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002DE000746D0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002DE000746D0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002DE00046740>, 'PRAGMA main.table_info(\"shots\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002DE00046740>, 'PRAGMA main.table_info(\"shots\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002DE00046740>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002DE00046740>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002DE00046740>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002DE00046740>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002DE000746D0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x000002DE000746D0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002DE00046AC0>, 'PRAGMA main.table_info(\"media_items\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x000002DE00046AC0>, 'PRAGMA main.table_info(\"media_items\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002DE00046AC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x000002DE00046AC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002DE00046AC0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.513158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x000002DE00046AC0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.517361", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method commit of sqlite3.Connection object at 0x000002DE000746D0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.517361", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method commit of sqlite3.Connection object at 0x000002DE000746D0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.517361", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method rollback of sqlite3.Connection object at 0x000002DE000746D0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:46:34.517361", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method rollback of sqlite3.Connection object at 0x000002DE000746D0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:46:34.518074", "level": "INFO", "logger": "src.core.database", "message": "Database tables created successfully", "module": "database", "function": "_create_tables", "line": 84}
{"timestamp": "2025-06-30T10:46:34.518074", "level": "INFO", "logger": "src.core.database", "message": "Database initialized successfully", "module": "database", "function": "initialize", "line": 73}
{"timestamp": "2025-06-30T10:46:34.518074", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T10:46:34.518074", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T10:46:34.518074", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: light", "module": "theme_manager", "function": "_apply_theme", "line": 100}
{"timestamp": "2025-06-30T10:46:34.518074", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 88}
{"timestamp": "2025-06-30T10:46:34.519079", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: light", "module": "theme_manager", "function": "_apply_theme", "line": 100}
{"timestamp": "2025-06-30T10:46:34.519079", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from light to light", "module": "theme_manager", "function": "set_theme", "line": 88}
{"timestamp": "2025-06-30T10:46:34.519079", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_llm (llm)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:46:34.519079", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: zhipu_llm (llm)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:46:34.519079", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: pollinations_image (image)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:46:34.519079", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_image (image)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:46:34.519079", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: edge_tts (voice)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:46:34.519079", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_tts (voice)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:46:34.520079", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: cogvideox (video)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:46:34.520079", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: local_composer (video)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:46:34.520079", "level": "INFO", "logger": "src.services.registry", "message": "Started health monitoring", "module": "registry", "function": "start_health_monitoring", "line": 254}
{"timestamp": "2025-06-30T10:46:34.520079", "level": "INFO", "logger": "main", "message": "Application initialized successfully", "module": "main", "function": "initialize", "line": 78}
{"timestamp": "2025-06-30T10:46:34.906620", "level": "INFO", "logger": "main", "message": "Application started successfully", "module": "main", "function": "run", "line": 152}
{"timestamp": "2025-06-30T10:46:55.073594", "level": "INFO", "logger": "src.ui.components.content_area", "message": "Open project dialog completed", "module": "content_area", "function": "show_open_project_dialog", "line": 375}
{"timestamp": "2025-06-30T10:47:04.139870", "level": "INFO", "logger": "src.ui.components.content_area", "message": "Creating new project: 妈的", "module": "content_area", "function": "show_new_project_dialog", "line": 352}
{"timestamp": "2025-06-30T10:47:05.164814", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog completed", "module": "content_area", "function": "show_new_project_dialog", "line": 357}
{"timestamp": "2025-06-30T10:47:09.400729", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: projects", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:47:09.401736", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: projects", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:47:09.401736", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: projects", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 120}
{"timestamp": "2025-06-30T10:47:11.504103", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: storyboard", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:47:11.504103", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: storyboard", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:47:11.504103", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: storyboard", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 120}
{"timestamp": "2025-06-30T10:47:33.327852", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: dashboard", "module": "main_window", "function": "_on_page_changed", "line": 303}
{"timestamp": "2025-06-30T10:47:33.327852", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: dashboard", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:47:33.327852", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: dashboard", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 120}
{"timestamp": "2025-06-30T10:48:02.713857", "level": "INFO", "logger": "src.ui.main_window", "message": "Main window closing", "module": "main_window", "function": "closeEvent", "line": 392}
{"timestamp": "2025-06-30T10:49:23.218023", "level": "INFO", "logger": "main", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 60}
{"timestamp": "2025-06-30T10:49:23.230111", "level": "DEBUG", "logger": "aiosqlite", "message": "executing <function connect.<locals>.connector at 0x0000025CB404BB00>", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.232054", "level": "DEBUG", "logger": "aiosqlite", "message": "operation <function connect.<locals>.connector at 0x0000025CB404BB00> completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.232605", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method create_function of sqlite3.Connection object at 0x0000025CB40547C0>, 'regexp', 2, <function SQLiteDialect_pysqlite.on_connect.<locals>.regexp at 0x0000025CB404A3E0>, deterministic=True)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.233111", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method create_function of sqlite3.Connection object at 0x0000025CB40547C0>, 'regexp', 2, <function SQLiteDialect_pysqlite.on_connect.<locals>.regexp at 0x0000025CB404A3E0>, deterministic=True) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.233639", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method create_function of sqlite3.Connection object at 0x0000025CB40547C0>, 'floor', 1, <built-in function floor>, deterministic=True)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.233639", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method create_function of sqlite3.Connection object at 0x0000025CB40547C0>, 'floor', 1, <built-in function floor>, deterministic=True) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.234165", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000025CB40547C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.234165", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000025CB40547C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.234165", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000025CB402A8C0>, 'PRAGMA read_uncommitted', [])", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.234719", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000025CB402A8C0>, 'PRAGMA read_uncommitted', []) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.234719", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000025CB402A8C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.235272", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000025CB402A8C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.235272", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000025CB402A8C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.235272", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000025CB402A8C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.235835", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method rollback of sqlite3.Connection object at 0x0000025CB40547C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.235835", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method rollback of sqlite3.Connection object at 0x0000025CB40547C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.236387", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000025CB40547C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.236387", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000025CB40547C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.236387", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000025CB402A840>, 'PRAGMA main.table_info(\"projects\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.236942", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000025CB402A840>, 'PRAGMA main.table_info(\"projects\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.236942", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000025CB402A840>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.236942", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000025CB402A840>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.237496", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000025CB402A840>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.237496", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000025CB402A840>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.238055", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000025CB40547C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.238055", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000025CB40547C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.238055", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000025CB402A8C0>, 'PRAGMA main.table_info(\"storyboards\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.238055", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000025CB402A8C0>, 'PRAGMA main.table_info(\"storyboards\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.238055", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000025CB402A8C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.238055", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000025CB402A8C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.238055", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000025CB402A8C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.238055", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000025CB402A8C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.239158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000025CB40547C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.239158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000025CB40547C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.239158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000025CB402A6C0>, 'PRAGMA main.table_info(\"shots\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.239158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000025CB402A6C0>, 'PRAGMA main.table_info(\"shots\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.239158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000025CB402A6C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.239158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000025CB402A6C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.239158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000025CB402A6C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.240158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000025CB402A6C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.240158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000025CB40547C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.240158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method cursor of sqlite3.Connection object at 0x0000025CB40547C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.240158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000025CB402AA40>, 'PRAGMA main.table_info(\"media_items\")', ())", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.240158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method execute of sqlite3.Cursor object at 0x0000025CB402AA40>, 'PRAGMA main.table_info(\"media_items\")', ()) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.240158", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000025CB402AA40>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.240158", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method fetchall of sqlite3.Cursor object at 0x0000025CB402AA40>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.241157", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000025CB402AA40>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.241157", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method close of sqlite3.Cursor object at 0x0000025CB402AA40>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.241157", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method commit of sqlite3.Connection object at 0x0000025CB40547C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.241157", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method commit of sqlite3.Connection object at 0x0000025CB40547C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.242095", "level": "DEBUG", "logger": "aiosqlite", "message": "executing functools.partial(<built-in method rollback of sqlite3.Connection object at 0x0000025CB40547C0>)", "module": "core", "function": "run", "line": 104}
{"timestamp": "2025-06-30T10:49:23.242095", "level": "DEBUG", "logger": "aiosqlite", "message": "operation functools.partial(<built-in method rollback of sqlite3.Connection object at 0x0000025CB40547C0>) completed", "module": "core", "function": "run", "line": 106}
{"timestamp": "2025-06-30T10:49:23.242095", "level": "INFO", "logger": "src.core.database", "message": "Database tables created successfully", "module": "database", "function": "_create_tables", "line": 84}
{"timestamp": "2025-06-30T10:49:23.242095", "level": "INFO", "logger": "src.core.database", "message": "Database initialized successfully", "module": "database", "function": "initialize", "line": 73}
{"timestamp": "2025-06-30T10:49:23.242095", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T10:49:23.242898", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T10:49:23.242898", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: light", "module": "theme_manager", "function": "_apply_theme", "line": 100}
{"timestamp": "2025-06-30T10:49:23.243384", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 88}
{"timestamp": "2025-06-30T10:49:23.243384", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: light", "module": "theme_manager", "function": "_apply_theme", "line": 100}
{"timestamp": "2025-06-30T10:49:23.243384", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from light to light", "module": "theme_manager", "function": "set_theme", "line": 88}
{"timestamp": "2025-06-30T10:49:23.243384", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_llm (llm)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:49:23.243384", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: zhipu_llm (llm)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:49:23.243384", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: pollinations_image (image)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:49:23.243384", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_image (image)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:49:23.243384", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: edge_tts (voice)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:49:23.243384", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: openai_tts (voice)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:49:23.244388", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: cogvideox (video)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:49:23.244388", "level": "INFO", "logger": "src.services.registry", "message": "Registered service: local_composer (video)", "module": "registry", "function": "register_service", "line": 161}
{"timestamp": "2025-06-30T10:49:23.244388", "level": "INFO", "logger": "src.services.registry", "message": "Started health monitoring", "module": "registry", "function": "start_health_monitoring", "line": 254}
{"timestamp": "2025-06-30T10:49:23.244388", "level": "INFO", "logger": "main", "message": "Application initialized successfully", "module": "main", "function": "initialize", "line": 78}
{"timestamp": "2025-06-30T10:49:23.583457", "level": "INFO", "logger": "main", "message": "Application started successfully", "module": "main", "function": "run", "line": 152}
{"timestamp": "2025-06-30T10:49:27.225776", "level": "INFO", "logger": "src.ui.main_window", "message": "New project requested", "module": "main_window", "function": "_on_new_project", "line": 274}
{"timestamp": "2025-06-30T10:49:28.568581", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog completed", "module": "content_area", "function": "show_new_project_dialog", "line": 357}
{"timestamp": "2025-06-30T10:49:35.748890", "level": "DEBUG", "logger": "src.ui.themes.theme_manager", "message": "Applied stylesheet for theme: dark", "module": "theme_manager", "function": "_apply_theme", "line": 100}
{"timestamp": "2025-06-30T10:49:35.749398", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from light to dark", "module": "theme_manager", "function": "set_theme", "line": 88}
{"timestamp": "2025-06-30T10:50:16.458101", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: projects", "module": "main_window", "function": "_on_page_changed", "line": 308}
{"timestamp": "2025-06-30T10:50:16.458101", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: projects", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:50:16.458101", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: projects", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 118}
{"timestamp": "2025-06-30T10:50:18.344283", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: storyboard", "module": "main_window", "function": "_on_page_changed", "line": 308}
{"timestamp": "2025-06-30T10:50:18.344283", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: storyboard", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:50:18.344283", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: storyboard", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 118}
{"timestamp": "2025-06-30T10:50:19.313448", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: media", "module": "main_window", "function": "_on_page_changed", "line": 308}
{"timestamp": "2025-06-30T10:50:19.313897", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: media", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:50:19.313897", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: media", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 118}
{"timestamp": "2025-06-30T10:50:21.231704", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: settings", "module": "main_window", "function": "_on_page_changed", "line": 308}
{"timestamp": "2025-06-30T10:50:21.232729", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: settings", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:50:21.232729", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: settings", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 118}
{"timestamp": "2025-06-30T10:50:27.233838", "level": "INFO", "logger": "src.ui.main_window", "message": "Generate storyboard requested", "module": "main_window", "function": "_on_generate_storyboard", "line": 292}
{"timestamp": "2025-06-30T10:50:31.469020", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: media", "module": "main_window", "function": "_on_page_changed", "line": 308}
{"timestamp": "2025-06-30T10:50:31.469020", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: media", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:50:31.469020", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: media", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 118}
{"timestamp": "2025-06-30T10:50:32.749682", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: storyboard", "module": "main_window", "function": "_on_page_changed", "line": 308}
{"timestamp": "2025-06-30T10:50:32.749682", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: storyboard", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:50:32.752182", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: storyboard", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 118}
{"timestamp": "2025-06-30T10:50:33.480541", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: projects", "module": "main_window", "function": "_on_page_changed", "line": 308}
{"timestamp": "2025-06-30T10:50:33.480541", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: projects", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:50:33.480541", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: projects", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 118}
{"timestamp": "2025-06-30T10:50:34.174364", "level": "DEBUG", "logger": "src.ui.main_window", "message": "Page changed to: dashboard", "module": "main_window", "function": "_on_page_changed", "line": 308}
{"timestamp": "2025-06-30T10:50:34.174364", "level": "DEBUG", "logger": "src.ui.components.content_area", "message": "Switched to page: dashboard", "module": "content_area", "function": "show_page", "line": 307}
{"timestamp": "2025-06-30T10:50:34.174364", "level": "DEBUG", "logger": "src.ui.components.navigation_panel", "message": "Navigation changed to: dashboard", "module": "navigation_panel", "function": "_on_nav_item_changed", "line": 118}
{"timestamp": "2025-06-30T10:51:16.816590", "level": "INFO", "logger": "src.ui.main_window", "message": "Main window closing", "module": "main_window", "function": "closeEvent", "line": 399}
