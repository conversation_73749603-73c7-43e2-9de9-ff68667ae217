{"timestamp": "2025-06-29T16:37:39.303047", "level": "INFO", "logger": "src.main", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 35}
{"timestamp": "2025-06-29T16:37:39.329133", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-29T16:37:39.329133", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-29T16:37:39.329133", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 83}
{"timestamp": "2025-06-29T16:37:39.665458", "level": "ERROR", "logger": "src.main", "message": "Failed to initialize application: 'ContentArea' object has no attribute '_pages'", "module": "main", "function": "initialize", "line": 54}
{"timestamp": "2025-06-29T16:38:58.070397", "level": "INFO", "logger": "src.main", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 35}
{"timestamp": "2025-06-29T16:38:58.093000", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-29T16:38:58.093000", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-29T16:38:58.094006", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 83}
{"timestamp": "2025-06-29T16:38:58.337810", "level": "ERROR", "logger": "src.main", "message": "Failed to initialize application: 'ContentArea' object has no attribute '_pages'", "module": "main", "function": "initialize", "line": 54}
{"timestamp": "2025-06-29T16:40:07.042804", "level": "INFO", "logger": "src.main", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 35}
{"timestamp": "2025-06-29T16:40:07.064911", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-29T16:40:07.066199", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-29T16:40:07.066199", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 83}
{"timestamp": "2025-06-29T16:40:07.320283", "level": "ERROR", "logger": "src.main", "message": "Failed to initialize application: 'LoadingWidget' object has no attribute 'message'", "module": "main", "function": "initialize", "line": 54}
{"timestamp": "2025-06-29T16:40:40.062943", "level": "INFO", "logger": "src.main", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 35}
{"timestamp": "2025-06-29T16:40:40.086682", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-29T16:40:40.086682", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-29T16:40:40.087681", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 83}
{"timestamp": "2025-06-29T16:40:40.358840", "level": "INFO", "logger": "src.main", "message": "Application initialized successfully", "module": "main", "function": "initialize", "line": 49}
{"timestamp": "2025-06-29T16:40:54.815422", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog requested", "module": "content_area", "function": "show_new_project_dialog", "line": 283}
{"timestamp": "2025-06-29T16:40:55.759818", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog requested", "module": "content_area", "function": "show_new_project_dialog", "line": 283}
{"timestamp": "2025-06-29T16:40:57.721548", "level": "INFO", "logger": "src.ui.main_window", "message": "Generate storyboard requested", "module": "main_window", "function": "_on_generate_storyboard", "line": 286}
{"timestamp": "2025-06-29T16:41:11.392026", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from light to dark", "module": "theme_manager", "function": "set_theme", "line": 83}
{"timestamp": "2025-06-29T16:41:21.637393", "level": "INFO", "logger": "src.ui.main_window", "message": "Main window closing", "module": "main_window", "function": "closeEvent", "line": 391}
{"timestamp": "2025-06-30T09:46:25.876278", "level": "INFO", "logger": "src.main", "message": "Starting AI Video Generator 2.0...", "module": "main", "function": "initialize", "line": 35}
{"timestamp": "2025-06-30T09:46:25.929933", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: light", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T09:46:25.929933", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Registered theme: dark", "module": "theme_manager", "function": "register_theme", "line": 43}
{"timestamp": "2025-06-30T09:46:25.930932", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from unknown to light", "module": "theme_manager", "function": "set_theme", "line": 83}
{"timestamp": "2025-06-30T09:46:26.255709", "level": "INFO", "logger": "src.main", "message": "Application initialized successfully", "module": "main", "function": "initialize", "line": 49}
{"timestamp": "2025-06-30T09:46:50.981592", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog requested", "module": "content_area", "function": "show_new_project_dialog", "line": 283}
{"timestamp": "2025-06-30T09:46:51.726132", "level": "INFO", "logger": "src.ui.components.content_area", "message": "Open project dialog requested", "module": "content_area", "function": "show_open_project_dialog", "line": 289}
{"timestamp": "2025-06-30T09:46:56.989041", "level": "INFO", "logger": "src.ui.main_window", "message": "New project requested", "module": "main_window", "function": "_on_new_project", "line": 268}
{"timestamp": "2025-06-30T09:46:56.989041", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog requested", "module": "content_area", "function": "show_new_project_dialog", "line": 283}
{"timestamp": "2025-06-30T09:46:57.805953", "level": "INFO", "logger": "src.ui.main_window", "message": "New project requested", "module": "main_window", "function": "_on_new_project", "line": 268}
{"timestamp": "2025-06-30T09:46:57.805953", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog requested", "module": "content_area", "function": "show_new_project_dialog", "line": 283}
{"timestamp": "2025-06-30T09:46:58.334213", "level": "INFO", "logger": "src.ui.main_window", "message": "Open project requested", "module": "main_window", "function": "_on_open_project", "line": 274}
{"timestamp": "2025-06-30T09:46:58.335231", "level": "INFO", "logger": "src.ui.components.content_area", "message": "Open project dialog requested", "module": "content_area", "function": "show_open_project_dialog", "line": 289}
{"timestamp": "2025-06-30T09:46:58.805521", "level": "INFO", "logger": "src.ui.main_window", "message": "Save project requested", "module": "main_window", "function": "_on_save_project", "line": 280}
{"timestamp": "2025-06-30T09:46:58.808341", "level": "INFO", "logger": "src.ui.components.content_area", "message": "Save project requested", "module": "content_area", "function": "save_current_project", "line": 295}
{"timestamp": "2025-06-30T09:46:59.333549", "level": "INFO", "logger": "src.ui.main_window", "message": "Generate storyboard requested", "module": "main_window", "function": "_on_generate_storyboard", "line": 286}
{"timestamp": "2025-06-30T09:47:00.958261", "level": "INFO", "logger": "src.ui.main_window", "message": "New project requested", "module": "main_window", "function": "_on_new_project", "line": 268}
{"timestamp": "2025-06-30T09:47:00.958261", "level": "INFO", "logger": "src.ui.components.content_area", "message": "New project dialog requested", "module": "content_area", "function": "show_new_project_dialog", "line": 283}
{"timestamp": "2025-06-30T09:47:04.007178", "level": "INFO", "logger": "src.ui.themes.theme_manager", "message": "Theme changed from light to dark", "module": "theme_manager", "function": "set_theme", "line": 83}
{"timestamp": "2025-06-30T09:47:04.007354", "level": "INFO", "logger": "src.ui.main_window", "message": "Theme toggled to: dark", "module": "main_window", "function": "_on_toggle_theme", "line": 293}
{"timestamp": "2025-06-30T09:47:28.662482", "level": "INFO", "logger": "src.ui.main_window", "message": "Main window closing", "module": "main_window", "function": "closeEvent", "line": 391}
