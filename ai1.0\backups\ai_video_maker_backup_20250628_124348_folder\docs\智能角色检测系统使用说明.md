# 智能角色检测系统使用说明

## 概述

新的智能角色检测系统能够处理各种复杂的角色称谓，包括动物主角、特殊类型角色等，解决了原有系统只能处理简单人类角色的局限性。

## 系统特点

### 1. 多层次检测策略

系统采用三层检测策略，按优先级依次执行：

1. **基础同义词匹配**：使用预定义的同义词映射表
2. **角色类型和特征匹配**：根据角色类型进行专门的匹配逻辑
3. **语义相似度匹配**：使用LLM进行智能语义分析

### 2. 支持的角色类型

#### 通用角色称谓
- 主角、主人公、男主、女主
- 反派、配角、朋友、敌人
- 路人、群众等

#### 动物角色
- **常见宠物**：小狗、小猫、小鸟、小兔、小鱼等
- **野生动物**：老虎、狮子、大象、熊猫、猴子等
- **海洋动物**：海豚、鲸鱼、乌龟等
- **神话动物**：龙、凤凰、麒麟等

#### 职业/身份角色
- 医生、老师、警察、司机、学生
- 工人、农民、商人、军人、厨师等

#### 年龄相关称谓
- 小孩、少年、青年、中年、老人
- 对应的各种称谓变体

#### 特殊类型角色
- 机器人、外星人、精灵、天使、恶魔
- 神仙、僵尸、吸血鬼等

## 使用示例

### 1. 动物主角检测

```python
# 角色定义
character_data = {
    "name": "小白",
    "type": "animal",
    "appearance": {
        "species": "狗",
        "color": "白色"
    }
}

# 描述文本
description = "一只忠诚的狗狗摇着尾巴跑向主人"

# 系统能够识别：
# - "狗狗" 是 "小狗" 的同义词
# - "摇着尾巴" 是狗的特征描述
# - 结果：成功匹配
```

### 2. 复杂称谓检测

```python
# 角色定义
character_data = {
    "name": "主角",
    "type": "human",
    "appearance": {
        "gender": "男",
        "age_range": "20-30岁"
    }
}

# 描述文本
description = "这位年轻的男主人公勇敢地面对挑战"

# 系统能够识别：
# - "男主人公" 是 "主角" 的同义词
# - "年轻" 符合年龄特征
# - 结果：成功匹配
```

### 3. 特殊类型角色检测

```python
# 角色定义
character_data = {
    "name": "机器人助手",
    "type": "robot",
    "appearance": {
        "material": "金属",
        "features": "电子眼"
    }
}

# 描述文本
description = "这个金属制造的人工智能助手闪烁着电子光芒"

# 系统能够识别：
# - "金属" 是机器人的特征描述
# - "人工智能" 是机器人的同义词
# - 结果：成功匹配
```

## 配置和扩展

### 1. 添加自定义同义词

```python
from src.utils.character_detection_config import CharacterDetectionConfig

# 添加自定义同义词映射
custom_synonyms = {
    "超级英雄": ["英雄", "救世主", "守护者"],
    "魔法师": ["法师", "巫师", "术士"]
}

CharacterDetectionConfig.add_custom_synonyms(custom_synonyms)
```

### 2. 添加自定义动物描述词

```python
# 添加自定义动物描述词
custom_descriptors = {
    "独角兽": ["独角", "纯洁", "魔法", "彩虹"],
    "凤凰": ["重生", "火焰", "不死", "神鸟"]
}

CharacterDetectionConfig.add_custom_animal_descriptors(custom_descriptors)
```

## 系统优势

### 1. 高度可扩展
- 配置文件化管理，易于添加新的角色类型和同义词
- 支持运行时动态添加自定义规则

### 2. 智能匹配
- 多层次检测策略，提高匹配准确率
- LLM语义分析作为最后保障

### 3. 广泛适用
- 支持各种文学作品中的角色类型
- 特别适合包含动物主角的童话、寓言等

### 4. 性能优化
- 按优先级执行，避免不必要的LLM调用
- 缓存机制减少重复计算

## 注意事项

1. **LLM调用成本**：系统会优先使用规则匹配，只在必要时调用LLM
2. **配置维护**：定期更新同义词映射以适应新的使用场景
3. **性能监控**：关注角色检测的准确率和响应时间

## 未来扩展方向

1. **机器学习增强**：训练专门的角色识别模型
2. **上下文分析**：考虑文本上下文进行更精确的匹配
3. **多语言支持**：扩展到其他语言的角色检测
4. **用户反馈学习**：根据用户反馈自动优化检测规则

通过这个智能角色检测系统，程序现在能够处理各种复杂的角色称谓，包括动物主角、特殊类型角色等，大大提升了系统的适用性和智能化水平。