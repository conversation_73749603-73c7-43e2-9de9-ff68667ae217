{"_comment": "AI视频生成器3.0 实际配置文件", "_note": "基于原有ai1.0配置和用户提供的API密钥", "llm": {"routing_strategy": "quality_first", "clients": {"zhipu": {"api_key": "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY", "base_url": "https://open.bigmodel.cn/api/paas/v4/", "model": "glm-4-flash"}, "qwen": {"api_key": "sk-ab30df729a9b4df287db20a8f47ba12c", "base_url": "https://dashscope.aliyuncs.com/api/v1/", "model": "qwen-turbo"}, "deepseek": {"api_key": "***********************************", "base_url": "https://api.deepseek.com/v1/", "model": "deepseek-chat"}, "gemini": {"api_key": "AIzaSyA3Nh4nxQYoaZRxlJWUpvvVR_kU-ihITok", "model": "gemini-1.5-flash"}}}, "translation": {"routing_strategy": "quality_first", "clients": {"baidu": {"app_id": "*****************", "secret_key": "fpPftxwOvbIGAWwmkucK", "base_url": "https://fanyi-api.baidu.com/api/trans/vip/translate"}, "google": {"api_key": "AIzaSyA3Nh4nxQYoaZRxlJWUpvvVR_kU-ihITok"}}}, "tts": {"default_engine": "edge", "engines": {"edge_tts": {"voice": "zh-CN-YunxiNeural", "rate": "+0%", "pitch": "+0Hz"}, "siliconflow": {"api_key": "", "base_url": "https://api.siliconflow.cn/v1/audio/speech", "model": "FishSpeech", "voice": "fishaudio_fish_speech_1"}}}, "image_generation": {"routing_strategy": "quality_first", "engines": {"pollinations": {"base_url": "https://image.pollinations.ai/prompt", "width": 1024, "height": 1024, "model": "flux"}, "comfyui_local": {"base_url": "http://127.0.0.1:8188", "workflow_file": "workflow_api.json"}, "comfyui_cloud": {"api_key": "", "base_url": "https://api.comfy.icu"}, "dalle": {"api_key": "", "model": "dall-e-3", "size": "1024x1024", "quality": "standard"}, "stability": {"api_key": "", "base_url": "https://api.stability.ai", "model": "stable-diffusion-xl-1024-v1-0"}, "google_imagen": {"api_key": "AIzaSyA3Nh4nxQYoaZRxlJWUpvvVR_kU-ihITok", "model": "imagen-3.0-generate-001"}, "cogview": {"api_key": "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY", "base_url": "https://open.bigmodel.cn/api/paas/v4/", "model": "cogview-3-flash"}}}, "video_generation": {"routing_strategy": "quality_first", "engines": {"cogvideox": {"api_key": "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY", "base_url": "https://open.bigmodel.cn/api/paas/v4/", "model": "cogvideox-flash"}, "replicate_svd": {"api_key": "", "model": "stability-ai/stable-video-diffusion:3f0457e4619daac51203dedb1a4919c746e16d22e4ccb1e87c1b5e5d7b4c5b1c"}, "pixverse": {"api_key": "", "base_url": "https://api.pixverse.ai"}, "haiper": {"api_key": "", "base_url": "https://api.haiper.ai"}, "runway": {"api_key": "", "base_url": "https://api.runwayml.com"}, "pika": {"api_key": "", "base_url": "https://api.pika.art"}}}, "social_media": {"platforms": {"youtube": {"client_id": "", "client_secret": "", "refresh_token": "", "api_key": ""}, "tiktok": {"client_key": "", "client_secret": "", "access_token": ""}, "wechat_channels": {"app_id": "", "app_secret": "", "access_token": ""}, "douyin": {"client_key": "", "client_secret": "", "access_token": ""}, "bilibili": {"access_token": "", "refresh_token": "", "csrf": ""}}}, "workflow": {"max_retries": 3, "timeout": 300, "parallel_tasks": 4, "temp_dir": "./temp", "output_dir": "./output", "log_level": "INFO"}, "performance": {"enable_caching": true, "cache_ttl": 3600, "max_concurrent_requests": 10, "request_timeout": 60}, "security": {"encrypt_api_keys": false, "log_api_requests": false, "rate_limiting": {"enabled": true, "requests_per_minute": 60}}}