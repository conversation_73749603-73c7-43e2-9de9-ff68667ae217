"""事件系统

提供发布-订阅模式的事件处理机制。
"""

from typing import Dict, List, Callable, Any, Optional, Union
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import asyncio
import logging
from uuid import uuid4, UUID

logger = logging.getLogger(__name__)


class EventPriority(Enum):
    """事件优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class Event:
    """事件基类"""
    name: str
    data: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    source: str = ""
    event_id: UUID = field(default_factory=uuid4)
    priority: EventPriority = EventPriority.NORMAL
    
    def __post_init__(self):
        if isinstance(self.priority, int):
            self.priority = EventPriority(self.priority)


class EventHandler(ABC):
    """事件处理器接口"""
    
    @abstractmethod
    async def handle(self, event: Event) -> None:
        """处理事件"""
        pass
    
    @property
    def handler_name(self) -> str:
        """处理器名称"""
        return self.__class__.__name__


class AsyncEventHandler(EventHandler):
    """异步事件处理器"""
    
    def __init__(self, handler_func: Callable[[Event], Any]):
        self.handler_func = handler_func
        self._handler_name = getattr(handler_func, '__name__', 'AsyncHandler')
    
    async def handle(self, event: Event) -> None:
        """处理事件"""
        if asyncio.iscoroutinefunction(self.handler_func):
            await self.handler_func(event)
        else:
            self.handler_func(event)
    
    @property
    def handler_name(self) -> str:
        return self._handler_name


class EventBus:
    """事件总线"""
    
    def __init__(self, max_queue_size: int = 1000):
        self._handlers: Dict[str, List[EventHandler]] = {}
        self._event_queue: asyncio.Queue = asyncio.Queue(maxsize=max_queue_size)
        self._running = False
        self._worker_task: Optional[asyncio.Task] = None
        self._stats = {
            'published': 0,
            'processed': 0,
            'failed': 0
        }
    
    def subscribe(self, event_name: str, handler: Union[EventHandler, Callable]) -> None:
        """订阅事件"""
        if not isinstance(handler, EventHandler):
            handler = AsyncEventHandler(handler)
        
        if event_name not in self._handlers:
            self._handlers[event_name] = []
        
        self._handlers[event_name].append(handler)
        logger.debug(f"Subscribed {handler.handler_name} to event {event_name}")
    
    def unsubscribe(self, event_name: str, handler: EventHandler) -> None:
        """取消订阅"""
        if event_name in self._handlers:
            if handler in self._handlers[event_name]:
                self._handlers[event_name].remove(handler)
                logger.debug(f"Unsubscribed {handler.handler_name} from event {event_name}")
            
            # 如果没有处理器了，删除事件
            if not self._handlers[event_name]:
                del self._handlers[event_name]
    
    async def publish(self, event: Event) -> None:
        """发布事件"""
        try:
            await self._event_queue.put(event)
            self._stats['published'] += 1
            logger.debug(f"Published event {event.name} with ID {event.event_id}")
        except asyncio.QueueFull:
            logger.error(f"Event queue is full, dropping event {event.name}")
    
    def publish_sync(self, event: Event) -> None:
        """同步发布事件（立即处理）"""
        asyncio.create_task(self._handle_event(event))
    
    async def start(self) -> None:
        """启动事件总线"""
        if self._running:
            return
        
        self._running = True
        self._worker_task = asyncio.create_task(self._process_events())
        logger.info("Event bus started")
    
    async def stop(self) -> None:
        """停止事件总线"""
        if not self._running:
            return
        
        self._running = False
        
        if self._worker_task:
            self._worker_task.cancel()
            try:
                await self._worker_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Event bus stopped")
    
    async def _process_events(self) -> None:
        """处理事件队列"""
        while self._running:
            try:
                # 等待事件，超时1秒
                event = await asyncio.wait_for(
                    self._event_queue.get(), timeout=1.0
                )
                await self._handle_event(event)
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error processing event: {e}")
    
    async def _handle_event(self, event: Event) -> None:
        """处理单个事件"""
        handlers = self._handlers.get(event.name, [])
        if not handlers:
            logger.debug(f"No handlers for event {event.name}")
            return
        
        # 按优先级排序处理器（如果需要）
        # handlers.sort(key=lambda h: getattr(h, 'priority', 0), reverse=True)
        
        tasks = []
        for handler in handlers:
            task = asyncio.create_task(self._safe_handle(handler, event))
            tasks.append(task)
        
        # 等待所有处理器完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        for result in results:
            if isinstance(result, Exception):
                self._stats['failed'] += 1
                logger.error(f"Handler failed for event {event.name}: {result}")
            else:
                self._stats['processed'] += 1
    
    async def _safe_handle(self, handler: EventHandler, event: Event) -> None:
        """安全地处理事件"""
        try:
            await handler.handle(event)
        except Exception as e:
            logger.error(f"Handler {handler.handler_name} failed: {e}")
            raise
    
    def get_stats(self) -> Dict[str, int]:
        """获取统计信息"""
        return self._stats.copy()
    
    def get_handlers(self, event_name: str) -> List[EventHandler]:
        """获取事件的处理器列表"""
        return self._handlers.get(event_name, []).copy()
    
    def clear_handlers(self, event_name: Optional[str] = None) -> None:
        """清除处理器"""
        if event_name:
            if event_name in self._handlers:
                del self._handlers[event_name]
        else:
            self._handlers.clear()


# 全局事件总线实例
_event_bus: Optional[EventBus] = None


def get_event_bus() -> EventBus:
    """获取全局事件总线实例"""
    global _event_bus
    if _event_bus is None:
        _event_bus = EventBus()
    return _event_bus


# 装饰器用于简化事件处理器注册
def event_handler(event_name: str, event_bus: Optional[EventBus] = None):
    """事件处理器装饰器"""
    def decorator(func):
        bus = event_bus or get_event_bus()
        bus.subscribe(event_name, func)
        return func
    return decorator


# 预定义的事件类型
class ApplicationEvents:
    """应用程序事件"""
    STARTUP = "app.startup"
    SHUTDOWN = "app.shutdown"
    ERROR = "app.error"


class ProjectEvents:
    """项目事件"""
    CREATED = "project.created"
    LOADED = "project.loaded"
    SAVED = "project.saved"
    DELETED = "project.deleted"


class StoryboardEvents:
    """分镜事件"""
    GENERATION_STARTED = "storyboard.generation.started"
    GENERATION_COMPLETED = "storyboard.generation.completed"
    GENERATION_FAILED = "storyboard.generation.failed"


class MediaEvents:
    """媒体事件"""
    IMAGE_GENERATION_STARTED = "media.image.generation.started"
    IMAGE_GENERATION_COMPLETED = "media.image.generation.completed"
    IMAGE_GENERATION_FAILED = "media.image.generation.failed"
    VIDEO_GENERATION_STARTED = "media.video.generation.started"
    VIDEO_GENERATION_COMPLETED = "media.video.generation.completed"
    VIDEO_GENERATION_FAILED = "media.video.generation.failed"
