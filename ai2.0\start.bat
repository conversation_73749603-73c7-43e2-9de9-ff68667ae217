@echo off
chcp 65001 >nul
title AI视频生成器 2.0

echo ========================================
echo AI视频生成器 2.0
echo ========================================
echo.

:: 检查虚拟环境是否存在
if not exist "venv\Scripts\activate.bat" (
    echo 正在创建虚拟环境...
    python -m venv venv
    if errorlevel 1 (
        echo 错误：无法创建虚拟环境
        echo 请确保已安装Python 3.8或更高版本
        pause
        exit /b 1
    )
)

:: 激活虚拟环境
echo 正在激活虚拟环境...
call venv\Scripts\activate.bat

:: 检查依赖是否安装
echo 正在检查依赖...
python -c "import PyQt6" 2>nul
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误：依赖安装失败
        pause
        exit /b 1
    )
)

:: 启动程序
echo 正在启动AI视频生成器 2.0...
echo.
python run.py

:: 程序结束后暂停
if errorlevel 1 (
    echo.
    echo 程序异常退出
    pause
) else (
    echo.
    echo 程序正常退出
)
