"""项目管理控制器

实现项目管理的核心业务逻辑。
"""

from typing import Dict, Any, List, Optional
from uuid import UUID
from datetime import datetime

from src.models import (
    Project, ProjectStatus, CreateProjectSchema, UpdateProjectSchema,
    ProjectSchema, ProjectSummarySchema
)
from src.repositories import ProjectRepository
from src.core.events import get_event_bus, Event, ProjectEvents
from src.core.exceptions import BusinessLogicError, ValidationError, ProjectNotFoundError
from src.utils.logger import get_logger


class ProjectController:
    """项目管理控制器"""
    
    def __init__(self, project_repo: ProjectRepository):
        self.project_repo = project_repo
        self.event_bus = get_event_bus()
        self.logger = get_logger(__name__)
    
    async def create_project(self, project_data: CreateProjectSchema) -> ProjectSchema:
        """创建项目"""
        try:
            # 验证项目数据
            await self._validate_project_data(project_data.dict())
            
            # 创建项目实体
            project = Project(**project_data.dict())
            project.status = ProjectStatus.DRAFT
            
            # 保存到数据库
            created_project = await self.project_repo.create(project)
            
            # 发布项目创建事件
            await self.event_bus.publish(Event(
                name=ProjectEvents.CREATED,
                data={
                    'project_id': str(created_project.id),
                    'title': created_project.title,
                    'style': created_project.style
                }
            ))
            
            self.logger.info(f"Project created: {created_project.id}")
            return ProjectSchema.from_orm(created_project)
            
        except Exception as e:
            self.logger.error(f"Failed to create project: {e}")
            raise BusinessLogicError(f"Failed to create project: {str(e)}", cause=e)
    
    async def get_project(self, project_id: UUID) -> ProjectSchema:
        """获取项目"""
        try:
            project = await self.project_repo.get_by_id(project_id)
            if not project:
                raise ProjectNotFoundError(f"Project not found: {project_id}", project_id=str(project_id))
            
            return ProjectSchema.from_orm(project)
            
        except ProjectNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"Failed to get project {project_id}: {e}")
            raise BusinessLogicError(f"Failed to get project: {str(e)}", cause=e)
    
    async def get_project_with_storyboards(self, project_id: UUID) -> Dict[str, Any]:
        """获取项目及其分镜"""
        try:
            project = await self.project_repo.get_by_id_with_storyboards(project_id)
            if not project:
                raise ProjectNotFoundError(f"Project not found: {project_id}", project_id=str(project_id))
            
            # 转换为字典格式
            project_data = ProjectSchema.from_orm(project).dict()
            project_data['storyboards'] = [
                {
                    'id': str(sb.id),
                    'name': sb.name,
                    'status': sb.status.value,
                    'shot_count': sb.shot_count,
                    'total_duration': sb.total_duration,
                    'created_at': sb.created_at.isoformat(),
                    'updated_at': sb.updated_at.isoformat()
                }
                for sb in project.get_active_storyboards()
            ]
            
            return project_data
            
        except ProjectNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"Failed to get project with storyboards {project_id}: {e}")
            raise BusinessLogicError(f"Failed to get project with storyboards: {str(e)}", cause=e)
    
    async def update_project(self, project_id: UUID, update_data: UpdateProjectSchema) -> ProjectSchema:
        """更新项目"""
        try:
            # 检查项目是否存在
            existing_project = await self.project_repo.get_by_id(project_id)
            if not existing_project:
                raise ProjectNotFoundError(f"Project not found: {project_id}", project_id=str(project_id))
            
            # 验证更新数据
            update_dict = update_data.dict(exclude_unset=True)
            await self._validate_project_data(update_dict, is_update=True)
            
            # 更新项目
            updated_project = await self.project_repo.update_by_id(project_id, update_dict)
            
            # 发布项目更新事件
            await self.event_bus.publish(Event(
                name=ProjectEvents.SAVED,
                data={
                    'project_id': str(project_id),
                    'updated_fields': list(update_dict.keys())
                }
            ))
            
            self.logger.info(f"Project updated: {project_id}")
            return ProjectSchema.from_orm(updated_project)
            
        except (ProjectNotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Failed to update project {project_id}: {e}")
            raise BusinessLogicError(f"Failed to update project: {str(e)}", cause=e)
    
    async def delete_project(self, project_id: UUID, soft_delete: bool = True) -> bool:
        """删除项目"""
        try:
            # 检查项目是否存在
            project = await self.project_repo.get_by_id(project_id)
            if not project:
                raise ProjectNotFoundError(f"Project not found: {project_id}", project_id=str(project_id))
            
            # 删除项目
            success = await self.project_repo.delete(project_id, soft_delete)
            
            if success:
                # 发布项目删除事件
                await self.event_bus.publish(Event(
                    name=ProjectEvents.DELETED,
                    data={
                        'project_id': str(project_id),
                        'title': project.title,
                        'soft_delete': soft_delete
                    }
                ))
                
                self.logger.info(f"Project {'soft ' if soft_delete else ''}deleted: {project_id}")
            
            return success
            
        except ProjectNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"Failed to delete project {project_id}: {e}")
            raise BusinessLogicError(f"Failed to delete project: {str(e)}", cause=e)
    
    async def list_projects(
        self,
        skip: int = 0,
        limit: int = 100,
        status: Optional[ProjectStatus] = None,
        style: Optional[str] = None,
        language: Optional[str] = None,
        title_search: Optional[str] = None
    ) -> List[ProjectSummarySchema]:
        """列出项目"""
        try:
            if title_search:
                projects = await self.project_repo.search_by_title(title_search, skip, limit)
            elif any([status, style, language]):
                projects = await self.project_repo.get_projects_with_filters(
                    status=status,
                    style=style,
                    language=language,
                    skip=skip,
                    limit=limit
                )
            else:
                projects = await self.project_repo.get_all(skip=skip, limit=limit)
            
            return [ProjectSummarySchema.from_orm(project) for project in projects]
            
        except Exception as e:
            self.logger.error(f"Failed to list projects: {e}")
            raise BusinessLogicError(f"Failed to list projects: {str(e)}", cause=e)
    
    async def get_recent_projects(self, limit: int = 10) -> List[ProjectSummarySchema]:
        """获取最近的项目"""
        try:
            projects = await self.project_repo.get_recent_projects(limit)
            return [ProjectSummarySchema.from_orm(project) for project in projects]
            
        except Exception as e:
            self.logger.error(f"Failed to get recent projects: {e}")
            raise BusinessLogicError(f"Failed to get recent projects: {str(e)}", cause=e)
    
    async def get_project_statistics(self) -> Dict[str, Any]:
        """获取项目统计信息"""
        try:
            stats = await self.project_repo.get_statistics()
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get project statistics: {e}")
            raise BusinessLogicError(f"Failed to get project statistics: {str(e)}", cause=e)
    
    async def archive_project(self, project_id: UUID) -> ProjectSchema:
        """归档项目"""
        try:
            project = await self.project_repo.archive_project(project_id)
            if not project:
                raise ProjectNotFoundError(f"Project not found: {project_id}", project_id=str(project_id))
            
            self.logger.info(f"Project archived: {project_id}")
            return ProjectSchema.from_orm(project)
            
        except ProjectNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"Failed to archive project {project_id}: {e}")
            raise BusinessLogicError(f"Failed to archive project: {str(e)}", cause=e)
    
    async def unarchive_project(self, project_id: UUID) -> ProjectSchema:
        """取消归档项目"""
        try:
            project = await self.project_repo.unarchive_project(project_id)
            if not project:
                raise ProjectNotFoundError(f"Project not found: {project_id}", project_id=str(project_id))
            
            self.logger.info(f"Project unarchived: {project_id}")
            return ProjectSchema.from_orm(project)
            
        except ProjectNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"Failed to unarchive project {project_id}: {e}")
            raise BusinessLogicError(f"Failed to unarchive project: {str(e)}", cause=e)
    
    async def duplicate_project(self, project_id: UUID, new_title: str) -> ProjectSchema:
        """复制项目"""
        try:
            project = await self.project_repo.duplicate_project(project_id, new_title)
            if not project:
                raise ProjectNotFoundError(f"Project not found: {project_id}", project_id=str(project_id))
            
            # 发布项目创建事件
            await self.event_bus.publish(Event(
                name=ProjectEvents.CREATED,
                data={
                    'project_id': str(project.id),
                    'title': project.title,
                    'duplicated_from': str(project_id)
                }
            ))
            
            self.logger.info(f"Project duplicated: {project_id} -> {project.id}")
            return ProjectSchema.from_orm(project)
            
        except ProjectNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"Failed to duplicate project {project_id}: {e}")
            raise BusinessLogicError(f"Failed to duplicate project: {str(e)}", cause=e)
    
    async def _validate_project_data(self, data: Dict[str, Any], is_update: bool = False) -> None:
        """验证项目数据"""
        # 标题验证
        if 'title' in data:
            title = data['title']
            if not title or not title.strip():
                raise ValidationError("项目标题不能为空", field="title")
            if len(title) > 255:
                raise ValidationError("项目标题过长", field="title")
        
        # 内容验证
        if 'content' in data and data['content']:
            content = data['content']
            if len(content) > 100000:  # 100KB限制
                raise ValidationError("项目内容过长", field="content")
        
        # 视频尺寸验证
        if 'video_width' in data:
            width = data['video_width']
            if width < 480 or width > 4096:
                raise ValidationError("视频宽度必须在480-4096之间", field="video_width")
        
        if 'video_height' in data:
            height = data['video_height']
            if height < 360 or height > 2160:
                raise ValidationError("视频高度必须在360-2160之间", field="video_height")
        
        # 帧率验证
        if 'video_fps' in data:
            fps = data['video_fps']
            if fps < 15.0 or fps > 60.0:
                raise ValidationError("视频帧率必须在15-60之间", field="video_fps")
        
        # 风格验证
        if 'style' in data:
            from src.models.project import ProjectConstants
            style = data['style']
            if style not in ProjectConstants.SUPPORTED_STYLES:
                raise ValidationError(f"不支持的视觉风格: {style}", field="style")
        
        # 语言验证
        if 'language' in data:
            from src.models.project import ProjectConstants
            language = data['language']
            if language not in ProjectConstants.SUPPORTED_LANGUAGES:
                raise ValidationError(f"不支持的语言: {language}", field="language")
