# AI视频生成器 2.0 Makefile

.PHONY: help install install-dev test test-cov lint format type-check clean build run docs

# 默认目标
help:
	@echo "AI视频生成器 2.0 开发工具"
	@echo ""
	@echo "可用命令:"
	@echo "  install      - 安装生产依赖"
	@echo "  install-dev  - 安装开发依赖"
	@echo "  test         - 运行测试"
	@echo "  test-cov     - 运行测试并生成覆盖率报告"
	@echo "  lint         - 运行代码检查"
	@echo "  format       - 格式化代码"
	@echo "  type-check   - 运行类型检查"
	@echo "  clean        - 清理临时文件"
	@echo "  build        - 构建项目"
	@echo "  run          - 运行应用程序"
	@echo "  docs         - 生成文档"

# 安装依赖
install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements-dev.txt
	pre-commit install

# 测试
test:
	pytest

test-cov:
	pytest --cov=src --cov-report=html --cov-report=term

test-unit:
	pytest -m unit

test-integration:
	pytest -m integration

test-ui:
	pytest -m ui

# 代码质量
lint:
	flake8 src tests
	mypy src

format:
	black src tests
	isort src tests

type-check:
	mypy src

# 清理
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/

# 构建
build:
	python -m build

# 运行
run:
	python -m src.main

run-dev:
	APP_ENV=development python -m src.main

# 文档
docs:
	cd docs && make html

docs-serve:
	cd docs/_build/html && python -m http.server 8000

# 开发工具
dev-setup: install-dev
	@echo "开发环境设置完成"

check-all: format lint type-check test
	@echo "所有检查完成"

# 数据库
db-init:
	python -m src.database.init

db-migrate:
	alembic upgrade head

db-reset:
	rm -f data/*.db
	python -m src.database.init

# 发布
release-patch:
	bump2version patch

release-minor:
	bump2version minor

release-major:
	bump2version major
