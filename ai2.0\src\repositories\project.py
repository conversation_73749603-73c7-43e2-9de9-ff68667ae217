"""项目仓库

实现项目相关的数据访问操作。
"""

from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from sqlalchemy.orm import selectinload

from src.models import Project, ProjectStatus
from src.repositories.base import AsyncRepository
from src.core.exceptions import DataAccessError


class ProjectRepository(AsyncRepository[Project]):
    """项目仓库"""
    
    def __init__(self, session: AsyncSession):
        super().__init__(session, Project)
    
    async def get_by_id_with_storyboards(self, project_id: UUID) -> Optional[Project]:
        """获取项目及其分镜"""
        try:
            query = (
                select(Project)
                .options(selectinload(Project.storyboards))
                .where(and_(
                    Project.id == project_id,
                    Project.is_deleted == False
                ))
            )
            
            result = await self.session.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            raise DataAccessError(f"Failed to get project with storyboards: {str(e)}", cause=e)
    
    async def get_by_status(self, status: ProjectStatus, skip: int = 0, limit: int = 100) -> List[Project]:
        """根据状态获取项目"""
        return await self.find_by(status=status, skip=skip, limit=limit)
    
    async def get_recent_projects(self, limit: int = 10) -> List[Project]:
        """获取最近的项目"""
        try:
            query = (
                select(Project)
                .where(Project.is_deleted == False)
                .order_by(Project.updated_at.desc())
                .limit(limit)
            )
            
            result = await self.session.execute(query)
            return result.scalars().all()
        except Exception as e:
            raise DataAccessError(f"Failed to get recent projects: {str(e)}", cause=e)
    
    async def search_by_title(self, title: str, skip: int = 0, limit: int = 100) -> List[Project]:
        """根据标题搜索项目"""
        try:
            query = (
                select(Project)
                .where(and_(
                    Project.title.ilike(f"%{title}%"),
                    Project.is_deleted == False
                ))
                .order_by(Project.updated_at.desc())
                .offset(skip)
                .limit(limit)
            )
            
            result = await self.session.execute(query)
            return result.scalars().all()
        except Exception as e:
            raise DataAccessError(f"Failed to search projects by title: {str(e)}", cause=e)
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取项目统计信息"""
        try:
            # 总项目数
            total_query = select(func.count(Project.id)).where(Project.is_deleted == False)
            total_result = await self.session.execute(total_query)
            total_projects = total_result.scalar()
            
            # 按状态统计
            status_stats = {}
            for status in ProjectStatus:
                status_query = select(func.count(Project.id)).where(and_(
                    Project.status == status,
                    Project.is_deleted == False
                ))
                status_result = await self.session.execute(status_query)
                status_stats[status.value] = status_result.scalar()
            
            # 今日创建的项目数
            today = datetime.utcnow().date()
            today_query = select(func.count(Project.id)).where(and_(
                func.date(Project.created_at) == today,
                Project.is_deleted == False
            ))
            today_result = await self.session.execute(today_query)
            today_projects = today_result.scalar()
            
            # 总时长统计
            duration_query = select(func.sum(Project.total_duration)).where(Project.is_deleted == False)
            duration_result = await self.session.execute(duration_query)
            total_duration = duration_result.scalar() or 0.0
            
            return {
                'total_projects': total_projects,
                'status_distribution': status_stats,
                'today_created': today_projects,
                'total_duration': total_duration
            }
        except Exception as e:
            raise DataAccessError(f"Failed to get project statistics: {str(e)}", cause=e)
    
    async def update_statistics(self, project_id: UUID) -> Optional[Project]:
        """更新项目统计信息"""
        try:
            project = await self.get_by_id_with_storyboards(project_id)
            if not project:
                return None
            
            # 重新计算统计信息
            project.update_statistics()
            
            # 保存更新
            await self.session.commit()
            await self.session.refresh(project)
            
            return project
        except Exception as e:
            await self.session.rollback()
            raise DataAccessError(f"Failed to update project statistics: {str(e)}", cause=e)
    
    async def get_projects_by_style(self, style: str, skip: int = 0, limit: int = 100) -> List[Project]:
        """根据风格获取项目"""
        return await self.find_by(style=style, skip=skip, limit=limit)
    
    async def get_projects_by_language(self, language: str, skip: int = 0, limit: int = 100) -> List[Project]:
        """根据语言获取项目"""
        return await self.find_by(language=language, skip=skip, limit=limit)
    
    async def get_completed_projects(self, skip: int = 0, limit: int = 100) -> List[Project]:
        """获取已完成的项目"""
        return await self.get_by_status(ProjectStatus.COMPLETED, skip, limit)
    
    async def get_in_progress_projects(self, skip: int = 0, limit: int = 100) -> List[Project]:
        """获取进行中的项目"""
        return await self.get_by_status(ProjectStatus.IN_PROGRESS, skip, limit)
    
    async def archive_project(self, project_id: UUID) -> Optional[Project]:
        """归档项目"""
        try:
            data = {
                'status': ProjectStatus.ARCHIVED,
                'updated_at': datetime.utcnow()
            }
            return await self.update_by_id(project_id, data)
        except Exception as e:
            raise DataAccessError(f"Failed to archive project: {str(e)}", cause=e)
    
    async def unarchive_project(self, project_id: UUID) -> Optional[Project]:
        """取消归档项目"""
        try:
            data = {
                'status': ProjectStatus.DRAFT,
                'updated_at': datetime.utcnow()
            }
            return await self.update_by_id(project_id, data)
        except Exception as e:
            raise DataAccessError(f"Failed to unarchive project: {str(e)}", cause=e)
    
    async def duplicate_project(self, project_id: UUID, new_title: str) -> Optional[Project]:
        """复制项目"""
        try:
            original = await self.get_by_id(project_id)
            if not original:
                return None
            
            # 创建新项目
            new_project = Project(
                name=f"Copy of {original.name}",
                title=new_title,
                content=original.content,
                description=original.description,
                style=original.style,
                language=original.language,
                video_width=original.video_width,
                video_height=original.video_height,
                video_fps=original.video_fps,
                generation_settings=original.generation_settings,
                status=ProjectStatus.DRAFT
            )
            
            return await self.create(new_project)
        except Exception as e:
            raise DataAccessError(f"Failed to duplicate project: {str(e)}", cause=e)
    
    async def get_projects_with_filters(
        self,
        status: Optional[ProjectStatus] = None,
        style: Optional[str] = None,
        language: Optional[str] = None,
        title_search: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Project]:
        """根据多个条件过滤项目"""
        try:
            query = select(Project).where(Project.is_deleted == False)
            
            if status:
                query = query.where(Project.status == status)
            
            if style:
                query = query.where(Project.style == style)
            
            if language:
                query = query.where(Project.language == language)
            
            if title_search:
                query = query.where(Project.title.ilike(f"%{title_search}%"))
            
            query = query.order_by(Project.updated_at.desc()).offset(skip).limit(limit)
            
            result = await self.session.execute(query)
            return result.scalars().all()
        except Exception as e:
            raise DataAccessError(f"Failed to get projects with filters: {str(e)}", cause=e)
