"""工作流集成测试

测试完整的业务流程和组件集成。
"""

import pytest
from unittest.mock import AsyncMock, patch
import uuid
from datetime import datetime
from pathlib import Path

from src.business.storyboard.generator import StoryboardGenerator
from src.business.media.processor import MediaProcessor
from src.business.consistency.manager import ConsistencyManager
from src.repositories.project import ProjectRepository
from src.repositories.storyboard import StoryboardRepository
from src.repositories.shot import ShotRepository
from src.models.project import Project
from src.models.storyboard import Storyboard
from src.models.shot import Shot
from src.services.base import ServiceResponse
from src.services.registry import ServiceRegistry, get_service_registry
from src.services.config_manager import ServiceConfigManager
from src.core.config import ConfigManager


class TestProjectWorkflow:
    """测试项目工作流"""
    
    @pytest.fixture
    async def project_repo(self, async_db_session):
        """项目仓库"""
        return ProjectRepository(async_db_session)
    
    @pytest.fixture
    async def storyboard_repo(self, async_db_session):
        """分镜仓库"""
        return StoryboardRepository(async_db_session)
    
    @pytest.fixture
    async def shot_repo(self, async_db_session):
        """镜头仓库"""
        return ShotRepository(async_db_session)
    
    @pytest.fixture
    def mock_services(self):
        """模拟服务"""
        services = {
            "llm": AsyncMock(),
            "image": AsyncMock(),
            "voice": AsyncMock(),
            "video": AsyncMock()
        }
        
        # 配置LLM服务返回
        services["llm"].chat_completion.return_value = ServiceResponse.success_response(
            """
            {
                "shots": [
                    {
                        "sequence_number": 1,
                        "shot_type": "wide_shot",
                        "content": "森林全景，小女孩从远处走来",
                        "duration": 3.0,
                        "image_prompt": "Wide shot of forest with girl walking from distance, animation style",
                        "video_prompt": "Girl walking through forest",
                        "voice_prompt": "小女孩走在森林小径上"
                    },
                    {
                        "sequence_number": 2,
                        "shot_type": "medium_shot",
                        "content": "小女孩的中景镜头，她好奇地四处张望",
                        "duration": 2.5,
                        "image_prompt": "Medium shot of young girl in forest, looking around curiously",
                        "video_prompt": "Girl looking around curiously",
                        "voice_prompt": "她好奇地四处张望"
                    }
                ]
            }
            """
        )
        
        # 配置图像服务返回
        services["image"].generate_image.return_value = ServiceResponse.success_response(
            "/generated/images/test_image.png"
        )
        
        # 配置语音服务返回
        services["voice"].text_to_speech.return_value = ServiceResponse.success_response(
            "/generated/voice/test_voice.mp3"
        )
        
        # 配置视频服务返回
        services["video"].text_to_video.return_value = ServiceResponse.success_response(
            "/generated/videos/test_video.mp4"
        )
        
        return services
    
    @pytest.fixture
    def sample_project_data(self):
        """示例项目数据"""
        return {
            "title": "森林小女孩的故事",
            "description": "一个关于小女孩在森林中冒险的温馨故事",
            "content": "从前有一个小女孩，她住在森林边缘的小屋里。有一天，她决定深入森林探险，寻找传说中的魔法花朵。",
            "style": "动画",
            "language": "zh-CN",
            "video_width": 1920,
            "video_height": 1080,
            "video_fps": 24.0
        }
    
    @pytest.mark.asyncio
    async def test_complete_project_workflow(
        self, 
        project_repo, 
        storyboard_repo, 
        shot_repo,
        mock_services,
        sample_project_data
    ):
        """测试完整的项目工作流"""
        
        # 1. 创建项目
        project = Project(**sample_project_data)
        saved_project = await project_repo.create(project)
        assert saved_project.id is not None
        
        # 2. 生成分镜
        storyboard_generator = StoryboardGenerator(
            llm_service=mock_services["llm"],
            project_repository=project_repo
        )
        
        storyboard_result = await storyboard_generator.generate_from_text(
            text=saved_project.content,
            style=saved_project.style,
            project_id=saved_project.id
        )
        
        assert storyboard_result.success is True
        storyboard = storyboard_result.data
        assert isinstance(storyboard, Storyboard)
        assert len(storyboard.shots) == 2
        
        # 3. 保存分镜
        saved_storyboard = await storyboard_repo.create(storyboard)
        assert saved_storyboard.id is not None
        
        # 4. 保存镜头
        for shot in storyboard.shots:
            shot.storyboard_id = saved_storyboard.id
            saved_shot = await shot_repo.create(shot)
            assert saved_shot.id is not None
        
        # 5. 处理媒体
        media_processor = MediaProcessor(
            image_service=mock_services["image"],
            voice_service=mock_services["voice"],
            video_service=mock_services["video"]
        )
        
        # 处理图像
        image_result = await media_processor.process_shot_images(storyboard.shots)
        assert image_result.success is True
        
        # 处理语音
        voice_result = await media_processor.process_shot_voices(storyboard.shots)
        assert voice_result.success is True
        
        # 处理视频
        video_result = await media_processor.process_shot_videos(storyboard.shots)
        assert video_result.success is True
        
        # 6. 验证最终结果
        processed_shots = video_result.data
        for shot in processed_shots:
            assert shot.image_path is not None
            assert shot.voice_path is not None
            assert shot.video_path is not None
        
        # 7. 验证数据库中的数据
        retrieved_project = await project_repo.get_by_id(saved_project.id)
        assert retrieved_project is not None
        assert retrieved_project.title == sample_project_data["title"]
        
        retrieved_storyboard = await storyboard_repo.get_by_id(saved_storyboard.id)
        assert retrieved_storyboard is not None
        assert retrieved_storyboard.project_id == saved_project.id
    
    @pytest.mark.asyncio
    async def test_storyboard_consistency_check(
        self,
        mock_services,
        sample_project_data
    ):
        """测试分镜一致性检查"""
        
        # 创建测试分镜和镜头
        storyboard = Storyboard(
            id=uuid.uuid4(),
            project_id=uuid.uuid4(),
            name="测试分镜",
            content="测试内容",
            generation_settings={"style": "动画"}
        )
        
        shots = [
            Shot(
                id=uuid.uuid4(),
                storyboard_id=storyboard.id,
                name="镜头1",
                sequence_number=1,
                shot_type="wide_shot",
                content="小女孩在森林中行走",
                duration=3.0,
                image_prompt="Girl walking in forest",
                video_prompt="Walking scene",
                voice_prompt="小女孩走路"
            ),
            Shot(
                id=uuid.uuid4(),
                storyboard_id=storyboard.id,
                name="镜头2",
                sequence_number=2,
                shot_type="medium_shot",
                content="小女孩停下来观察花朵",
                duration=2.5,
                image_prompt="Girl looking at flowers",
                video_prompt="Observing flowers",
                voice_prompt="小女孩看花"
            )
        ]
        
        storyboard.shots = shots
        
        # 配置一致性检查的LLM返回
        mock_services["llm"].chat_completion.return_value = ServiceResponse.success_response(
            """
            {
                "consistency_score": 0.92,
                "character_appearances": {
                    "小女孩": {
                        "consistent": true,
                        "variations": []
                    }
                },
                "environment_elements": {
                    "森林": {
                        "consistent": true,
                        "description": "茂密的绿色森林"
                    }
                },
                "issues": [],
                "suggestions": [
                    "保持角色服装在所有镜头中的一致性",
                    "确保光照条件的连贯性"
                ]
            }
            """
        )
        
        # 执行一致性检查
        consistency_manager = ConsistencyManager(llm_service=mock_services["llm"])
        
        character_result = await consistency_manager.check_character_consistency(shots)
        assert character_result.success is True
        assert character_result.data["consistency_score"] == 0.92
        
        environment_result = await consistency_manager.check_environment_consistency(shots)
        assert environment_result.success is True
        assert "森林" in environment_result.data["environment_elements"]
    
    @pytest.mark.asyncio
    async def test_error_handling_in_workflow(
        self,
        project_repo,
        mock_services,
        sample_project_data
    ):
        """测试工作流中的错误处理"""
        
        # 创建项目
        project = Project(**sample_project_data)
        saved_project = await project_repo.create(project)
        
        # 模拟LLM服务错误
        mock_services["llm"].chat_completion.return_value = ServiceResponse.error_response(
            "LLM service temporarily unavailable"
        )
        
        # 尝试生成分镜
        storyboard_generator = StoryboardGenerator(
            llm_service=mock_services["llm"],
            project_repository=project_repo
        )
        
        result = await storyboard_generator.generate_from_text(
            text=saved_project.content,
            style=saved_project.style,
            project_id=saved_project.id
        )
        
        # 验证错误处理
        assert result.success is False
        assert "LLM service temporarily unavailable" in result.error_message
    
    @pytest.mark.asyncio
    async def test_partial_media_processing_failure(
        self,
        mock_services
    ):
        """测试部分媒体处理失败的情况"""
        
        # 创建测试镜头
        shot = Shot(
            id=uuid.uuid4(),
            storyboard_id=uuid.uuid4(),
            name="测试镜头",
            sequence_number=1,
            shot_type="medium_shot",
            content="测试内容",
            duration=3.0,
            image_prompt="Test image prompt",
            video_prompt="Test video prompt",
            voice_prompt="测试语音提示"
        )
        
        # 模拟图像生成成功，但语音生成失败
        mock_services["image"].generate_image.return_value = ServiceResponse.success_response(
            "/generated/images/test.png"
        )
        mock_services["voice"].text_to_speech.return_value = ServiceResponse.error_response(
            "Voice service unavailable"
        )
        
        media_processor = MediaProcessor(
            image_service=mock_services["image"],
            voice_service=mock_services["voice"],
            video_service=mock_services["video"]
        )
        
        # 处理图像（应该成功）
        image_result = await media_processor.process_shot_images([shot])
        assert image_result.success is True
        
        # 处理语音（应该失败）
        voice_result = await media_processor.process_shot_voices([shot])
        assert voice_result.success is False
        assert "Voice service unavailable" in voice_result.error_message


class TestServiceIntegration:
    """测试服务集成"""
    
    @pytest.fixture
    def config_manager(self, test_config):
        """配置管理器"""
        return test_config
    
    @pytest.fixture
    def service_config_manager(self, config_manager):
        """服务配置管理器"""
        return ServiceConfigManager(config_manager)
    
    def test_service_registry_initialization(self):
        """测试服务注册表初始化"""
        registry = get_service_registry()
        
        # 验证默认服务已注册
        llm_services = registry.get_available_services(ServiceType.LLM)
        assert len(llm_services) >= 2  # OpenAI和智谱
        
        image_services = registry.get_available_services(ServiceType.IMAGE)
        assert len(image_services) >= 2  # Pollinations和OpenAI
        
        voice_services = registry.get_available_services(ServiceType.VOICE)
        assert len(voice_services) >= 2  # Edge TTS和OpenAI TTS
        
        video_services = registry.get_available_services(ServiceType.VIDEO)
        assert len(video_services) >= 2  # CogVideoX和本地合成器
    
    def test_service_configuration_validation(self, service_config_manager):
        """测试服务配置验证"""
        # 测试有效配置
        valid_config = {
            "api_key": "test_key_123",
            "base_url": "https://api.test.com",
            "timeout": 60
        }
        
        errors = service_config_manager.validate_service_config("openai_llm", valid_config)
        assert len(errors) == 0
        
        # 测试无效配置（缺少必需字段）
        invalid_config = {
            "base_url": "https://api.test.com"
            # 缺少api_key
        }
        
        errors = service_config_manager.validate_service_config("openai_llm", invalid_config)
        assert len(errors) > 0
        assert any("api_key" in error for error in errors)
    
    @pytest.mark.asyncio
    async def test_service_health_monitoring(self, service_config_manager):
        """测试服务健康监控"""
        registry = get_service_registry()
        
        # 创建模拟服务
        config = {
            "api_key": "test_key",
            "base_url": "https://api.test.com"
        }
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            # 模拟健康检查成功
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = {"status": "ok"}
            mock_get.return_value.__aenter__.return_value = mock_response
            
            service = await registry.create_service("openai_llm", config)
            assert service is not None
            
            # 执行健康检查
            is_healthy = await service.health_check()
            assert is_healthy is True
    
    def test_service_status_summary(self, service_config_manager):
        """测试服务状态摘要"""
        summary = service_config_manager.get_available_services_summary()
        
        assert "service_types" in summary
        assert "total_services" in summary
        assert "enabled_services" in summary
        assert "configured_services" in summary
        
        # 验证各服务类型
        for service_type in ["llm", "image", "voice", "video"]:
            assert service_type in summary["service_types"]
            type_summary = summary["service_types"][service_type]
            assert "available_services" in type_summary
            assert "enabled_count" in type_summary
