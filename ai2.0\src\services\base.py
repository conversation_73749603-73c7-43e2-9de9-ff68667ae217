"""基础服务接口

定义服务层的基础接口和抽象类。
"""

from typing import Dict, Any, Optional, List, TypeVar, Generic
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
import asyncio
from datetime import datetime

from src.core.exceptions import ServiceError, NetworkError, RateLimitExceededError
from src.utils.logger import get_logger

T = TypeVar('T')


class ServiceStatus(Enum):
    """服务状态"""
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    RATE_LIMITED = "rate_limited"
    ERROR = "error"


@dataclass
class ServiceResponse(Generic[T]):
    """服务响应"""
    success: bool
    data: Optional[T] = None
    error_message: Optional[str] = None
    error_code: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    @classmethod
    def success_response(cls, data: T, metadata: Optional[Dict[str, Any]] = None) -> 'ServiceResponse[T]':
        """创建成功响应"""
        return cls(success=True, data=data, metadata=metadata)
    
    @classmethod
    def error_response(
        cls, 
        error_message: str, 
        error_code: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> 'ServiceResponse[T]':
        """创建错误响应"""
        return cls(
            success=False, 
            error_message=error_message, 
            error_code=error_code,
            metadata=metadata
        )


@dataclass
class ServiceConfig:
    """服务配置"""
    provider: str
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    rate_limit: Optional[int] = None
    custom_settings: Optional[Dict[str, Any]] = None


class BaseService(ABC):
    """基础服务抽象类"""
    
    def __init__(self, config: ServiceConfig):
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self._status = ServiceStatus.AVAILABLE
        self._last_error: Optional[str] = None
        self._rate_limit_reset: Optional[datetime] = None
    
    @property
    def status(self) -> ServiceStatus:
        """获取服务状态"""
        return self._status
    
    @property
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return self._status == ServiceStatus.AVAILABLE
    
    @property
    def last_error(self) -> Optional[str]:
        """获取最后的错误信息"""
        return self._last_error
    
    def _set_status(self, status: ServiceStatus, error_message: Optional[str] = None) -> None:
        """设置服务状态"""
        self._status = status
        self._last_error = error_message
        
        if status != ServiceStatus.AVAILABLE:
            self.logger.warning(f"Service status changed to {status.value}: {error_message}")
    
    def _handle_rate_limit(self, retry_after: Optional[int] = None) -> None:
        """处理速率限制"""
        self._status = ServiceStatus.RATE_LIMITED
        if retry_after:
            self._rate_limit_reset = datetime.utcnow().replace(second=retry_after)
        
        self.logger.warning(f"Rate limit exceeded, retry after: {retry_after}")
    
    async def _retry_with_backoff(self, func, *args, **kwargs):
        """带退避的重试机制"""
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                return await func(*args, **kwargs)
            except RateLimitExceededError as e:
                self._handle_rate_limit(getattr(e, 'retry_after', None))
                raise
            except (NetworkError, ServiceError) as e:
                last_exception = e
                if attempt < self.config.max_retries:
                    delay = self.config.retry_delay * (2 ** attempt)  # 指数退避
                    self.logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay}s: {e}")
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(f"All {self.config.max_retries + 1} attempts failed")
                    self._set_status(ServiceStatus.ERROR, str(e))
        
        raise last_exception
    
    @abstractmethod
    async def health_check(self) -> bool:
        """健康检查"""
        pass
    
    @abstractmethod
    async def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        pass


class AIService(BaseService):
    """AI服务基类"""
    
    @abstractmethod
    async def generate(self, prompt: str, **kwargs) -> ServiceResponse[Any]:
        """生成内容"""
        pass
    
    @abstractmethod
    async def validate_prompt(self, prompt: str) -> bool:
        """验证提示词"""
        pass
    
    def _prepare_prompt(self, prompt: str, **kwargs) -> str:
        """准备提示词"""
        # 基础实现，子类可以重写
        return prompt.strip()
    
    def _validate_response(self, response: Any) -> bool:
        """验证响应"""
        # 基础实现，子类可以重写
        return response is not None


class LLMService(AIService):
    """大语言模型服务接口"""
    
    @abstractmethod
    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> ServiceResponse[str]:
        """聊天完成"""
        pass
    
    @abstractmethod
    async def text_completion(
        self, 
        prompt: str, 
        **kwargs
    ) -> ServiceResponse[str]:
        """文本完成"""
        pass
    
    @abstractmethod
    async def analyze_text(
        self, 
        text: str, 
        analysis_type: str = "sentiment"
    ) -> ServiceResponse[Dict[str, Any]]:
        """文本分析"""
        pass


class ImageGenerationService(AIService):
    """图像生成服务接口"""
    
    @abstractmethod
    async def generate_image(
        self, 
        prompt: str, 
        width: int = 1024, 
        height: int = 1024,
        **kwargs
    ) -> ServiceResponse[str]:
        """生成图像，返回图像URL或路径"""
        pass
    
    @abstractmethod
    async def edit_image(
        self, 
        image_path: str, 
        prompt: str, 
        **kwargs
    ) -> ServiceResponse[str]:
        """编辑图像"""
        pass
    
    @abstractmethod
    async def upscale_image(
        self, 
        image_path: str, 
        scale_factor: int = 2
    ) -> ServiceResponse[str]:
        """图像放大"""
        pass


class VideoGenerationService(AIService):
    """视频生成服务接口"""
    
    @abstractmethod
    async def generate_video(
        self, 
        prompt: str, 
        duration: float = 3.0,
        width: int = 1920,
        height: int = 1080,
        fps: int = 30,
        **kwargs
    ) -> ServiceResponse[str]:
        """生成视频，返回视频URL或路径"""
        pass
    
    @abstractmethod
    async def image_to_video(
        self, 
        image_path: str, 
        prompt: str,
        duration: float = 3.0,
        **kwargs
    ) -> ServiceResponse[str]:
        """图像转视频"""
        pass
    
    @abstractmethod
    async def extend_video(
        self, 
        video_path: str, 
        duration: float = 3.0,
        **kwargs
    ) -> ServiceResponse[str]:
        """延长视频"""
        pass


class VoiceGenerationService(AIService):
    """语音生成服务接口"""
    
    @abstractmethod
    async def text_to_speech(
        self, 
        text: str, 
        voice: str = "default",
        language: str = "zh-CN",
        **kwargs
    ) -> ServiceResponse[str]:
        """文本转语音，返回音频URL或路径"""
        pass
    
    @abstractmethod
    async def get_available_voices(self, language: str = "zh-CN") -> ServiceResponse[List[Dict[str, str]]]:
        """获取可用的语音列表"""
        pass
    
    @abstractmethod
    async def clone_voice(
        self, 
        sample_audio_path: str, 
        text: str,
        **kwargs
    ) -> ServiceResponse[str]:
        """语音克隆"""
        pass


class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self._services: Dict[str, BaseService] = {}
        self.logger = get_logger(self.__class__.__name__)
    
    def register_service(self, name: str, service: BaseService) -> None:
        """注册服务"""
        self._services[name] = service
        self.logger.info(f"Registered service: {name}")
    
    def get_service(self, name: str) -> Optional[BaseService]:
        """获取服务"""
        return self._services.get(name)
    
    def get_available_services(self) -> List[str]:
        """获取可用的服务列表"""
        return [
            name for name, service in self._services.items() 
            if service.is_available
        ]
    
    async def health_check_all(self) -> Dict[str, bool]:
        """检查所有服务的健康状态"""
        results = {}
        for name, service in self._services.items():
            try:
                results[name] = await service.health_check()
            except Exception as e:
                self.logger.error(f"Health check failed for {name}: {e}")
                results[name] = False
        return results
    
    async def get_service_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有服务的状态"""
        status = {}
        for name, service in self._services.items():
            status[name] = {
                'status': service.status.value,
                'available': service.is_available,
                'last_error': service.last_error,
                'provider': service.config.provider
            }
        return status
