"""数据库管理器

管理数据库连接、初始化和迁移。
"""

import asyncio
from typing import Optional, AsyncGenerator
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine, text
from pathlib import Path

from src.models.base import Base
from src.utils.logger import get_logger


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.engine = None
        self.async_engine = None
        self.session_factory = None
        self.async_session_factory = None
        self.logger = get_logger(__name__)
        
        # 处理SQLite数据库URL
        if database_url.startswith("sqlite:///"):
            # 确保数据库目录存在
            db_path = Path(database_url.replace("sqlite:///", ""))
            if not db_path.is_absolute():
                db_path = Path.cwd() / db_path
            db_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 转换为异步SQLite URL
            self.async_database_url = database_url.replace("sqlite:///", "sqlite+aiosqlite:///")
        else:
            self.async_database_url = database_url
    
    async def initialize(self) -> None:
        """初始化数据库"""
        try:
            # 创建异步引擎
            self.async_engine = create_async_engine(
                self.async_database_url,
                echo=False,
                future=True
            )
            
            # 创建同步引擎（用于某些操作）
            self.engine = create_engine(
                self.database_url,
                echo=False,
                future=True
            )
            
            # 创建会话工厂
            self.async_session_factory = async_sessionmaker(
                self.async_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            self.session_factory = sessionmaker(
                self.engine,
                expire_on_commit=False
            )
            
            # 创建表
            await self._create_tables()
            
            self.logger.info("Database initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize database: {e}")
            raise
    
    async def _create_tables(self) -> None:
        """创建数据库表"""
        try:
            async with self.async_engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            self.logger.info("Database tables created successfully")
        except Exception as e:
            self.logger.error(f"Failed to create tables: {e}")
            raise
    
    async def get_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取异步数据库会话"""
        if not self.async_session_factory:
            raise RuntimeError("Database not initialized")
        
        async with self.async_session_factory() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    def get_session(self):
        """获取同步数据库会话"""
        if not self.session_factory:
            raise RuntimeError("Database not initialized")
        
        return self.session_factory()
    
    async def health_check(self) -> bool:
        """数据库健康检查"""
        try:
            async with self.get_async_session() as session:
                result = await session.execute(text("SELECT 1"))
                return result.scalar() == 1
        except Exception as e:
            self.logger.error(f"Database health check failed: {e}")
            return False
    
    async def close(self) -> None:
        """关闭数据库连接"""
        try:
            if self.async_engine:
                await self.async_engine.dispose()
            
            if self.engine:
                self.engine.dispose()
            
            self.logger.info("Database connections closed")
            
        except Exception as e:
            self.logger.error(f"Error closing database: {e}")
    
    async def backup_database(self, backup_path: str) -> bool:
        """备份数据库"""
        try:
            if self.database_url.startswith("sqlite:///"):
                # SQLite数据库备份
                import shutil
                source_path = self.database_url.replace("sqlite:///", "")
                shutil.copy2(source_path, backup_path)
                self.logger.info(f"Database backed up to {backup_path}")
                return True
            else:
                self.logger.warning("Database backup not implemented for this database type")
                return False
        except Exception as e:
            self.logger.error(f"Database backup failed: {e}")
            return False
    
    async def get_database_info(self) -> dict:
        """获取数据库信息"""
        try:
            info = {
                "url": self.database_url,
                "engine_type": str(type(self.async_engine).__name__) if self.async_engine else None,
                "is_connected": await self.health_check()
            }
            
            if self.database_url.startswith("sqlite:///"):
                db_path = Path(self.database_url.replace("sqlite:///", ""))
                if db_path.exists():
                    info["file_size"] = db_path.stat().st_size
                    info["file_path"] = str(db_path.absolute())
            
            return info
            
        except Exception as e:
            self.logger.error(f"Failed to get database info: {e}")
            return {"error": str(e)}


# 全局数据库管理器实例
_db_manager: Optional[DatabaseManager] = None


def get_database_manager() -> Optional[DatabaseManager]:
    """获取全局数据库管理器实例"""
    return _db_manager


def set_database_manager(manager: DatabaseManager) -> None:
    """设置全局数据库管理器实例"""
    global _db_manager
    _db_manager = manager


async def create_database_manager(database_url: str) -> DatabaseManager:
    """创建并初始化数据库管理器"""
    manager = DatabaseManager(database_url)
    await manager.initialize()
    set_database_manager(manager)
    return manager


# 便捷函数
async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """获取异步数据库会话的便捷函数"""
    manager = get_database_manager()
    if not manager:
        raise RuntimeError("Database manager not initialized")
    
    async with manager.get_async_session() as session:
        yield session


def get_session():
    """获取同步数据库会话的便捷函数"""
    manager = get_database_manager()
    if not manager:
        raise RuntimeError("Database manager not initialized")
    
    return manager.get_session()
