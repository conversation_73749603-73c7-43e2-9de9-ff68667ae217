"""
工作流程引擎 - 协调整个视频生成流程
"""
import asyncio
import logging
import time
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from pathlib import Path

from ..services.llm_service import llm_service, StoryboardResult
from ..services.image_service import image_service, ImageResult
from ..services.voice_service import voice_service, VoiceResult
from ..services.video_service import video_service, VideoResult

logger = logging.getLogger(__name__)

@dataclass
class WorkflowConfig:
    """工作流程配置"""
    text: str
    style: str = "现代"
    voice: str = "中文女声"
    image_width: int = 1280
    image_height: int = 720
    output_name: str = ""

@dataclass
class WorkflowResult:
    """工作流程结果"""
    success: bool
    video_path: str = ""
    storyboard: Optional[StoryboardResult] = None
    images: List[ImageResult] = None
    voices: List[VoiceResult] = None
    video: Optional[VideoResult] = None
    error_message: str = ""
    total_time: float = 0.0

class WorkflowEngine:
    """工作流程引擎"""
    
    def __init__(self):
        self.current_workflow = None
        self.progress_callback: Optional[Callable] = None
        
    def set_progress_callback(self, callback: Callable[[float, str], None]):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def _update_progress(self, progress: float, message: str):
        """更新进度"""
        if self.progress_callback:
            self.progress_callback(progress, message)
        logger.info(f"进度: {progress:.1%} - {message}")
    
    async def generate_video(self, config: WorkflowConfig) -> WorkflowResult:
        """执行完整的视频生成工作流程"""
        start_time = time.time()
        
        try:
            logger.info("开始执行视频生成工作流程")
            self._update_progress(0.0, "初始化工作流程...")
            
            # 步骤1: 生成分镜脚本 (0-20%)
            self._update_progress(0.05, "正在生成分镜脚本...")
            storyboard = await self._step_generate_storyboard(config)
            
            if not storyboard.success:
                return WorkflowResult(
                    success=False,
                    error_message=f"分镜生成失败: {storyboard.error_message}"
                )
            
            self._update_progress(0.2, f"分镜生成完成，共{len(storyboard.shots)}个镜头")
            
            # 步骤2: 生成图像 (20-60%)
            self._update_progress(0.25, "正在生成图像...")
            images = await self._step_generate_images(storyboard, config)
            
            success_images = [img for img in images if img.success]
            if not success_images:
                return WorkflowResult(
                    success=False,
                    storyboard=storyboard,
                    images=images,
                    error_message="图像生成失败"
                )
            
            self._update_progress(0.6, f"图像生成完成，成功{len(success_images)}张")
            
            # 步骤3: 生成语音 (60-80%)
            self._update_progress(0.65, "正在生成语音...")
            voices = await self._step_generate_voices(storyboard, config)
            
            success_voices = [voice for voice in voices if voice.success]
            if not success_voices:
                return WorkflowResult(
                    success=False,
                    storyboard=storyboard,
                    images=images,
                    voices=voices,
                    error_message="语音生成失败"
                )
            
            self._update_progress(0.8, f"语音生成完成，成功{len(success_voices)}段")
            
            # 步骤4: 合成视频 (80-100%)
            self._update_progress(0.85, "正在合成视频...")
            video = await self._step_compose_video(success_images, success_voices, config)
            
            if not video.success:
                return WorkflowResult(
                    success=False,
                    storyboard=storyboard,
                    images=images,
                    voices=voices,
                    video=video,
                    error_message=f"视频合成失败: {video.error_message}"
                )
            
            total_time = time.time() - start_time
            self._update_progress(1.0, f"视频生成完成！总耗时: {total_time:.1f}秒")
            
            logger.info(f"工作流程执行成功，输出: {video.video_path}")
            
            return WorkflowResult(
                success=True,
                video_path=video.video_path,
                storyboard=storyboard,
                images=images,
                voices=voices,
                video=video,
                total_time=total_time
            )
            
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"工作流程执行失败: {e}")
            
            return WorkflowResult(
                success=False,
                error_message=str(e),
                total_time=total_time
            )
    
    async def _step_generate_storyboard(self, config: WorkflowConfig) -> StoryboardResult:
        """步骤1: 生成分镜脚本"""
        try:
            return await llm_service.generate_storyboard(config.text, config.style)
        except Exception as e:
            logger.error(f"分镜生成步骤失败: {e}")
            return StoryboardResult(
                success=False,
                shots=[],
                total_duration=0,
                error_message=str(e)
            )
    
    async def _step_generate_images(self, storyboard: StoryboardResult, config: WorkflowConfig) -> List[ImageResult]:
        """步骤2: 生成图像"""
        try:
            prompts = [shot.image_prompt for shot in storyboard.shots]
            
            # 批量生成图像，带进度更新
            results = []
            total_shots = len(prompts)
            
            for i, prompt in enumerate(prompts):
                progress = 0.2 + (i / total_shots) * 0.4  # 20%-60%
                self._update_progress(progress, f"生成第{i+1}/{total_shots}张图像...")
                
                result = await image_service.generate_image(
                    prompt, 
                    config.image_width, 
                    config.image_height
                )
                results.append(result)
                
                # 添加延迟避免API限制
                await asyncio.sleep(1)
            
            return results
            
        except Exception as e:
            logger.error(f"图像生成步骤失败: {e}")
            return []
    
    async def _step_generate_voices(self, storyboard: StoryboardResult, config: WorkflowConfig) -> List[VoiceResult]:
        """步骤3: 生成语音"""
        try:
            texts = [shot.narration for shot in storyboard.shots if shot.narration.strip()]
            
            if not texts:
                logger.warning("没有旁白文本，跳过语音生成")
                return []
            
            # 批量生成语音，带进度更新
            results = []
            total_texts = len(texts)
            
            for i, text in enumerate(texts):
                progress = 0.6 + (i / total_texts) * 0.2  # 60%-80%
                self._update_progress(progress, f"生成第{i+1}/{total_texts}段语音...")
                
                result = await voice_service.synthesize_speech(text, config.voice)
                results.append(result)
                
                # 添加延迟
                await asyncio.sleep(0.5)
            
            return results
            
        except Exception as e:
            logger.error(f"语音生成步骤失败: {e}")
            return []
    
    async def _step_compose_video(self, images: List[ImageResult], voices: List[VoiceResult], config: WorkflowConfig) -> VideoResult:
        """步骤4: 合成视频"""
        try:
            # 提取文件路径
            image_paths = [img.image_path for img in images if img.success and img.image_path]
            audio_paths = [voice.audio_path for voice in voices if voice.success and voice.audio_path]
            
            if not image_paths:
                return VideoResult(
                    success=False,
                    error_message="没有可用的图像文件"
                )
            
            # 生成输出文件名
            output_name = config.output_name
            if not output_name:
                timestamp = int(time.time())
                output_name = f"ai_video_{timestamp}.mp4"
            
            # 合成视频
            return await video_service.create_video_from_images_and_audio(
                image_paths, 
                audio_paths, 
                output_name
            )
            
        except Exception as e:
            logger.error(f"视频合成步骤失败: {e}")
            return VideoResult(
                success=False,
                error_message=str(e)
            )
    
    def get_service_status(self) -> Dict[str, bool]:
        """获取各服务状态"""
        return {
            "LLM服务": True,  # LLM服务总是可用（有模拟数据）
            "图像生成": True,  # Pollinations免费服务
            "语音合成": True,  # Edge TTS免费服务
            "视频合成": video_service.ffmpeg_available
        }

# 全局工作流程引擎实例
workflow_engine = WorkflowEngine()
