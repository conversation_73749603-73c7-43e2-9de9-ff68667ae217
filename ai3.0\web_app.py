#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 3.0 - Streamlit Web界面
快速原型版本
"""

import streamlit as st
import json
import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 页面配置
st.set_page_config(
    page_title="AI视频生成器 3.0",
    page_icon="🎬",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
.main-header {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    padding: 1rem;
    border-radius: 10px;
    color: white;
    text-align: center;
    margin-bottom: 2rem;
}
.feature-card {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    margin: 1rem 0;
}
.status-success {
    background: #d4edda;
    color: #155724;
    padding: 0.5rem;
    border-radius: 5px;
    border-left: 4px solid #28a745;
}
.status-warning {
    background: #fff3cd;
    color: #856404;
    padding: 0.5rem;
    border-radius: 5px;
    border-left: 4px solid #ffc107;
}
.workflow-step {
    background: #e3f2fd;
    padding: 0.8rem;
    margin: 0.5rem 0;
    border-radius: 5px;
    border-left: 3px solid #2196f3;
}
</style>
""", unsafe_allow_html=True)

def load_config():
    """加载配置文件"""
    config_path = project_root / "config.json"
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    return None

def check_api_keys(config):
    """检查API密钥配置状态"""
    if not config:
        return {}
    
    status = {}
    
    # LLM服务
    llm_services = config.get('llm', {}).get('services', {})
    for service, conf in llm_services.items():
        api_key = conf.get('api_key', '')
        status[f'LLM-{service}'] = '✅ 已配置' if api_key and api_key != 'your_api_key_here' else '❌ 未配置'
    
    # 翻译服务
    translation_services = config.get('translation', {}).get('services', {})
    for service, conf in translation_services.items():
        if service == 'baidu':
            app_id = conf.get('app_id', '')
            secret_key = conf.get('secret_key', '')
            status[f'翻译-{service}'] = '✅ 已配置' if app_id and secret_key else '❌ 未配置'
        else:
            api_key = conf.get('api_key', '')
            status[f'翻译-{service}'] = '✅ 已配置' if api_key and api_key != 'your_api_key_here' else '❌ 未配置'
    
    # 图像生成服务
    image_services = config.get('image_generation', {}).get('services', {})
    for service, conf in image_services.items():
        if service in ['pollinations', 'comfyui_local']:
            status[f'图像-{service}'] = '✅ 免费服务'
        else:
            api_key = conf.get('api_key', '')
            status[f'图像-{service}'] = '✅ 已配置' if api_key and api_key != 'your_api_key_here' else '❌ 未配置'
    
    # 视频生成服务
    video_services = config.get('video_generation', {}).get('services', {})
    for service, conf in video_services.items():
        api_key = conf.get('api_key', '')
        status[f'视频-{service}'] = '✅ 已配置' if api_key and api_key != 'your_api_key_here' else '❌ 未配置'
    
    return status

def simulate_workflow_step(step_name, duration=2):
    """模拟工作流步骤"""
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    for i in range(duration * 10):
        progress = (i + 1) / (duration * 10)
        progress_bar.progress(progress)
        status_text.text(f'{step_name}... {int(progress * 100)}%')
        time.sleep(0.1)
    
    status_text.text(f'{step_name} ✅ 完成')
    return True

def main():
    """主函数"""
    
    # 页面标题
    st.markdown("""
    <div class="main-header">
        <h1>🎬 AI视频生成器 3.0</h1>
        <p>基于MCP工具集成的全自动AI视频生成系统 - Web界面版</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 侧边栏
    with st.sidebar:
        st.header("🛠️ 功能菜单")
        
        page = st.selectbox(
            "选择功能",
            ["🏠 首页概览", "🎯 快速生成", "⚙️ 配置管理", "📊 服务状态", "🔧 系统设置"]
        )
        
        st.markdown("---")
        st.markdown("### 📈 系统状态")
        
        # 加载配置
        config = load_config()
        if config:
            st.success("✅ 配置文件已加载")
        else:
            st.error("❌ 配置文件未找到")
        
        # API密钥状态
        if config:
            api_status = check_api_keys(config)
            configured_count = sum(1 for status in api_status.values() if '✅' in status)
            total_count = len(api_status)
            
            st.metric(
                "API服务状态",
                f"{configured_count}/{total_count}",
                f"{configured_count/total_count*100:.0f}% 已配置" if total_count > 0 else "无服务"
            )
    
    # 主内容区域
    if page == "🏠 首页概览":
        show_home_page(config)
    elif page == "🎯 快速生成":
        show_generation_page(config)
    elif page == "⚙️ 配置管理":
        show_config_page(config)
    elif page == "📊 服务状态":
        show_status_page(config)
    elif page == "🔧 系统设置":
        show_settings_page()

def show_home_page(config):
    """显示首页"""
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("""
        <div class="feature-card">
            <h3>🚀 欢迎使用AI视频生成器 3.0</h3>
            <p>这是基于MCP工具集成的全自动AI视频生成系统的Web界面版本。</p>
            <p>支持从文本到视频的完整工作流程，集成多种AI服务。</p>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("### 🎯 核心功能")
        
        features = [
            ("📝", "智能文章创作", "基于主题自动生成高质量文章内容"),
            ("🎬", "自动分镜生成", "将文章智能分解为视频分镜脚本"),
            ("🎵", "AI语音合成", "多语言、多音色的专业配音生成"),
            ("🖼️", "图像自动生成", "根据分镜描述生成匹配的视觉内容"),
            ("🎞️", "视频智能合成", "将图像和音频合成为完整视频"),
            ("📱", "一键社交发布", "自动发布到各大社交媒体平台")
        ]
        
        for icon, title, desc in features:
            st.markdown(f"""
            <div class="workflow-step">
                <strong>{icon} {title}</strong><br>
                <small>{desc}</small>
            </div>
            """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("### 📊 系统概览")
        
        if config:
            api_status = check_api_keys(config)
            
            # 服务统计
            llm_count = sum(1 for k in api_status.keys() if k.startswith('LLM-') and '✅' in api_status[k])
            image_count = sum(1 for k in api_status.keys() if k.startswith('图像-') and '✅' in api_status[k])
            video_count = sum(1 for k in api_status.keys() if k.startswith('视频-') and '✅' in api_status[k])
            
            st.metric("LLM服务", llm_count, "个可用")
            st.metric("图像生成", image_count, "个可用")
            st.metric("视频生成", video_count, "个可用")
            
            st.markdown("---")
            st.markdown("### 🎯 快速开始")
            
            if st.button("🚀 开始创作视频", type="primary", use_container_width=True):
                st.switch_page = "🎯 快速生成"
                st.rerun()
            
            if st.button("⚙️ 查看配置", use_container_width=True):
                st.switch_page = "⚙️ 配置管理"
                st.rerun()
        else:
            st.error("请先配置系统")

def show_generation_page(config):
    """显示视频生成页面"""
    
    st.header("🎯 AI视频生成")
    
    if not config:
        st.error("❌ 配置文件未找到，请先进行系统配置")
        return
    
    # 输入区域
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("📝 创作设置")
        
        # 主题输入
        topic = st.text_input(
            "视频主题",
            placeholder="例如：人工智能的发展历程",
            help="输入您想要创作的视频主题"
        )
        
        # 高级设置
        with st.expander("🔧 高级设置"):
            col_a, col_b = st.columns(2)
            
            with col_a:
                video_duration = st.selectbox(
                    "视频时长",
                    ["30秒", "1分钟", "2分钟", "5分钟"],
                    index=1
                )
                
                video_style = st.selectbox(
                    "视频风格",
                    ["专业商务", "轻松活泼", "科技感", "文艺清新"],
                    index=0
                )
            
            with col_b:
                voice_type = st.selectbox(
                    "配音类型",
                    ["男声-专业", "女声-温和", "男声-活力", "女声-甜美"],
                    index=1
                )
                
                language = st.selectbox(
                    "语言",
                    ["中文", "英文", "日文", "韩文"],
                    index=0
                )
    
    with col2:
        st.subheader("📊 生成预览")
        
        if topic:
            st.markdown(f"""
            <div class="feature-card">
                <strong>🎬 视频预览</strong><br>
                <strong>主题:</strong> {topic}<br>
                <strong>时长:</strong> {video_duration}<br>
                <strong>风格:</strong> {video_style}<br>
                <strong>配音:</strong> {voice_type}<br>
                <strong>语言:</strong> {language}
            </div>
            """, unsafe_allow_html=True)
        else:
            st.info("请输入视频主题以查看预览")
    
    # 生成按钮
    st.markdown("---")
    
    col_btn1, col_btn2, col_btn3 = st.columns([1, 2, 1])
    
    with col_btn2:
        if st.button("🚀 开始生成视频", type="primary", use_container_width=True, disabled=not topic):
            generate_video_workflow(topic, video_duration, video_style, voice_type, language)

def generate_video_workflow(topic, duration, style, voice, language):
    """执行视频生成工作流"""
    
    st.subheader("🔄 视频生成进行中...")
    
    # 工作流步骤
    steps = [
        ("📝 AI文章创作", f"基于主题'{topic}'生成文章内容"),
        ("🎬 智能分镜生成", "将文章转换为视频分镜脚本"),
        ("🎵 语音合成", f"使用{voice}生成{language}配音"),
        ("🖼️ 图像生成", "为每个分镜生成匹配图像"),
        ("🎞️ 视频合成", f"合成{duration}的{style}风格视频"),
        ("📱 准备发布", "视频生成完成，准备发布")
    ]
    
    # 创建进度容器
    progress_container = st.container()
    
    with progress_container:
        for i, (step_name, step_desc) in enumerate(steps):
            st.markdown(f"""
            <div class="workflow-step">
                <strong>步骤 {i+1}: {step_name}</strong><br>
                <small>{step_desc}</small>
            </div>
            """, unsafe_allow_html=True)
            
            # 模拟处理时间
            simulate_workflow_step(step_name, duration=2)
            
            st.success(f"✅ {step_name} 完成")
    
    # 完成提示
    st.balloons()
    st.success("🎉 视频生成完成！")
    
    # 结果展示
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.download_button(
            "📥 下载视频",
            data=b"fake_video_data",
            file_name=f"{topic}_video.mp4",
            mime="video/mp4"
        )
    
    with col2:
        if st.button("📱 发布到社交媒体"):
            st.info("发布功能开发中...")
    
    with col3:
        if st.button("🔄 重新生成"):
            st.rerun()

def show_config_page(config):
    """显示配置页面"""
    
    st.header("⚙️ 配置管理")
    
    if not config:
        st.error("❌ 配置文件未找到")
        return
    
    # API密钥状态
    st.subheader("🔑 API密钥状态")
    
    api_status = check_api_keys(config)
    
    # 按服务类型分组显示
    service_groups = {
        "🤖 LLM服务": [k for k in api_status.keys() if k.startswith('LLM-')],
        "🌐 翻译服务": [k for k in api_status.keys() if k.startswith('翻译-')],
        "🖼️ 图像生成": [k for k in api_status.keys() if k.startswith('图像-')],
        "🎞️ 视频生成": [k for k in api_status.keys() if k.startswith('视频-')]
    }
    
    for group_name, services in service_groups.items():
        if services:
            st.markdown(f"### {group_name}")
            
            for service in services:
                status = api_status[service]
                service_name = service.split('-', 1)[1]
                
                col1, col2 = st.columns([3, 1])
                with col1:
                    st.markdown(f"**{service_name}**")
                with col2:
                    if '✅' in status:
                        st.markdown(f'<div class="status-success">{status}</div>', unsafe_allow_html=True)
                    else:
                        st.markdown(f'<div class="status-warning">{status}</div>', unsafe_allow_html=True)
    
    # 配置文件预览
    st.markdown("---")
    st.subheader("📄 配置文件预览")
    
    with st.expander("查看完整配置"):
        st.json(config)

def show_status_page(config):
    """显示服务状态页面"""
    
    st.header("📊 服务状态")
    
    if not config:
        st.error("❌ 配置文件未找到")
        return
    
    # 实时状态检查
    st.subheader("🔄 实时服务检查")
    
    if st.button("🔍 检查所有服务状态", type="primary"):
        with st.spinner("正在检查服务状态..."):
            # 模拟服务检查
            time.sleep(2)
            
            st.success("✅ 服务状态检查完成")
            
            # 显示模拟的服务状态
            services_status = {
                "LLM服务": {"智谱AI": "正常", "通义千问": "正常", "Deepseek": "正常", "Gemini": "正常"},
                "图像生成": {"Pollinations": "正常", "ComfyUI": "离线", "CogView": "正常"},
                "视频生成": {"CogVideoX": "正常", "Replicate": "未配置"},
                "翻译服务": {"百度翻译": "正常", "Google翻译": "正常"}
            }
            
            for category, services in services_status.items():
                st.markdown(f"### {category}")
                
                cols = st.columns(len(services))
                for i, (service, status) in enumerate(services.items()):
                    with cols[i]:
                        if status == "正常":
                            st.metric(service, "✅", "在线")
                        elif status == "离线":
                            st.metric(service, "⚠️", "离线")
                        else:
                            st.metric(service, "❌", status)

def show_settings_page():
    """显示系统设置页面"""
    
    st.header("🔧 系统设置")
    
    # 界面设置
    st.subheader("🎨 界面设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        theme = st.selectbox("主题", ["浅色", "深色", "自动"])
        language_ui = st.selectbox("界面语言", ["中文", "English", "日本語"])
    
    with col2:
        auto_refresh = st.checkbox("自动刷新状态", value=True)
        show_advanced = st.checkbox("显示高级选项", value=False)
    
    # 性能设置
    st.subheader("⚡ 性能设置")
    
    max_concurrent = st.slider("最大并发任务", 1, 10, 3)
    timeout_seconds = st.slider("请求超时时间(秒)", 10, 300, 60)
    
    # 保存设置
    if st.button("💾 保存设置", type="primary"):
        st.success("✅ 设置已保存")
    
    # 系统信息
    st.markdown("---")
    st.subheader("ℹ️ 系统信息")
    
    system_info = {
        "版本": "AI视频生成器 3.0",
        "架构": "MCP工具集成",
        "界面": "Streamlit Web界面",
        "Python版本": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        "运行时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    for key, value in system_info.items():
        st.text(f"{key}: {value}")

if __name__ == "__main__":
    main()