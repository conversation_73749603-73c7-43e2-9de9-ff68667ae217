"""服务层单元测试

测试各种AI服务的功能。
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
import aiohttp
from pathlib import Path

from src.services.base import ServiceConfig, ServiceResponse, ServiceStatus
from src.services.llm import OpenAILLMService, ZhipuLLMService
from src.services.image import PollinationsImageService, OpenAIImageService
from src.services.voice import EdgeTTSService, OpenAITTSService
from src.services.video import CogVideoXService, LocalVideoComposer
from src.services.registry import ServiceRegistry, ServiceType, ServiceRegistration
from src.core.exceptions import APIKeyInvalidError, RateLimitExceededError


class TestServiceBase:
    """测试服务基类"""
    
    def test_service_config_creation(self):
        """测试服务配置创建"""
        config = ServiceConfig(
            api_key="test_key",
            base_url="https://api.test.com",
            timeout=30,
            custom_settings={"model": "test-model"}
        )
        
        assert config.api_key == "test_key"
        assert config.base_url == "https://api.test.com"
        assert config.timeout == 30
        assert config.custom_settings["model"] == "test-model"
    
    def test_service_response_success(self):
        """测试成功响应"""
        response = ServiceResponse.success_response("test_data", {"key": "value"})
        
        assert response.success is True
        assert response.data == "test_data"
        assert response.metadata["key"] == "value"
        assert response.error_code is None
    
    def test_service_response_error(self):
        """测试错误响应"""
        response = ServiceResponse.error_response("Test error", "ERROR_001")
        
        assert response.success is False
        assert response.data is None
        assert response.error_message == "Test error"
        assert response.error_code == "ERROR_001"


class TestLLMServices:
    """测试LLM服务"""
    
    @pytest.fixture
    def openai_config(self):
        """OpenAI配置"""
        return ServiceConfig(
            api_key="test_openai_key",
            base_url="https://api.openai.com/v1",
            timeout=60
        )
    
    @pytest.fixture
    def zhipu_config(self):
        """智谱配置"""
        return ServiceConfig(
            api_key="test_zhipu_key",
            base_url="https://open.bigmodel.cn/api/paas/v4",
            timeout=60
        )
    
    def test_openai_service_creation(self, openai_config):
        """测试OpenAI服务创建"""
        service = OpenAILLMService(openai_config)
        
        assert service.config.api_key == "test_openai_key"
        assert service.base_url == "https://api.openai.com/v1"
        assert service.model == "gpt-4"
        assert service.status == ServiceStatus.UNKNOWN
    
    def test_zhipu_service_creation(self, zhipu_config):
        """测试智谱服务创建"""
        service = ZhipuLLMService(zhipu_config)
        
        assert service.config.api_key == "test_zhipu_key"
        assert service.base_url == "https://open.bigmodel.cn/api/paas/v4"
        assert service.model == "glm-4"
    
    @pytest.mark.asyncio
    async def test_openai_prompt_validation(self, openai_config):
        """测试OpenAI提示词验证"""
        service = OpenAILLMService(openai_config)
        
        # 有效提示词
        assert await service.validate_prompt("Hello, world!") is True
        
        # 无效提示词
        assert await service.validate_prompt("") is False
        assert await service.validate_prompt("   ") is False
        assert await service.validate_prompt("a" * 10001) is False  # 太长
    
    @pytest.mark.asyncio
    @patch('aiohttp.ClientSession.post')
    async def test_openai_chat_completion_success(self, mock_post, openai_config):
        """测试OpenAI聊天完成成功"""
        # 模拟成功响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {
            "choices": [{
                "message": {"content": "Hello! How can I help you?"},
                "finish_reason": "stop"
            }],
            "model": "gpt-4",
            "usage": {"total_tokens": 20}
        }
        mock_post.return_value.__aenter__.return_value = mock_response
        
        service = OpenAILLMService(openai_config)
        messages = [{"role": "user", "content": "Hello"}]
        
        result = await service.chat_completion(messages)
        
        assert result.success is True
        assert "Hello! How can I help you?" in result.data
        assert result.metadata["model"] == "gpt-4"
        assert result.metadata["usage"]["total_tokens"] == 20
    
    @pytest.mark.asyncio
    @patch('aiohttp.ClientSession.post')
    async def test_openai_rate_limit_error(self, mock_post, openai_config):
        """测试OpenAI速率限制错误"""
        # 模拟速率限制响应
        mock_response = AsyncMock()
        mock_response.status = 429
        mock_response.headers = {"Retry-After": "60"}
        mock_post.return_value.__aenter__.return_value = mock_response
        
        service = OpenAILLMService(openai_config)
        messages = [{"role": "user", "content": "Hello"}]
        
        with pytest.raises(RateLimitExceededError) as exc_info:
            await service.chat_completion(messages)
        
        assert exc_info.value.retry_after == 60


class TestImageServices:
    """测试图像服务"""
    
    @pytest.fixture
    def pollinations_config(self):
        """Pollinations配置"""
        return ServiceConfig()
    
    @pytest.fixture
    def openai_image_config(self):
        """OpenAI图像配置"""
        return ServiceConfig(
            api_key="test_openai_key",
            base_url="https://api.openai.com/v1"
        )
    
    def test_pollinations_service_creation(self, pollinations_config):
        """测试Pollinations服务创建"""
        service = PollinationsImageService(pollinations_config)
        
        assert service.base_url == "https://image.pollinations.ai"
        assert service.status == ServiceStatus.UNKNOWN
    
    @pytest.mark.asyncio
    async def test_pollinations_prompt_validation(self, pollinations_config):
        """测试Pollinations提示词验证"""
        service = PollinationsImageService(pollinations_config)
        
        # 有效提示词
        assert await service.validate_prompt("A beautiful sunset") is True
        
        # 无效提示词
        assert await service.validate_prompt("") is False
        assert await service.validate_prompt("   ") is False
    
    @pytest.mark.asyncio
    @patch('aiohttp.ClientSession.get')
    async def test_pollinations_image_generation(self, mock_get, pollinations_config, temp_dir):
        """测试Pollinations图像生成"""
        # 模拟图像数据
        mock_image_data = b"fake_image_data"
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.read.return_value = mock_image_data
        mock_get.return_value.__aenter__.return_value = mock_response
        
        service = PollinationsImageService(pollinations_config)
        service.output_dir = temp_dir / "images"
        service.output_dir.mkdir()
        
        result = await service.generate_image("A beautiful sunset")
        
        assert result.success is True
        assert Path(result.data).exists()
        assert result.metadata["prompt"] == "A beautiful sunset"


class TestVoiceServices:
    """测试语音服务"""
    
    @pytest.fixture
    def edge_tts_config(self):
        """Edge TTS配置"""
        return ServiceConfig()
    
    @pytest.fixture
    def openai_tts_config(self):
        """OpenAI TTS配置"""
        return ServiceConfig(
            api_key="test_openai_key",
            base_url="https://api.openai.com/v1"
        )
    
    def test_edge_tts_service_creation(self, edge_tts_config):
        """测试Edge TTS服务创建"""
        service = EdgeTTSService(edge_tts_config)
        
        assert "zh-CN" in service.voices
        assert "en-US" in service.voices
        assert len(service.voices["zh-CN"]["female"]) > 0
    
    @pytest.mark.asyncio
    async def test_edge_tts_text_validation(self, edge_tts_config):
        """测试Edge TTS文本验证"""
        service = EdgeTTSService(edge_tts_config)
        
        # 有效文本
        assert await service.validate_text("Hello, world!") is True
        
        # 无效文本
        assert await service.validate_text("") is False
        assert await service.validate_text("   ") is False
        assert await service.validate_text("a" * 5001) is False  # 太长
    
    @pytest.mark.asyncio
    async def test_edge_tts_get_available_voices(self, edge_tts_config):
        """测试获取可用语音"""
        service = EdgeTTSService(edge_tts_config)
        
        zh_voices = await service.get_available_voices("zh-CN")
        assert len(zh_voices) > 0
        assert "zh-CN-XiaoxiaoNeural" in zh_voices
        
        en_voices = await service.get_available_voices("en-US")
        assert len(en_voices) > 0
        assert "en-US-AriaNeural" in en_voices


class TestVideoServices:
    """测试视频服务"""
    
    @pytest.fixture
    def cogvideox_config(self):
        """CogVideoX配置"""
        return ServiceConfig(
            api_key="test_cogvideox_key",
            base_url="https://api.cogvideox.com/v1"
        )
    
    @pytest.fixture
    def local_composer_config(self):
        """本地合成器配置"""
        return ServiceConfig()
    
    def test_cogvideox_service_creation(self, cogvideox_config):
        """测试CogVideoX服务创建"""
        service = CogVideoXService(cogvideox_config)
        
        assert service.config.api_key == "test_cogvideox_key"
        assert service.base_url == "https://api.cogvideox.com/v1"
        assert service.model == "cogvideox-5b"
    
    @pytest.mark.asyncio
    async def test_cogvideox_prompt_validation(self, cogvideox_config):
        """测试CogVideoX提示词验证"""
        service = CogVideoXService(cogvideox_config)
        
        # 有效提示词
        assert await service.validate_prompt("A girl walking in forest") is True
        
        # 无效提示词
        assert await service.validate_prompt("") is False
        assert await service.validate_prompt("   ") is False
        assert await service.validate_prompt("a" * 2001) is False  # 太长
    
    def test_local_composer_service_creation(self, local_composer_config):
        """测试本地合成器服务创建"""
        service = LocalVideoComposer(local_composer_config)
        
        assert service.output_dir.name == "videos"
        assert service.status == ServiceStatus.UNKNOWN


class TestServiceRegistry:
    """测试服务注册表"""
    
    def test_registry_creation(self):
        """测试注册表创建"""
        registry = ServiceRegistry()
        
        # 检查默认服务是否注册
        services = registry.get_available_services()
        assert len(services) > 0
        
        # 检查各类型服务
        llm_services = registry.get_available_services(ServiceType.LLM)
        assert len(llm_services) >= 2  # OpenAI和智谱
        
        image_services = registry.get_available_services(ServiceType.IMAGE)
        assert len(image_services) >= 2  # Pollinations和OpenAI
    
    def test_service_registration(self):
        """测试服务注册"""
        registry = ServiceRegistry()
        
        # 注册自定义服务
        registration = ServiceRegistration(
            service_type=ServiceType.LLM,
            service_class=OpenAILLMService,
            name="custom_llm",
            description="Custom LLM service",
            priority=10
        )
        
        registry.register_service(registration)
        
        # 验证注册
        custom_service = registry.get_service_registration("custom_llm")
        assert custom_service is not None
        assert custom_service.name == "custom_llm"
        assert custom_service.priority == 10
    
    @pytest.mark.asyncio
    async def test_service_creation(self, mock_service_config):
        """测试服务创建"""
        registry = ServiceRegistry()
        
        config = {
            "api_key": "test_key",
            "base_url": "https://api.test.com",
            "timeout": 30
        }
        
        service = await registry.create_service("openai_llm", config)
        
        assert service is not None
        assert isinstance(service, OpenAILLMService)
        assert service.config.api_key == "test_key"
    
    def test_get_services_by_type(self):
        """测试按类型获取服务"""
        registry = ServiceRegistry()
        
        # 创建一些服务实例（模拟）
        config = {"api_key": "test"}
        
        # 这里只测试注册表的逻辑，不实际创建服务
        llm_registrations = registry.get_available_services(ServiceType.LLM)
        assert len(llm_registrations) >= 2
        
        image_registrations = registry.get_available_services(ServiceType.IMAGE)
        assert len(image_registrations) >= 2
