"""服务层模块

包含所有服务层的实现：
- AI服务接口
- LLM服务
- 图像服务
- 视频服务
- 语音服务
"""

from .base import (
    BaseService,
    AIService,
    LLMService,
    ImageGenerationService,
    VideoGenerationService,
    VoiceGenerationService,
    ServiceManager,
    ServiceConfig,
    ServiceResponse,
    ServiceStatus
)

from .llm import OpenAILLMService, ZhipuLLMService
from .image import PollinationsImageService, OpenAIImageService
from .voice import EdgeTTSService, OpenAITTSService
from .video import CogVideoXService, LocalVideoComposer

__all__ = [
    # 基础服务
    "BaseService",
    "AIService",
    "LLMService",
    "ImageGenerationService",
    "VideoGenerationService",
    "VoiceGenerationService",
    "ServiceManager",
    "ServiceConfig",
    "ServiceResponse",
    "ServiceStatus",

    # LLM服务
    "OpenAILLMService",
    "ZhipuLLMService",

    # 图像服务
    "PollinationsImageService",
    "OpenAIImageService",

    # 语音服务
    "EdgeTTSService",
    "OpenAITTSService",

    # 视频服务
    "CogVideoXService",
    "LocalVideoComposer"
]
