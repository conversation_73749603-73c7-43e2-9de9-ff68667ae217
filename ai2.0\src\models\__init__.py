"""数据模型模块

导出所有数据模型和相关功能。
"""

from .base import (
    Base,
    BaseEntity,
    TimestampMixin,
    SoftDeleteMixin,
    BaseSchema,
    TimestampSchema,
    BaseEntitySchema,
    CreateEntitySchema,
    UpdateEntitySchema,
    ProjectStatus,
    StoryboardStatus,
    ShotType,
    MediaType,
    MediaStatus,
    GenerationProvider,
    Constants
)

from .project import (
    Project,
    ProjectSchema,
    CreateProjectSchema,
    UpdateProjectSchema,
    ProjectSummarySchema,
    ProjectSettingsSchema,
    ProjectConstants
)

from .storyboard import (
    Storyboard,
    StoryboardSchema,
    CreateStoryboardSchema,
    UpdateStoryboardSchema,
    StoryboardSummarySchema,
    StoryboardGenerationRequest,
    StoryboardGenerationResponse,
    StoryboardConstants
)

from .shot import (
    Shot,
    ShotSchema,
    CreateShotSchema,
    UpdateShotSchema,
    ShotSummarySchema,
    ShotConstants
)

from .media import (
    MediaItem,
    MediaItemSchema,
    CreateMediaItemSchema,
    UpdateMediaItemSchema,
    MediaGenerationRequest,
    MediaGenerationResponse,
    MediaConstants
)

# 导出所有模型类
__all__ = [
    # 基础类
    "Base",
    "BaseEntity",
    "TimestampMixin",
    "SoftDeleteMixin",

    # 基础模式
    "BaseSchema",
    "TimestampSchema",
    "BaseEntitySchema",
    "CreateEntitySchema",
    "UpdateEntitySchema",

    # 枚举类型
    "ProjectStatus",
    "StoryboardStatus",
    "ShotType",
    "MediaType",
    "MediaStatus",
    "GenerationProvider",

    # 项目相关
    "Project",
    "ProjectSchema",
    "CreateProjectSchema",
    "UpdateProjectSchema",
    "ProjectSummarySchema",
    "ProjectSettingsSchema",
    "ProjectConstants",

    # 分镜相关
    "Storyboard",
    "StoryboardSchema",
    "CreateStoryboardSchema",
    "UpdateStoryboardSchema",
    "StoryboardSummarySchema",
    "StoryboardGenerationRequest",
    "StoryboardGenerationResponse",
    "StoryboardConstants",

    # 镜头相关
    "Shot",
    "ShotSchema",
    "CreateShotSchema",
    "UpdateShotSchema",
    "ShotSummarySchema",
    "ShotConstants",

    # 媒体相关
    "MediaItem",
    "MediaItemSchema",
    "CreateMediaItemSchema",
    "UpdateMediaItemSchema",
    "MediaGenerationRequest",
    "MediaGenerationResponse",
    "MediaConstants",

    # 常量
    "Constants"
]
