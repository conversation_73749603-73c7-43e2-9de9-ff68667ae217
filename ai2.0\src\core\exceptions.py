"""异常定义

定义应用程序的异常层次结构。
"""

from typing import Dict, Any, Optional
from datetime import datetime
from enum import Enum


class ErrorCode(Enum):
    """错误代码"""
    # 通用错误
    UNKNOWN_ERROR = "E0001"
    VALIDATION_ERROR = "E0002"
    CONFIGURATION_ERROR = "E0003"
    
    # 业务逻辑错误
    BUSINESS_LOGIC_ERROR = "E1001"
    INVALID_STORYBOARD = "E1002"
    GENERATION_LIMIT_EXCEEDED = "E1003"
    CONSISTENCY_CHECK_FAILED = "E1004"
    
    # 服务错误
    SERVICE_ERROR = "E2001"
    AI_SERVICE_UNAVAILABLE = "E2002"
    RATE_LIMIT_EXCEEDED = "E2003"
    API_KEY_INVALID = "E2004"
    NETWORK_ERROR = "E2005"
    
    # 数据访问错误
    DATA_ACCESS_ERROR = "E3001"
    PROJECT_NOT_FOUND = "E3002"
    STORYBOARD_NOT_FOUND = "E3003"
    DATABASE_CONNECTION_ERROR = "E3004"
    
    # UI错误
    UI_ERROR = "E4001"
    COMPONENT_INITIALIZATION_ERROR = "E4002"
    THEME_LOAD_ERROR = "E4003"


class ApplicationError(Exception):
    """应用程序基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[ErrorCode] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or ErrorCode.UNKNOWN_ERROR
        self.details = details or {}
        self.cause = cause
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'error_code': self.error_code.value,
            'message': self.message,
            'details': self.details,
            'timestamp': self.timestamp.isoformat(),
            'cause': str(self.cause) if self.cause else None
        }
    
    def __str__(self) -> str:
        return f"[{self.error_code.value}] {self.message}"


class ValidationError(ApplicationError):
    """验证错误"""
    
    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        super().__init__(message, ErrorCode.VALIDATION_ERROR, **kwargs)
        if field:
            self.details['field'] = field


class ConfigurationError(ApplicationError):
    """配置错误"""
    
    def __init__(self, message: str, config_key: Optional[str] = None, **kwargs):
        super().__init__(message, ErrorCode.CONFIGURATION_ERROR, **kwargs)
        if config_key:
            self.details['config_key'] = config_key


# 业务逻辑异常
class BusinessLogicError(ApplicationError):
    """业务逻辑异常"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCode.BUSINESS_LOGIC_ERROR, **kwargs)


class InvalidStoryboardError(BusinessLogicError):
    """无效分镜异常"""
    
    def __init__(self, message: str, storyboard_id: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.error_code = ErrorCode.INVALID_STORYBOARD
        if storyboard_id:
            self.details['storyboard_id'] = storyboard_id


class GenerationLimitExceededError(BusinessLogicError):
    """生成限制超出异常"""
    
    def __init__(self, message: str, limit: Optional[int] = None, current: Optional[int] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.error_code = ErrorCode.GENERATION_LIMIT_EXCEEDED
        if limit is not None:
            self.details['limit'] = limit
        if current is not None:
            self.details['current'] = current


class ConsistencyCheckFailedError(BusinessLogicError):
    """一致性检查失败异常"""
    
    def __init__(self, message: str, check_type: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.error_code = ErrorCode.CONSISTENCY_CHECK_FAILED
        if check_type:
            self.details['check_type'] = check_type


# 服务异常
class ServiceError(ApplicationError):
    """服务异常"""
    
    def __init__(self, message: str, service_name: Optional[str] = None, **kwargs):
        super().__init__(message, ErrorCode.SERVICE_ERROR, **kwargs)
        if service_name:
            self.details['service_name'] = service_name


class AIServiceUnavailableError(ServiceError):
    """AI服务不可用异常"""
    
    def __init__(self, message: str, provider: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.error_code = ErrorCode.AI_SERVICE_UNAVAILABLE
        if provider:
            self.details['provider'] = provider


class RateLimitExceededError(ServiceError):
    """速率限制超出异常"""
    
    def __init__(self, message: str, retry_after: Optional[int] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.error_code = ErrorCode.RATE_LIMIT_EXCEEDED
        if retry_after:
            self.details['retry_after'] = retry_after


class APIKeyInvalidError(ServiceError):
    """API密钥无效异常"""
    
    def __init__(self, message: str, provider: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.error_code = ErrorCode.API_KEY_INVALID
        if provider:
            self.details['provider'] = provider


class NetworkError(ServiceError):
    """网络错误"""
    
    def __init__(self, message: str, url: Optional[str] = None, status_code: Optional[int] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.error_code = ErrorCode.NETWORK_ERROR
        if url:
            self.details['url'] = url
        if status_code:
            self.details['status_code'] = status_code


# 数据访问异常
class DataAccessError(ApplicationError):
    """数据访问异常"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCode.DATA_ACCESS_ERROR, **kwargs)


class ProjectNotFoundError(DataAccessError):
    """项目未找到异常"""
    
    def __init__(self, message: str, project_id: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.error_code = ErrorCode.PROJECT_NOT_FOUND
        if project_id:
            self.details['project_id'] = project_id


class StoryboardNotFoundError(DataAccessError):
    """分镜未找到异常"""
    
    def __init__(self, message: str, storyboard_id: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.error_code = ErrorCode.STORYBOARD_NOT_FOUND
        if storyboard_id:
            self.details['storyboard_id'] = storyboard_id


class DatabaseConnectionError(DataAccessError):
    """数据库连接错误"""
    
    def __init__(self, message: str, database_url: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.error_code = ErrorCode.DATABASE_CONNECTION_ERROR
        if database_url:
            # 隐藏敏感信息
            safe_url = database_url.split('@')[-1] if '@' in database_url else database_url
            self.details['database_url'] = safe_url


# UI异常
class UIError(ApplicationError):
    """UI异常"""
    
    def __init__(self, message: str, component: Optional[str] = None, **kwargs):
        super().__init__(message, ErrorCode.UI_ERROR, **kwargs)
        if component:
            self.details['component'] = component


class ComponentInitializationError(UIError):
    """组件初始化错误"""
    
    def __init__(self, message: str, component: Optional[str] = None, **kwargs):
        super().__init__(message, component, **kwargs)
        self.error_code = ErrorCode.COMPONENT_INITIALIZATION_ERROR


class ThemeLoadError(UIError):
    """主题加载错误"""
    
    def __init__(self, message: str, theme_name: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.error_code = ErrorCode.THEME_LOAD_ERROR
        if theme_name:
            self.details['theme_name'] = theme_name


# 异常处理装饰器
def handle_exceptions(default_return=None, log_errors=True):
    """异常处理装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except ApplicationError:
                # 重新抛出应用程序异常
                raise
            except Exception as e:
                if log_errors:
                    import logging
                    logger = logging.getLogger(func.__module__)
                    logger.error(f"Unexpected error in {func.__name__}: {e}")
                
                # 包装为应用程序异常
                raise ApplicationError(
                    f"Unexpected error in {func.__name__}: {str(e)}",
                    cause=e
                )
        return wrapper
    return decorator


def async_handle_exceptions(default_return=None, log_errors=True):
    """异步异常处理装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except ApplicationError:
                # 重新抛出应用程序异常
                raise
            except Exception as e:
                if log_errors:
                    import logging
                    logger = logging.getLogger(func.__module__)
                    logger.error(f"Unexpected error in {func.__name__}: {e}")
                
                # 包装为应用程序异常
                raise ApplicationError(
                    f"Unexpected error in {func.__name__}: {str(e)}",
                    cause=e
                )
        return wrapper
    return decorator
