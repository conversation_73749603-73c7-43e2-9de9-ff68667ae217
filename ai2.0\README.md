# AI视频生成器 2.0

基于现代化技术栈重新设计的AI视频生成器，提供智能分镜、图像生成、语音合成和视频制作的完整解决方案。

## 🚀 主要特性

- **智能分镜系统**: 基于LLM的五阶段文本解析和分镜生成
- **多引擎图像生成**: 支持多种AI图像生成服务
- **智能语音合成**: 集成多种TTS引擎
- **视频生成与合成**: 支持多种视频生成引擎
- **一致性控制**: 角色和场景的智能一致性管理
- **项目管理**: 完整的项目生命周期管理
- **多语言支持**: 国际化和本地化支持

## 🏗️ 技术架构

### 技术栈
- **前端**: PyQt6 + Material Design
- **后端**: Python 3.11+ + asyncio
- **数据库**: SQLite/PostgreSQL + SQLAlchemy 2.0
- **缓存**: Redis (可选)
- **AI服务**: OpenAI, 智谱AI, 通义千问, DeepSeek等

### 架构模式
- 分层架构 (UI → Controller → Business → Service → Data)
- 依赖注入
- 事件驱动
- 仓库模式

## 📦 安装要求

- Python 3.11+
- PyQt6
- 其他依赖见 requirements.txt

## 🛠️ 开发环境设置

```bash
# 克隆项目
git clone <repository-url>
cd ai2.0

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt

# 安装pre-commit钩子
pre-commit install

# 运行应用
python -m src.main
```

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=src --cov-report=html

# 运行特定类型的测试
pytest -m unit        # 单元测试
pytest -m integration # 集成测试
pytest -m ui          # UI测试
```

## 📝 代码质量

```bash
# 代码格式化
black src tests

# 导入排序
isort src tests

# 代码检查
flake8 src tests

# 类型检查
mypy src
```

## 🏃‍♂️ 快速开始

1. 启动应用程序
2. 创建新项目
3. 输入文本内容
4. 选择风格和参数
5. 生成分镜
6. 生成图像和视频

## 📚 文档

- [用户指南](docs/user/README.md)
- [开发者指南](docs/developer/README.md)
- [API文档](docs/api/README.md)
- [架构设计](docs/architecture.md)

## 🤝 贡献

请阅读 [CONTRIBUTING.md](CONTRIBUTING.md) 了解贡献指南。

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 🔄 版本历史

### v2.0.0 (开发中)
- 全新架构设计
- 现代化UI界面
- 增强的AI服务集成
- 改进的性能和稳定性

### v1.0.x
- 基础功能实现
- 初始版本发布

## 📞 支持

如有问题或建议，请提交 Issue 或联系开发团队。
