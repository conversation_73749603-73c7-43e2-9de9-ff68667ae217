#!/usr/bin/env python3
"""
AI视频生成器 2.0 依赖安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ {package} 安装成功")
            return True
        else:
            print(f"✗ {package} 安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ {package} 安装异常: {e}")
        return False

def main():
    """主函数"""
    print("AI视频生成器 2.0 - 依赖安装")
    print("=" * 50)
    
    # 必需的包
    required_packages = [
        "PyQt6",
        "sqlalchemy", 
        "aiohttp",
        "aiofiles",
        "psutil",
        "aiosqlite",
        "edge-tts"
    ]
    
    print(f"将安装 {len(required_packages)} 个依赖包...")
    print()
    
    success_count = 0
    failed_packages = []
    
    for package in required_packages:
        if install_package(package):
            success_count += 1
        else:
            failed_packages.append(package)
    
    print()
    print("=" * 50)
    print(f"安装完成: {success_count}/{len(required_packages)} 成功")
    
    if failed_packages:
        print(f"失败的包: {', '.join(failed_packages)}")
        print("\n请手动安装失败的包:")
        for package in failed_packages:
            print(f"  pip install {package}")
    else:
        print("所有依赖安装成功！")
        print("\n现在可以运行: python run.py")
    
    # 检查FFmpeg
    print("\n检查FFmpeg...")
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ FFmpeg已安装")
        else:
            print("⚠ FFmpeg未正确安装")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("⚠ FFmpeg未找到")
        print("  请从 https://ffmpeg.org/download.html 下载安装")
        print("  FFmpeg用于视频合成，没有它将无法生成最终视频")

if __name__ == "__main__":
    main()
