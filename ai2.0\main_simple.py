#!/usr/bin/env python3
"""AI视频生成器 2.0 简化版主程序

简化版本，避免复杂的初始化过程。
"""

import sys
import asyncio
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QIcon

from src.utils.logger import configure_logging, get_logger, LogLevel


class SimpleMainWindow(QMainWindow):
    """简化的主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AI视频生成器 2.0")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(40, 40, 40, 40)
        
        # 标题
        title_label = QLabel("AI视频生成器 2.0")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 32px;
                font-weight: bold;
                color: #2196F3;
                margin: 20px 0;
            }
        """)
        layout.addWidget(title_label)
        
        # 状态信息
        status_label = QLabel("程序启动成功！")
        status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        status_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                color: #4CAF50;
                margin: 10px 0;
            }
        """)
        layout.addWidget(status_label)
        
        # 功能说明
        info_label = QLabel("""
        欢迎使用AI视频生成器 2.0！
        
        这是一个简化版本的界面，用于验证程序基本功能。
        
        主要功能：
        • 智能分镜生成
        • AI图像生成
        • 语音合成
        • 视频制作
        
        如果您看到这个界面，说明程序已经成功启动。
        """)
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #666;
                margin: 20px 0;
                line-height: 1.6;
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        layout.addWidget(info_label)
        
        # 按钮区域
        button_layout = QVBoxLayout()
        button_layout.setSpacing(10)
        
        # 新建项目按钮
        new_project_btn = QPushButton("新建项目")
        new_project_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)
        new_project_btn.clicked.connect(self.on_new_project)
        button_layout.addWidget(new_project_btn)
        
        # 打开项目按钮
        open_project_btn = QPushButton("打开项目")
        open_project_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #388E3C;
            }
            QPushButton:pressed {
                background-color: #2E7D32;
            }
        """)
        open_project_btn.clicked.connect(self.on_open_project)
        button_layout.addWidget(open_project_btn)
        
        # 设置按钮
        settings_btn = QPushButton("设置")
        settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #E65100;
            }
        """)
        settings_btn.clicked.connect(self.on_settings)
        button_layout.addWidget(settings_btn)
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
        # 版本信息
        version_label = QLabel("Version 2.0.0")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        version_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #999;
                margin: 10px 0;
            }
        """)
        layout.addWidget(version_label)
    
    def on_new_project(self):
        """新建项目"""
        print("点击了新建项目按钮")
        # 这里可以添加新建项目的逻辑
    
    def on_open_project(self):
        """打开项目"""
        print("点击了打开项目按钮")
        # 这里可以添加打开项目的逻辑
    
    def on_settings(self):
        """设置"""
        print("点击了设置按钮")
        # 这里可以添加设置的逻辑


class SimpleApplication:
    """简化的应用程序类"""
    
    def __init__(self):
        self.app: Optional[QApplication] = None
        self.main_window: Optional[SimpleMainWindow] = None
        self.logger = None
    
    def create_qt_application(self) -> QApplication:
        """创建Qt应用程序"""
        # 设置应用程序属性
        QApplication.setApplicationName("AI视频生成器")
        QApplication.setApplicationVersion("2.0.0")
        QApplication.setOrganizationName("AI Video Generator")
        
        # 创建应用程序实例
        app = QApplication(sys.argv)
        
        # 设置应用程序图标（如果存在）
        icon_path = project_root / "resources" / "icons" / "app_icon.png"
        if icon_path.exists():
            app.setWindowIcon(QIcon(str(icon_path)))
        
        return app
    
    def run(self) -> int:
        """运行应用程序"""
        try:
            # 配置日志
            configure_logging(
                level=LogLevel.INFO,
                console_enabled=True,
                file_enabled=True,
                log_dir=Path("logs")
            )
            self.logger = get_logger(__name__)
            
            print("正在启动AI视频生成器 2.0...")
            
            # 创建Qt应用程序
            self.app = self.create_qt_application()
            
            # 创建主窗口
            print("正在创建主窗口...")
            self.main_window = SimpleMainWindow()
            
            # 显示主窗口
            print("正在显示主窗口...")
            self.main_window.show()
            
            # 启动事件循环
            self.logger.info("Application started successfully")
            print("应用程序启动成功！")
            
            return self.app.exec()
            
        except Exception as e:
            error_msg = f"Failed to start application: {e}"
            print(error_msg)
            if self.logger:
                self.logger.error(error_msg)
            return 1


def main():
    """主函数"""
    app = SimpleApplication()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())
