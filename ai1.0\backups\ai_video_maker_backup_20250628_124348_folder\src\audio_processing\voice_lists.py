# -*- coding: utf-8 -*-
"""
语音列表模块
提供Edge TTS和SiliconFlow的语音列表
"""

def get_edge_voices():
    """获取Edge TTS语音列表"""
    return [
        "zh-C<PERSON>-<PERSON><PERSON>oNeural (晓晓-女声)",
        "zh-CN-YunxiNeural (云希-男声)", 
        "zh-CN-YunyangNeural (云扬-男声)",
        "zh-CN-XiaoyiNeural (晓伊-女声)",
        "zh-CN-YunjianNeural (云健-男声)",
        "zh-CN-XiaochenNeural (晓辰-女声)",
        "zh-CN-XiaohanNeural (晓涵-女声)",
        "zh-CN-XiaomengNeural (晓梦-女声)",
        "zh-CN-XiaomoNeural (晓墨-女声)",
        "zh-CN-XiaoqiuNeural (晓秋-女声)",
        "zh-CN-XiaoruiNeural (晓睿-女声)",
        "zh-CN-XiaoshuangNeural (晓双-女声)",
        "zh-CN-Xiao<PERSON>uanNeural (晓萱-女声)",
        "zh-CN-XiaoyanNeural (晓颜-女声)",
        "zh-CN-XiaoyouNeural (晓悠-女声)",
        "zh-CN-XiaozhenNeural (晓甄-女声)",
        "zh-CN-YunfengNeural (云枫-男声)",
        "zh-CN-YunhaoNeural (云皓-男声)",
        "zh-CN-YunjieNeural (云杰-男声)",
        "zh-CN-YunxiaNeural (云夏-男声)",
        "zh-CN-YunyeNeural (云野-男声)",
        "zh-CN-YunzeNeural (云泽-男声)"
    ]

def get_siliconflow_voices():
    """获取SiliconFlow语音列表"""
    return [
        "siliconflow:zh-CN-XiaoxiaoNeural (晓晓-女声)",
        "siliconflow:zh-CN-YunxiNeural (云希-男声)",
        "siliconflow:zh-CN-YunyangNeural (云扬-男声)",
        "siliconflow:zh-CN-XiaoyiNeural (晓伊-女声)",
        "siliconflow:zh-CN-YunjianNeural (云健-男声)",
        "siliconflow:zh-CN-XiaochenNeural (晓辰-女声)",
        "siliconflow:zh-CN-XiaohanNeural (晓涵-女声)",
        "siliconflow:zh-CN-XiaomengNeural (晓梦-女声)",
        "siliconflow:zh-CN-XiaomoNeural (晓墨-女声)",
        "siliconflow:zh-CN-XiaoqiuNeural (晓秋-女声)",
        "siliconflow:zh-CN-XiaoruiNeural (晓睿-女声)",
        "siliconflow:zh-CN-XiaoshuangNeural (晓双-女声)",
        "siliconflow:zh-CN-XiaoxuanNeural (晓萱-女声)",
        "siliconflow:zh-CN-XiaoyanNeural (晓颜-女声)",
        "siliconflow:zh-CN-XiaoyouNeural (晓悠-女声)",
        "siliconflow:zh-CN-XiaozhenNeural (晓甄-女声)",
        "siliconflow:zh-CN-YunfengNeural (云枫-男声)",
        "siliconflow:zh-CN-YunhaoNeural (云皓-男声)",
        "siliconflow:zh-CN-YunjieNeural (云杰-男声)",
        "siliconflow:zh-CN-YunxiaNeural (云夏-男声)",
        "siliconflow:zh-CN-YunyeNeural (云野-男声)",
        "siliconflow:zh-CN-YunzeNeural (云泽-男声)"
    ]