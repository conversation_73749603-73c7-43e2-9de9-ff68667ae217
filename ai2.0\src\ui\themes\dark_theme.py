"""深色主题

实现深色主题的颜色方案和样式。
"""

from .base_theme import Theme, ColorScheme


class DarkTheme(Theme):
    """深色主题"""
    
    def __init__(self):
        super().__init__("dark")
    
    def _create_color_scheme(self) -> ColorScheme:
        """创建深色主题颜色方案"""
        return ColorScheme(
            # 主色调 - 深色模式下的现代配色
            primary="#58a6ff",
            secondary="#a5a5ff",
            success="#56d364",
            warning="#e3b341",
            error="#f85149",
            info="#79c0ff",

            # 背景色 - GitHub深色主题风格
            background="#0d1117",
            surface="#161b22",
            card="#21262d",

            # 文本色 - 更好的可读性
            text_primary="#f0f6fc",
            text_secondary="#8b949e",
            text_disabled="#484f58",

            # 边框色 - 更细腻的分隔
            border="#30363d",
            divider="#21262d",

            # 状态色 - 更明显的交互反馈
            hover="#262c36",
            selected="#1c2128",
            disabled="#21262d"
        )
