"""深色主题

实现深色主题的颜色方案和样式。
"""

from .base_theme import Theme, ColorScheme


class DarkTheme(Theme):
    """深色主题"""
    
    def __init__(self):
        super().__init__("dark")
    
    def _create_color_scheme(self) -> ColorScheme:
        """创建深色主题颜色方案"""
        return ColorScheme(
            # 主色调
            primary="#1976D2",
            secondary="#FFA000",
            success="#388E3C", 
            warning="#F57C00",
            error="#D32F2F",
            info="#1976D2",
            
            # 背景色
            background="#121212",
            surface="#1E1E1E",
            card="#2D2D2D",
            
            # 文本色
            text_primary="#FFFFFF",
            text_secondary="#B0B0B0",
            text_disabled="#666666",
            
            # 边框色
            border="#404040",
            divider="#404040",
            
            # 状态色
            hover="#333333",
            selected="#1565C0",
            disabled="#2D2D2D"
        )
