"""异步任务队列

提供高性能的异步任务处理队列，支持优先级、重试和并发控制。
"""

import asyncio
import time
import uuid
from typing import Any, Callable, Optional, Dict, List, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import heapq
import weakref
from collections import defaultdict

from src.utils.logger import get_logger
from src.utils.performance import performance_monitor


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 3
    NORMAL = 2
    HIGH = 1
    URGENT = 0


@dataclass
class TaskResult:
    """任务结果"""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[Exception] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    duration: Optional[float] = None
    retry_count: int = 0
    
    @property
    def success(self) -> bool:
        """是否成功"""
        return self.status == TaskStatus.COMPLETED
    
    @property
    def duration_ms(self) -> Optional[float]:
        """持续时间（毫秒）"""
        return self.duration * 1000 if self.duration else None


@dataclass
class Task:
    """异步任务"""
    id: str
    func: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    max_retries: int = 0
    retry_delay: float = 1.0
    timeout: Optional[float] = None
    callback: Optional[Callable] = None
    created_at: float = field(default_factory=time.time)
    
    # 内部状态
    status: TaskStatus = TaskStatus.PENDING
    retry_count: int = 0
    last_error: Optional[Exception] = None
    
    def __lt__(self, other):
        """用于优先级队列排序"""
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value
        return self.created_at < other.created_at


class AsyncTaskQueue:
    """异步任务队列"""
    
    def __init__(
        self,
        max_workers: int = 10,
        max_queue_size: int = 1000,
        default_timeout: float = 300.0
    ):
        self.max_workers = max_workers
        self.max_queue_size = max_queue_size
        self.default_timeout = default_timeout
        
        # 任务队列（优先级队列）
        self._queue: List[Task] = []
        self._queue_lock = asyncio.Lock()
        
        # 任务管理
        self._tasks: Dict[str, Task] = {}
        self._results: Dict[str, TaskResult] = {}
        self._running_tasks: Dict[str, asyncio.Task] = {}
        
        # 工作器管理
        self._workers: List[asyncio.Task] = []
        self._worker_semaphore = asyncio.Semaphore(max_workers)
        self._shutdown_event = asyncio.Event()
        
        # 统计信息
        self._stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "cancelled_tasks": 0,
            "retried_tasks": 0
        }
        
        # 回调管理
        self._callbacks: Dict[str, List[Callable]] = defaultdict(list)
        
        self.logger = get_logger(__name__)
        self._started = False
    
    async def start(self) -> None:
        """启动任务队列"""
        if self._started:
            return
        
        self._started = True
        self._shutdown_event.clear()
        
        # 启动工作器
        for i in range(self.max_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self._workers.append(worker)
        
        self.logger.info(f"Started async task queue with {self.max_workers} workers")
    
    async def stop(self, timeout: float = 30.0) -> None:
        """停止任务队列"""
        if not self._started:
            return
        
        self.logger.info("Stopping async task queue...")
        
        # 设置停止事件
        self._shutdown_event.set()
        
        # 等待所有工作器完成
        try:
            await asyncio.wait_for(
                asyncio.gather(*self._workers, return_exceptions=True),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            self.logger.warning("Timeout waiting for workers to stop, cancelling...")
            for worker in self._workers:
                worker.cancel()
        
        # 取消所有运行中的任务
        for task in self._running_tasks.values():
            task.cancel()
        
        self._workers.clear()
        self._running_tasks.clear()
        self._started = False
        
        self.logger.info("Async task queue stopped")
    
    async def submit(
        self,
        func: Callable,
        *args,
        priority: TaskPriority = TaskPriority.NORMAL,
        max_retries: int = 0,
        retry_delay: float = 1.0,
        timeout: Optional[float] = None,
        callback: Optional[Callable] = None,
        **kwargs
    ) -> str:
        """提交任务"""
        if not self._started:
            await self.start()
        
        # 检查队列大小
        if len(self._queue) >= self.max_queue_size:
            raise RuntimeError(f"Task queue is full (max size: {self.max_queue_size})")
        
        # 创建任务
        task_id = str(uuid.uuid4())
        task = Task(
            id=task_id,
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority,
            max_retries=max_retries,
            retry_delay=retry_delay,
            timeout=timeout or self.default_timeout,
            callback=callback
        )
        
        # 添加到队列
        async with self._queue_lock:
            heapq.heappush(self._queue, task)
            self._tasks[task_id] = task
        
        self._stats["total_tasks"] += 1
        self.logger.debug(f"Submitted task {task_id} with priority {priority.name}")
        
        return task_id
    
    async def get_result(self, task_id: str, timeout: Optional[float] = None) -> TaskResult:
        """获取任务结果"""
        start_time = time.time()
        
        while True:
            if task_id in self._results:
                return self._results[task_id]
            
            if timeout and (time.time() - start_time) > timeout:
                raise asyncio.TimeoutError(f"Timeout waiting for task {task_id}")
            
            await asyncio.sleep(0.1)
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        # 如果任务正在运行，取消它
        if task_id in self._running_tasks:
            self._running_tasks[task_id].cancel()
            return True
        
        # 如果任务在队列中，标记为取消
        if task_id in self._tasks:
            task = self._tasks[task_id]
            task.status = TaskStatus.CANCELLED
            
            result = TaskResult(
                task_id=task_id,
                status=TaskStatus.CANCELLED
            )
            self._results[task_id] = result
            self._stats["cancelled_tasks"] += 1
            
            return True
        
        return False
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        if task_id in self._results:
            return self._results[task_id].status
        elif task_id in self._tasks:
            return self._tasks[task_id].status
        return None
    
    def get_queue_stats(self) -> Dict[str, Any]:
        """获取队列统计信息"""
        queue_size = len(self._queue)
        running_count = len(self._running_tasks)
        
        # 按优先级统计队列
        priority_counts = defaultdict(int)
        for task in self._queue:
            priority_counts[task.priority.name] += 1
        
        return {
            "queue_size": queue_size,
            "running_tasks": running_count,
            "max_workers": self.max_workers,
            "max_queue_size": self.max_queue_size,
            "priority_distribution": dict(priority_counts),
            "statistics": self._stats.copy()
        }
    
    async def _worker(self, worker_name: str) -> None:
        """工作器协程"""
        self.logger.debug(f"Worker {worker_name} started")
        
        while not self._shutdown_event.is_set():
            try:
                # 获取任务
                task = await self._get_next_task()
                if task is None:
                    await asyncio.sleep(0.1)
                    continue
                
                # 执行任务
                await self._execute_task(task, worker_name)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Worker {worker_name} error: {e}")
                await asyncio.sleep(1.0)
        
        self.logger.debug(f"Worker {worker_name} stopped")
    
    async def _get_next_task(self) -> Optional[Task]:
        """获取下一个任务"""
        async with self._queue_lock:
            while self._queue:
                task = heapq.heappop(self._queue)
                
                # 跳过已取消的任务
                if task.status == TaskStatus.CANCELLED:
                    continue
                
                return task
        
        return None
    
    @performance_monitor("task_execution")
    async def _execute_task(self, task: Task, worker_name: str) -> None:
        """执行任务"""
        task.status = TaskStatus.RUNNING
        start_time = time.time()
        
        try:
            # 创建任务协程
            if asyncio.iscoroutinefunction(task.func):
                coro = task.func(*task.args, **task.kwargs)
            else:
                # 包装同步函数
                coro = asyncio.get_event_loop().run_in_executor(
                    None, lambda: task.func(*task.args, **task.kwargs)
                )
            
            # 执行任务（带超时）
            async_task = asyncio.create_task(coro)
            self._running_tasks[task.id] = async_task
            
            try:
                result = await asyncio.wait_for(async_task, timeout=task.timeout)
                
                # 任务成功完成
                end_time = time.time()
                task_result = TaskResult(
                    task_id=task.id,
                    status=TaskStatus.COMPLETED,
                    result=result,
                    start_time=start_time,
                    end_time=end_time,
                    duration=end_time - start_time,
                    retry_count=task.retry_count
                )
                
                self._results[task.id] = task_result
                self._stats["completed_tasks"] += 1
                
                # 执行回调
                if task.callback:
                    try:
                        if asyncio.iscoroutinefunction(task.callback):
                            await task.callback(task_result)
                        else:
                            task.callback(task_result)
                    except Exception as e:
                        self.logger.error(f"Task callback error: {e}")
                
                self.logger.debug(f"Task {task.id} completed successfully in {worker_name}")
            
            finally:
                if task.id in self._running_tasks:
                    del self._running_tasks[task.id]
        
        except asyncio.CancelledError:
            # 任务被取消
            task_result = TaskResult(
                task_id=task.id,
                status=TaskStatus.CANCELLED,
                start_time=start_time,
                end_time=time.time()
            )
            self._results[task.id] = task_result
            self._stats["cancelled_tasks"] += 1
            
            self.logger.debug(f"Task {task.id} was cancelled in {worker_name}")
        
        except Exception as e:
            # 任务执行失败
            task.last_error = e
            
            # 检查是否需要重试
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.RETRYING
                self._stats["retried_tasks"] += 1
                
                self.logger.warning(
                    f"Task {task.id} failed (attempt {task.retry_count}/{task.max_retries + 1}), "
                    f"retrying in {task.retry_delay}s: {e}"
                )
                
                # 延迟后重新加入队列
                await asyncio.sleep(task.retry_delay)
                async with self._queue_lock:
                    task.status = TaskStatus.PENDING
                    heapq.heappush(self._queue, task)
            else:
                # 重试次数用完，标记为失败
                end_time = time.time()
                task_result = TaskResult(
                    task_id=task.id,
                    status=TaskStatus.FAILED,
                    error=e,
                    start_time=start_time,
                    end_time=end_time,
                    duration=end_time - start_time,
                    retry_count=task.retry_count
                )
                
                self._results[task.id] = task_result
                self._stats["failed_tasks"] += 1
                
                self.logger.error(f"Task {task.id} failed permanently in {worker_name}: {e}")
        
        finally:
            # 清理任务引用
            if task.id in self._tasks:
                del self._tasks[task.id]


# 全局任务队列实例
_global_queue: Optional[AsyncTaskQueue] = None


def get_global_queue() -> AsyncTaskQueue:
    """获取全局任务队列"""
    global _global_queue
    if _global_queue is None:
        _global_queue = AsyncTaskQueue()
    return _global_queue


# 便捷函数
async def submit_task(
    func: Callable,
    *args,
    priority: TaskPriority = TaskPriority.NORMAL,
    max_retries: int = 0,
    timeout: Optional[float] = None,
    **kwargs
) -> str:
    """提交任务到全局队列"""
    queue = get_global_queue()
    return await queue.submit(
        func, *args,
        priority=priority,
        max_retries=max_retries,
        timeout=timeout,
        **kwargs
    )


async def wait_for_task(task_id: str, timeout: Optional[float] = None) -> TaskResult:
    """等待任务完成"""
    queue = get_global_queue()
    return await queue.get_result(task_id, timeout)


def task_async(
    priority: TaskPriority = TaskPriority.NORMAL,
    max_retries: int = 0,
    timeout: Optional[float] = None
):
    """异步任务装饰器"""
    def decorator(func: Callable) -> Callable:
        async def wrapper(*args, **kwargs):
            task_id = await submit_task(
                func, *args,
                priority=priority,
                max_retries=max_retries,
                timeout=timeout,
                **kwargs
            )
            result = await wait_for_task(task_id)
            
            if result.success:
                return result.result
            else:
                raise result.error or RuntimeError(f"Task {task_id} failed")
        
        return wrapper
    return decorator
