"""图像生成服务

实现各种图像生成服务的接口。
"""

from typing import Dict, Any, Optional
import aiohttp
import aiofiles
from pathlib import Path
import uuid
from datetime import datetime

from src.services.base import ImageGenerationService, ServiceConfig, ServiceResponse
from src.core.exceptions import NetworkError, APIKeyInvalidError, RateLimitExceededError


class PollinationsImageService(ImageGenerationService):
    """Pollinations图像生成服务"""
    
    def __init__(self, config: ServiceConfig):
        super().__init__(config)
        self.base_url = config.base_url or "https://image.pollinations.ai/prompt"
        self.output_dir = Path("generated/images")
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            # 发送一个简单的请求来检查服务状态
            test_url = f"{self.base_url}/test?width=64&height=64"
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    test_url,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        self._set_status(ServiceStatus.AVAILABLE)
                        return True
                    else:
                        self._set_status(ServiceStatus.UNAVAILABLE, f"HTTP {response.status}")
                        return False
        except Exception as e:
            self._set_status(ServiceStatus.ERROR, str(e))
            return False
    
    async def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            "provider": "pollinations",
            "base_url": self.base_url,
            "status": self.status.value,
            "features": ["text_to_image", "free_service"]
        }
    
    async def generate(self, prompt: str, **kwargs) -> ServiceResponse[str]:
        """生成内容"""
        width = kwargs.get("width", 1024)
        height = kwargs.get("height", 1024)
        return await self.generate_image(prompt, width, height, **kwargs)
    
    async def validate_prompt(self, prompt: str) -> bool:
        """验证提示词"""
        if not prompt or not prompt.strip():
            return False
        if len(prompt) > 2000:  # Pollinations的大致限制
            return False
        return True
    
    async def generate_image(
        self, 
        prompt: str, 
        width: int = 1024, 
        height: int = 1024,
        **kwargs
    ) -> ServiceResponse[str]:
        """生成图像"""
        try:
            if not await self.validate_prompt(prompt):
                return ServiceResponse.error_response("Invalid prompt")
            
            # 构建请求URL
            params = {
                "width": width,
                "height": height,
                "seed": kwargs.get("seed", -1),
                "model": kwargs.get("model", "flux"),
                "nologo": "true"
            }
            
            # URL编码提示词
            import urllib.parse
            encoded_prompt = urllib.parse.quote(prompt)
            url = f"{self.base_url}/{encoded_prompt}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url,
                    params=params,
                    timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                ) as response:
                    if response.status == 200:
                        # 生成唯一文件名
                        file_id = str(uuid.uuid4())
                        file_name = f"{file_id}.png"
                        file_path = self.output_dir / file_name
                        
                        # 保存图像
                        async with aiofiles.open(file_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                await f.write(chunk)
                        
                        metadata = {
                            "width": width,
                            "height": height,
                            "prompt": prompt,
                            "model": params.get("model"),
                            "file_size": file_path.stat().st_size,
                            "generated_at": datetime.utcnow().isoformat()
                        }
                        
                        return ServiceResponse.success_response(str(file_path), metadata)
                    
                    elif response.status == 429:
                        raise RateLimitExceededError("Pollinations rate limit exceeded")
                    
                    else:
                        error_msg = f"HTTP {response.status}"
                        return ServiceResponse.error_response(error_msg, str(response.status))
        
        except RateLimitExceededError:
            raise
        except Exception as e:
            self.logger.error(f"Pollinations image generation failed: {e}")
            return ServiceResponse.error_response(str(e))
    
    async def edit_image(
        self, 
        image_path: str, 
        prompt: str, 
        **kwargs
    ) -> ServiceResponse[str]:
        """编辑图像"""
        # Pollinations不直接支持图像编辑，返回错误
        return ServiceResponse.error_response("Image editing not supported by Pollinations")
    
    async def upscale_image(
        self, 
        image_path: str, 
        scale_factor: int = 2
    ) -> ServiceResponse[str]:
        """图像放大"""
        # Pollinations不直接支持图像放大，返回错误
        return ServiceResponse.error_response("Image upscaling not supported by Pollinations")


class OpenAIImageService(ImageGenerationService):
    """OpenAI DALL-E图像生成服务"""
    
    def __init__(self, config: ServiceConfig):
        super().__init__(config)
        self.base_url = config.base_url or "https://api.openai.com/v1"
        self.model = config.custom_settings.get("model", "dall-e-3") if config.custom_settings else "dall-e-3"
        self.output_dir = Path("generated/images")
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.config.api_key}",
                    "Content-Type": "application/json"
                }
                
                async with session.get(
                    f"{self.base_url}/models",
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        self._set_status(ServiceStatus.AVAILABLE)
                        return True
                    elif response.status == 401:
                        self._set_status(ServiceStatus.ERROR, "Invalid API key")
                        return False
                    else:
                        self._set_status(ServiceStatus.UNAVAILABLE, f"HTTP {response.status}")
                        return False
        except Exception as e:
            self._set_status(ServiceStatus.ERROR, str(e))
            return False
    
    async def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            "provider": "openai",
            "model": self.model,
            "base_url": self.base_url,
            "status": self.status.value,
            "features": ["text_to_image", "image_editing", "image_variations"]
        }
    
    async def generate(self, prompt: str, **kwargs) -> ServiceResponse[str]:
        """生成内容"""
        width = kwargs.get("width", 1024)
        height = kwargs.get("height", 1024)
        return await self.generate_image(prompt, width, height, **kwargs)
    
    async def validate_prompt(self, prompt: str) -> bool:
        """验证提示词"""
        if not prompt or not prompt.strip():
            return False
        if len(prompt) > 4000:  # OpenAI DALL-E的限制
            return False
        return True
    
    async def generate_image(
        self, 
        prompt: str, 
        width: int = 1024, 
        height: int = 1024,
        **kwargs
    ) -> ServiceResponse[str]:
        """生成图像"""
        try:
            if not self.config.api_key:
                return ServiceResponse.error_response("API key not configured")
            
            if not await self.validate_prompt(prompt):
                return ServiceResponse.error_response("Invalid prompt")
            
            # DALL-E 3支持的尺寸
            size = f"{width}x{height}"
            if size not in ["1024x1024", "1792x1024", "1024x1792"]:
                size = "1024x1024"  # 默认尺寸
            
            payload = {
                "model": self.model,
                "prompt": prompt,
                "n": 1,
                "size": size,
                "quality": kwargs.get("quality", "standard"),
                "style": kwargs.get("style", "vivid")
            }
            
            headers = {
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/images/generations",
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                ) as response:
                    response_data = await response.json()
                    
                    if response.status == 200:
                        image_url = response_data["data"][0]["url"]
                        
                        # 下载图像
                        async with session.get(image_url) as img_response:
                            if img_response.status == 200:
                                # 生成唯一文件名
                                file_id = str(uuid.uuid4())
                                file_name = f"{file_id}.png"
                                file_path = self.output_dir / file_name
                                
                                # 保存图像
                                async with aiofiles.open(file_path, 'wb') as f:
                                    async for chunk in img_response.content.iter_chunked(8192):
                                        await f.write(chunk)
                                
                                metadata = {
                                    "model": self.model,
                                    "size": size,
                                    "prompt": prompt,
                                    "quality": payload.get("quality"),
                                    "style": payload.get("style"),
                                    "file_size": file_path.stat().st_size,
                                    "generated_at": datetime.utcnow().isoformat(),
                                    "revised_prompt": response_data["data"][0].get("revised_prompt")
                                }
                                
                                return ServiceResponse.success_response(str(file_path), metadata)
                            else:
                                return ServiceResponse.error_response("Failed to download generated image")
                    
                    elif response.status == 401:
                        raise APIKeyInvalidError("Invalid OpenAI API key", provider="openai")
                    
                    elif response.status == 429:
                        retry_after = response.headers.get("Retry-After")
                        raise RateLimitExceededError(
                            "OpenAI rate limit exceeded", 
                            retry_after=int(retry_after) if retry_after else None
                        )
                    
                    else:
                        error_msg = response_data.get("error", {}).get("message", f"HTTP {response.status}")
                        return ServiceResponse.error_response(error_msg, str(response.status))
        
        except (APIKeyInvalidError, RateLimitExceededError):
            raise
        except Exception as e:
            self.logger.error(f"OpenAI image generation failed: {e}")
            return ServiceResponse.error_response(str(e))
    
    async def edit_image(
        self, 
        image_path: str, 
        prompt: str, 
        **kwargs
    ) -> ServiceResponse[str]:
        """编辑图像"""
        # OpenAI图像编辑API实现
        return ServiceResponse.error_response("Image editing implementation pending")
    
    async def upscale_image(
        self, 
        image_path: str, 
        scale_factor: int = 2
    ) -> ServiceResponse[str]:
        """图像放大"""
        # OpenAI不直接支持图像放大
        return ServiceResponse.error_response("Image upscaling not supported by OpenAI DALL-E")
