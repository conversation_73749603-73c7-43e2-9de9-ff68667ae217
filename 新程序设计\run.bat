@echo off
chcp 65001 >nul
cd /d "%~dp0"

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    AI视频生成器 2.0                          ║
echo ║              集成MCP工具的完整AI视频生成解决方案              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 请选择运行模式:
echo 1. 快速启动 (运行示例)
echo 2. 交互模式
echo 3. 测试服务
echo 4. 查看帮助
echo 5. 退出
echo.

set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" (
    echo.
    echo 🚀 启动快速模式...
    python start.py
    goto end
)

if "%choice%"=="2" (
    echo.
    echo 🎯 启动交互模式...
    python main.py --interactive
    goto end
)

if "%choice%"=="3" (
    echo.
    echo 🧪 开始测试服务...
    python test_services.py
    goto end
)

if "%choice%"=="4" (
    echo.
    echo 📖 显示帮助信息...
    python main.py --help
    goto end
)

if "%choice%"=="5" (
    echo.
    echo 👋 再见!
    goto end
)

echo.
echo ❌ 无效选择，请重新运行脚本

:end
echo.
echo 按任意键退出...
pause >nul