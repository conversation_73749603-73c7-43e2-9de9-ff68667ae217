"""服务配置管理器

管理AI服务的配置、API密钥和服务选择。
"""

from typing import Dict, Any, Optional, List
import json
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime

from src.services.registry import ServiceRegistry, ServiceType, get_service_registry
from src.services.base import BaseService
from src.core.config import ConfigManager
from src.utils.logger import get_logger


@dataclass
class ServiceProfile:
    """服务配置档案"""
    name: str
    service_type: str
    enabled: bool = True
    config: Dict[str, Any] = None
    priority: int = 0
    created_at: str = ""
    updated_at: str = ""
    
    def __post_init__(self):
        if self.config is None:
            self.config = {}
        if not self.created_at:
            self.created_at = datetime.utcnow().isoformat()
        self.updated_at = datetime.utcnow().isoformat()


class ServiceConfigManager:
    """服务配置管理器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.registry = get_service_registry()
        self.logger = get_logger(__name__)
        
        # 配置文件路径
        self.config_dir = Path("config/services")
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.profiles_file = self.config_dir / "service_profiles.json"
        
        # 服务档案
        self._profiles: Dict[str, ServiceProfile] = {}
        self._active_services: Dict[ServiceType, str] = {}
        
        # 加载配置
        self._load_profiles()
        self._load_default_profiles()
    
    def _load_profiles(self) -> None:
        """加载服务档案"""
        if self.profiles_file.exists():
            try:
                with open(self.profiles_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for profile_data in data.get("profiles", []):
                    profile = ServiceProfile(**profile_data)
                    self._profiles[profile.name] = profile
                
                self._active_services = data.get("active_services", {})
                # 转换字符串键为枚举
                self._active_services = {
                    ServiceType(k): v for k, v in self._active_services.items()
                }
                
                self.logger.info(f"Loaded {len(self._profiles)} service profiles")
                
            except Exception as e:
                self.logger.error(f"Failed to load service profiles: {e}")
    
    def _load_default_profiles(self) -> None:
        """加载默认服务档案"""
        # 为所有注册的服务创建默认档案
        for registration in self.registry.get_available_services():
            if registration.name not in self._profiles:
                profile = ServiceProfile(
                    name=registration.name,
                    service_type=registration.service_type.value,
                    enabled=False,  # 默认禁用，需要用户配置
                    priority=registration.priority
                )
                self._profiles[registration.name] = profile
        
        # 设置默认活动服务（优先级最高的免费服务）
        for service_type in ServiceType:
            if service_type not in self._active_services:
                available_services = self.registry.get_available_services(service_type)
                if available_services:
                    # 选择优先级最高的服务
                    default_service = available_services[0]
                    self._active_services[service_type] = default_service.name
    
    def save_profiles(self) -> None:
        """保存服务档案"""
        try:
            data = {
                "profiles": [asdict(profile) for profile in self._profiles.values()],
                "active_services": {k.value: v for k, v in self._active_services.items()},
                "updated_at": datetime.utcnow().isoformat()
            }
            
            with open(self.profiles_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.info("Service profiles saved")
            
        except Exception as e:
            self.logger.error(f"Failed to save service profiles: {e}")
    
    def get_profile(self, service_name: str) -> Optional[ServiceProfile]:
        """获取服务档案"""
        return self._profiles.get(service_name)
    
    def get_profiles_by_type(self, service_type: ServiceType) -> List[ServiceProfile]:
        """按类型获取服务档案"""
        return [
            profile for profile in self._profiles.values()
            if profile.service_type == service_type.value
        ]
    
    def update_profile(self, service_name: str, config: Dict[str, Any], enabled: bool = True) -> bool:
        """更新服务档案"""
        if service_name not in self._profiles:
            self.logger.error(f"Service profile not found: {service_name}")
            return False
        
        profile = self._profiles[service_name]
        profile.config.update(config)
        profile.enabled = enabled
        profile.updated_at = datetime.utcnow().isoformat()
        
        self.save_profiles()
        self.logger.info(f"Updated profile for service: {service_name}")
        return True
    
    def set_active_service(self, service_type: ServiceType, service_name: str) -> bool:
        """设置活动服务"""
        if service_name not in self._profiles:
            self.logger.error(f"Service not found: {service_name}")
            return False
        
        profile = self._profiles[service_name]
        if profile.service_type != service_type.value:
            self.logger.error(f"Service type mismatch: {service_name}")
            return False
        
        self._active_services[service_type] = service_name
        self.save_profiles()
        self.logger.info(f"Set active {service_type.value} service: {service_name}")
        return True
    
    def get_active_service(self, service_type: ServiceType) -> Optional[str]:
        """获取活动服务名称"""
        return self._active_services.get(service_type)
    
    async def create_active_service_instance(self, service_type: ServiceType) -> Optional[BaseService]:
        """创建活动服务实例"""
        service_name = self.get_active_service(service_type)
        if not service_name:
            self.logger.warning(f"No active service for type: {service_type.value}")
            return None
        
        profile = self.get_profile(service_name)
        if not profile or not profile.enabled:
            self.logger.warning(f"Service not enabled: {service_name}")
            return None
        
        # 创建服务实例
        service = await self.registry.create_service(service_name, profile.config)
        if service:
            # 初始化服务
            await self.registry.initialize_service(service_name)
        
        return service
    
    def validate_service_config(self, service_name: str, config: Dict[str, Any]) -> List[str]:
        """验证服务配置"""
        errors = []
        
        registration = self.registry.get_service_registration(service_name)
        if not registration:
            errors.append(f"Service not found: {service_name}")
            return errors
        
        # 验证必需字段
        for field_name, field_schema in registration.config_schema.items():
            if field_schema.get("required", False) and field_name not in config:
                errors.append(f"Required field missing: {field_name}")
            
            if field_name in config:
                value = config[field_name]
                expected_type = field_schema.get("type")
                
                if expected_type == "string" and not isinstance(value, str):
                    errors.append(f"Field {field_name} must be string")
                elif expected_type == "integer" and not isinstance(value, int):
                    errors.append(f"Field {field_name} must be integer")
                elif expected_type == "boolean" and not isinstance(value, bool):
                    errors.append(f"Field {field_name} must be boolean")
        
        return errors
    
    def get_service_config_schema(self, service_name: str) -> Optional[Dict[str, Any]]:
        """获取服务配置模式"""
        registration = self.registry.get_service_registration(service_name)
        return registration.config_schema if registration else None
    
    def get_available_services_summary(self) -> Dict[str, Any]:
        """获取可用服务摘要"""
        summary = {
            "service_types": {},
            "total_services": len(self._profiles),
            "enabled_services": 0,
            "configured_services": 0
        }
        
        for service_type in ServiceType:
            type_summary = {
                "active_service": self.get_active_service(service_type),
                "available_services": [],
                "enabled_count": 0
            }
            
            profiles = self.get_profiles_by_type(service_type)
            for profile in profiles:
                service_info = {
                    "name": profile.name,
                    "enabled": profile.enabled,
                    "configured": bool(profile.config),
                    "priority": profile.priority
                }
                type_summary["available_services"].append(service_info)
                
                if profile.enabled:
                    type_summary["enabled_count"] += 1
                    summary["enabled_services"] += 1
                
                if profile.config:
                    summary["configured_services"] += 1
            
            # 按优先级排序
            type_summary["available_services"].sort(key=lambda x: x["priority"], reverse=True)
            summary["service_types"][service_type.value] = type_summary
        
        return summary
    
    async def test_service_connection(self, service_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """测试服务连接"""
        result = {
            "success": False,
            "message": "",
            "details": {}
        }
        
        try:
            # 验证配置
            errors = self.validate_service_config(service_name, config)
            if errors:
                result["message"] = "Configuration validation failed"
                result["details"]["errors"] = errors
                return result
            
            # 创建临时服务实例
            service = await self.registry.create_service(service_name, config)
            if not service:
                result["message"] = "Failed to create service instance"
                return result
            
            # 执行健康检查
            is_healthy = await service.health_check()
            if is_healthy:
                result["success"] = True
                result["message"] = "Service connection successful"
                result["details"]["status"] = service.status.value
                
                # 获取服务信息
                service_info = await service.get_service_info()
                result["details"]["service_info"] = service_info
            else:
                result["message"] = "Service health check failed"
                result["details"]["status"] = service.status.value
                result["details"]["error"] = service.status_message
        
        except Exception as e:
            result["message"] = f"Service test failed: {str(e)}"
            result["details"]["exception"] = str(e)
        
        return result
    
    def export_configuration(self) -> Dict[str, Any]:
        """导出配置"""
        return {
            "profiles": {name: asdict(profile) for name, profile in self._profiles.items()},
            "active_services": {k.value: v for k, v in self._active_services.items()},
            "exported_at": datetime.utcnow().isoformat()
        }
    
    def import_configuration(self, config_data: Dict[str, Any]) -> bool:
        """导入配置"""
        try:
            # 导入档案
            if "profiles" in config_data:
                for name, profile_data in config_data["profiles"].items():
                    profile = ServiceProfile(**profile_data)
                    self._profiles[name] = profile
            
            # 导入活动服务
            if "active_services" in config_data:
                self._active_services = {
                    ServiceType(k): v for k, v in config_data["active_services"].items()
                }
            
            self.save_profiles()
            self.logger.info("Configuration imported successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to import configuration: {e}")
            return False
