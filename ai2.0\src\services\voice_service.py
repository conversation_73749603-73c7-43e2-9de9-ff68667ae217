"""
语音合成服务 - 提供TTS语音合成功能
"""
import asyncio
import logging
import os
import time
import uuid
from typing import List, Optional
from dataclasses import dataclass
from pathlib import Path
import edge_tts

logger = logging.getLogger(__name__)

@dataclass
class VoiceResult:
    """语音生成结果"""
    success: bool
    audio_path: str = ""
    text: str = ""
    duration: float = 0.0
    error_message: str = ""

class VoiceService:
    """语音合成服务"""
    
    def __init__(self):
        self.output_dir = Path("output/audio")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 支持的语音
        self.voices = {
            "中文女声": "zh-CN-XiaoxiaoNeural",
            "中文男声": "zh-CN-YunxiNeural", 
            "英文女声": "en-US-AriaNeural",
            "英文男声": "en-US-GuyNeural"
        }
        
        self.default_voice = "zh-CN-XiaoxiaoNeural"
    
    async def synthesize_speech(self, text: str, voice: str = "中文女声") -> VoiceResult:
        """合成语音"""
        try:
            start_time = time.time()
            logger.info(f"开始合成语音: {text[:30]}...")
            
            # 获取语音ID
            voice_id = self.voices.get(voice, self.default_voice)
            
            # 生成文件名
            timestamp = int(time.time() * 1000)
            filename = f"voice_{timestamp}_{uuid.uuid4().hex[:8]}.wav"
            output_path = self.output_dir / filename
            
            # 使用Edge TTS合成
            communicate = edge_tts.Communicate(text, voice_id)
            await communicate.save(str(output_path))
            
            # 计算音频时长
            duration = self._estimate_duration(text)
            
            generation_time = time.time() - start_time
            logger.info(f"语音合成成功: {output_path}, 时长: {duration:.2f}秒, 耗时: {generation_time:.2f}秒")
            
            return VoiceResult(
                success=True,
                audio_path=str(output_path),
                text=text,
                duration=duration
            )
            
        except Exception as e:
            logger.error(f"语音合成失败: {e}")
            return VoiceResult(
                success=False,
                text=text,
                error_message=str(e)
            )
    
    async def synthesize_batch(self, texts: List[str], voice: str = "中文女声") -> List[VoiceResult]:
        """批量合成语音"""
        logger.info(f"开始批量合成{len(texts)}段语音")
        
        # 串行处理避免资源冲突
        results = []
        for i, text in enumerate(texts):
            logger.info(f"合成第{i+1}/{len(texts)}段语音")
            result = await self.synthesize_speech(text, voice)
            results.append(result)
            
            # 添加短暂延迟
            await asyncio.sleep(0.5)
        
        success_count = sum(1 for r in results if r.success)
        logger.info(f"批量合成完成: {success_count}/{len(texts)} 成功")
        
        return results
    
    def _estimate_duration(self, text: str) -> float:
        """估算语音时长"""
        # 中文：约4字/秒，英文：约3词/秒
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        english_words = len([word for word in text.split() if word.isalpha()])
        other_chars = len(text) - chinese_chars - sum(len(word) for word in text.split() if word.isalpha())
        
        # 计算时长
        duration = chinese_chars / 4.0 + english_words / 3.0 + other_chars / 5.0
        
        # 最小时长1秒
        return max(1.0, duration)
    
    def get_available_voices(self) -> List[str]:
        """获取可用语音列表"""
        return list(self.voices.keys())
    
    def get_generated_audio(self) -> List[str]:
        """获取已生成的音频列表"""
        if not self.output_dir.exists():
            return []
        
        audio_files = []
        for ext in ['*.wav', '*.mp3']:
            audio_files.extend(self.output_dir.glob(ext))
        
        # 按修改时间排序
        audio_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        return [str(f) for f in audio_files]
    
    def clear_audio(self):
        """清理生成的音频"""
        try:
            for audio_file in self.get_generated_audio():
                os.remove(audio_file)
            logger.info("已清理所有生成的音频")
        except Exception as e:
            logger.error(f"清理音频失败: {e}")

# 全局语音服务实例
voice_service = VoiceService()
