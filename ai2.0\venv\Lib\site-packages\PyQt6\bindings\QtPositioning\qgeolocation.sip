// qgeolocation.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QGeoLocation
{
%TypeHeaderCode
#include <qgeolocation.h>
%End

public:
    QGeoLocation();
    QGeoLocation(const QGeoLocation &other);
    ~QGeoLocation();
    QGeoAddress address() const;
    void setAddress(const QGeoAddress &address);
    QGeoCoordinate coordinate() const;
    void setCoordinate(const QGeoCoordinate &position);
    bool isEmpty() const;
    QVariantMap extendedAttributes() const;
    void setExtendedAttributes(const QVariantMap &data);
    void swap(QGeoLocation &other /Constrained/);
    QGeoShape boundingShape() const;
    void setBoundingShape(const QGeoShape &shape);
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

%End
%If (Qt_6_2_0 -)
bool operator==(const QGeoLocation &lhs, const QGeoLocation &rhs);
%End
%If (Qt_6_2_0 -)
bool operator!=(const QGeoLocation &lhs, const QGeoLocation &rhs);
%End
