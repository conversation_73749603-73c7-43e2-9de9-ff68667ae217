# Include production dependencies
-r requirements.txt

# Testing
pytest>=7.0
pytest-cov>=4.0
pytest-asyncio>=0.21
pytest-qt>=4.2.0
pytest-mock>=3.10.0
factory-boy>=3.2.0

# Code quality
black>=23.0
isort>=5.0
flake8>=6.0
flake8-docstrings>=1.7.0
mypy>=1.0
pre-commit>=3.0

# Development tools
ipython>=8.0.0
jupyter>=1.0.0

# Documentation
sphinx>=6.0
sphinx-rtd-theme>=1.0
myst-parser>=1.0
sphinx-autodoc-typehints>=1.23.0

# Debugging
pdb++>=0.10.0
icecream>=2.1.0

# Performance profiling
memory-profiler>=0.60.0
line-profiler>=4.0.0

# Build tools
build>=0.10.0
twine>=4.0.0
