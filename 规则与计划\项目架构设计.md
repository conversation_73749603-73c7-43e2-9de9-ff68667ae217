# AI视频生成器 2.0 项目架构设计

## 1. 架构概览

### 1.1 设计原则
- **模块化**: 高内聚、低耦合的模块设计
- **可扩展性**: 支持插件和新功能的无缝集成
- **可维护性**: 清晰的代码结构和完善的文档
- **高性能**: 异步处理和智能缓存机制
- **用户友好**: 直观的界面和流畅的用户体验

### 1.2 整体架构图
```
┌─────────────────────────────────────────────────────────────────────┐
│                           用户界面层 (UI Layer)                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐    │
│  │  主窗口界面  │ │  分镜编辑器  │ │  媒体预览器  │ │  设置面板   │    │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘    │
├─────────────────────────────────────────────────────────────────────┤
│                          控制器层 (Controller Layer)                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐    │
│  │  应用控制器  │ │  项目控制器  │ │  媒体控制器  │ │  工作流控制器│    │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘    │
├─────────────────────────────────────────────────────────────────────┤
│                          业务逻辑层 (Business Layer)                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐    │
│  │  分镜生成器  │ │  图像处理器  │ │  视频合成器  │ │  一致性管理器│    │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘    │
├─────────────────────────────────────────────────────────────────────┤
│                           服务层 (Service Layer)                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐    │
│  │   LLM服务   │ │  图像生成服务│ │  语音合成服务│ │  视频生成服务│    │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘    │
├─────────────────────────────────────────────────────────────────────┤
│                          数据访问层 (Data Layer)                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐    │
│  │  项目仓库   │ │  媒体仓库   │ │  缓存仓库   │ │  配置仓库   │    │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘    │
├─────────────────────────────────────────────────────────────────────┤
│                         基础设施层 (Infrastructure)                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐    │
│  │  数据库引擎  │ │  缓存系统   │ │  文件系统   │ │  网络客户端  │    │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘    │
└─────────────────────────────────────────────────────────────────────┘
```

## 2. 详细架构设计

### 2.1 用户界面层 (UI Layer)

#### 2.1.1 主窗口架构
```python
# 主窗口采用现代化设计
class MainWindow(QMainWindow):
    """
    主窗口架构:
    ┌─────────────────────────────────────────────────────┐
    │  标题栏 (自定义标题栏，支持主题切换)                    │
    ├─────────────────────────────────────────────────────┤
    │  工具栏 (快速操作按钮)                               │
    ├──────────┬─────────────────────────┬────────────────┤
    │          │                         │                │
    │  侧边栏   │      主工作区域          │   属性面板     │
    │          │                         │                │
    │  - 项目   │  ┌─────────────────────┐ │  - 项目信息    │
    │  - 分镜   │  │                     │ │  - 当前选择    │
    │  - 媒体   │  │    内容视图区域      │ │  - 属性编辑    │
    │  - 设置   │  │                     │ │  - 预览窗口    │
    │          │  └─────────────────────┘ │                │
    ├──────────┴─────────────────────────┴────────────────┤
    │  状态栏 (进度显示、系统状态、快捷信息)                  │
    └─────────────────────────────────────────────────────┘
    """
```

#### 2.1.2 视图组件架构
```python
# 视图组件层次结构
ui/
├── views/                    # 主要视图
│   ├── storyboard_view.py    # 分镜视图
│   ├── media_library_view.py # 媒体库视图
│   ├── timeline_view.py      # 时间轴视图
│   └── export_view.py        # 导出视图
├── components/               # 可复用组件
│   ├── cards/               # 卡片组件
│   │   ├── shot_card.py     # 分镜卡片
│   │   ├── media_card.py    # 媒体卡片
│   │   └── project_card.py  # 项目卡片
│   ├── dialogs/             # 对话框组件
│   │   ├── project_dialog.py
│   │   ├── settings_dialog.py
│   │   └── export_dialog.py
│   └── widgets/             # 自定义控件
│       ├── progress_widget.py
│       ├── preview_widget.py
│       └── property_editor.py
└── themes/                  # 主题系统
    ├── theme_manager.py     # 主题管理器
    ├── dark_theme.py        # 暗色主题
    ├── light_theme.py       # 亮色主题
    └── custom_theme.py      # 自定义主题
```

### 2.2 控制器层 (Controller Layer)

#### 2.2.1 应用控制器
```python
class ApplicationController:
    """
    应用程序主控制器
    负责:
    - 应用程序生命周期管理
    - 全局状态管理
    - 服务协调
    - 事件分发
    """
    
    def __init__(self):
        self.service_registry = ServiceRegistry()
        self.event_bus = EventBus()
        self.config_manager = ConfigManager()
        self.plugin_manager = PluginManager()
    
    async def initialize(self):
        """初始化应用程序"""
        await self.service_registry.initialize_all_services()
        await self.plugin_manager.load_plugins()
        self.event_bus.start()
    
    async def shutdown(self):
        """关闭应用程序"""
        await self.service_registry.shutdown_all_services()
        self.event_bus.stop()
```

#### 2.2.2 项目控制器
```python
class ProjectController:
    """
    项目管理控制器
    负责:
    - 项目CRUD操作
    - 项目状态管理
    - 项目数据同步
    """
    
    def __init__(self, project_service: ProjectService):
        self.project_service = project_service
        self.current_project: Optional[Project] = None
    
    async def create_project(self, project_data: ProjectCreateRequest) -> Project:
        """创建新项目"""
        project = await self.project_service.create_project(project_data)
        self.current_project = project
        return project
    
    async def load_project(self, project_id: UUID) -> Project:
        """加载项目"""
        project = await self.project_service.get_project(project_id)
        self.current_project = project
        return project
```

### 2.3 业务逻辑层 (Business Layer)

#### 2.3.1 分镜生成器
```python
class StoryboardGenerator:
    """
    分镜生成器
    负责:
    - 文本解析和理解
    - 分镜结构生成
    - 场景和角色提取
    - 一致性检查
    """
    
    def __init__(self, llm_service: LLMService, consistency_manager: ConsistencyManager):
        self.llm_service = llm_service
        self.consistency_manager = consistency_manager
    
    async def generate_from_text(self, text: str, style: str) -> Storyboard:
        """从文本生成分镜"""
        # 1. 文本预处理
        processed_text = await self._preprocess_text(text)
        
        # 2. LLM分析生成
        raw_storyboard = await self.llm_service.generate_storyboard(
            processed_text, style
        )
        
        # 3. 一致性检查和优化
        optimized_storyboard = await self.consistency_manager.optimize_storyboard(
            raw_storyboard
        )
        
        return optimized_storyboard
```

#### 2.3.2 媒体处理器
```python
class MediaProcessor:
    """
    媒体处理器
    负责:
    - 图像生成和处理
    - 视频生成和合成
    - 音频处理和同步
    - 格式转换
    """
    
    def __init__(self, 
                 image_service: ImageService,
                 video_service: VideoService,
                 audio_service: AudioService):
        self.image_service = image_service
        self.video_service = video_service
        self.audio_service = audio_service
    
    async def process_storyboard(self, storyboard: Storyboard) -> ProcessedStoryboard:
        """处理分镜生成媒体"""
        tasks = []
        
        # 并行生成图像
        for shot in storyboard.shots:
            task = self.image_service.generate_image(
                shot.image_prompt, 
                storyboard.style
            )
            tasks.append(task)
        
        images = await asyncio.gather(*tasks)
        
        # 生成视频
        videos = await self._generate_videos_from_images(images)
        
        # 生成音频
        audio_tracks = await self._generate_audio_tracks(storyboard)
        
        return ProcessedStoryboard(
            storyboard=storyboard,
            images=images,
            videos=videos,
            audio_tracks=audio_tracks
        )
```

### 2.4 服务层 (Service Layer)

#### 2.4.1 服务注册中心
```python
class ServiceRegistry:
    """
    服务注册中心
    负责:
    - 服务注册和发现
    - 服务生命周期管理
    - 服务依赖注入
    - 服务健康检查
    """
    
    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._service_configs: Dict[str, ServiceConfig] = {}
        self._health_checkers: Dict[str, Callable] = {}
    
    def register_service(self, name: str, service: Any, config: ServiceConfig):
        """注册服务"""
        self._services[name] = service
        self._service_configs[name] = config
    
    def get_service(self, name: str) -> Any:
        """获取服务"""
        if name not in self._services:
            raise ServiceNotFoundError(f"Service {name} not found")
        return self._services[name]
    
    async def health_check_all(self) -> Dict[str, bool]:
        """检查所有服务健康状态"""
        results = {}
        for name, checker in self._health_checkers.items():
            try:
                results[name] = await checker()
            except Exception:
                results[name] = False
        return results
```

#### 2.4.2 AI服务抽象
```python
class AIServiceBase(ABC):
    """
    AI服务基类
    定义所有AI服务的通用接口
    """
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化服务"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """健康检查"""
        pass
    
    @abstractmethod
    async def generate(self, request: GenerationRequest) -> GenerationResult:
        """生成内容"""
        pass
    
    @abstractmethod
    def get_capabilities(self) -> List[str]:
        """获取服务能力"""
        pass
```

### 2.5 数据访问层 (Data Layer)

#### 2.5.1 仓库模式
```python
class Repository(ABC, Generic[T]):
    """
    仓库基类
    定义数据访问的通用接口
    """
    
    @abstractmethod
    async def create(self, entity: T) -> T:
        """创建实体"""
        pass
    
    @abstractmethod
    async def get_by_id(self, entity_id: UUID) -> Optional[T]:
        """根据ID获取实体"""
        pass
    
    @abstractmethod
    async def update(self, entity: T) -> T:
        """更新实体"""
        pass
    
    @abstractmethod
    async def delete(self, entity_id: UUID) -> bool:
        """删除实体"""
        pass
    
    @abstractmethod
    async def list(self, filters: Optional[Dict] = None) -> List[T]:
        """列出实体"""
        pass
```

#### 2.5.2 数据模型
```python
# 使用SQLAlchemy 2.0和Pydantic
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from pydantic import BaseModel

class Base(DeclarativeBase):
    pass

class ProjectORM(Base):
    __tablename__ = "projects"
    
    id: Mapped[UUID] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(255))
    description: Mapped[Optional[str]] = mapped_column(Text)
    created_at: Mapped[datetime] = mapped_column(DateTime)
    updated_at: Mapped[datetime] = mapped_column(DateTime)
    settings: Mapped[Dict] = mapped_column(JSON)

class Project(BaseModel):
    """项目数据模型"""
    id: UUID
    name: str
    description: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    settings: Dict[str, Any]
    
    class Config:
        from_attributes = True
```

## 3. 核心工作流程

### 3.1 分镜生成工作流
```mermaid
sequenceDiagram
    participant U as User
    participant UI as UI Layer
    participant C as Controller
    participant B as Business Logic
    participant S as Service Layer
    participant D as Data Layer
    
    U->>UI: 输入文本和风格
    UI->>C: 创建分镜请求
    C->>B: 调用分镜生成器
    B->>S: 请求LLM服务
    S->>S: 调用外部AI API
    S-->>B: 返回原始分镜
    B->>B: 一致性检查和优化
    B-->>C: 返回优化后分镜
    C->>D: 保存分镜数据
    D-->>C: 确认保存
    C-->>UI: 返回分镜结果
    UI-->>U: 显示分镜界面
```

### 3.2 媒体生成工作流
```mermaid
sequenceDiagram
    participant U as User
    participant UI as UI Layer
    participant C as Controller
    participant B as Business Logic
    participant S as Service Layer
    participant Cache as Cache
    
    U->>UI: 选择分镜生成媒体
    UI->>C: 媒体生成请求
    C->>B: 调用媒体处理器
    
    loop 每个镜头
        B->>Cache: 检查缓存
        alt 缓存命中
            Cache-->>B: 返回缓存结果
        else 缓存未命中
            B->>S: 请求图像生成
            S->>S: 调用AI服务
            S-->>B: 返回生成结果
            B->>Cache: 更新缓存
        end
    end
    
    B->>S: 请求视频合成
    S-->>B: 返回视频结果
    B-->>C: 返回完整媒体
    C-->>UI: 更新界面
    UI-->>U: 显示生成结果
```

## 4. 插件架构

### 4.1 插件接口
```python
class PluginInterface(ABC):
    """
    插件接口
    所有插件必须实现此接口
    """
    
    @property
    @abstractmethod
    def name(self) -> str:
        """插件名称"""
        pass
    
    @property
    @abstractmethod
    def version(self) -> str:
        """插件版本"""
        pass
    
    @abstractmethod
    async def initialize(self, app_context: ApplicationContext) -> bool:
        """初始化插件"""
        pass
    
    @abstractmethod
    async def shutdown(self) -> bool:
        """关闭插件"""
        pass
    
    @abstractmethod
    def get_capabilities(self) -> List[str]:
        """获取插件能力"""
        pass
```

### 4.2 插件类型
```python
# AI服务插件
class AIServicePlugin(PluginInterface):
    """AI服务插件"""
    
    @abstractmethod
    def create_service(self) -> AIServiceBase:
        """创建AI服务实例"""
        pass

# UI组件插件
class UIComponentPlugin(PluginInterface):
    """UI组件插件"""
    
    @abstractmethod
    def create_widget(self) -> QWidget:
        """创建UI组件"""
        pass

# 导出格式插件
class ExportFormatPlugin(PluginInterface):
    """导出格式插件"""
    
    @abstractmethod
    async def export(self, project: Project, output_path: str) -> bool:
        """导出项目"""
        pass
```

## 5. 缓存架构

### 5.1 多级缓存
```python
class CacheManager:
    """
    缓存管理器
    实现多级缓存策略
    """
    
    def __init__(self):
        self.memory_cache = MemoryCache(max_size=100)  # L1缓存
        self.redis_cache = RedisCache()                # L2缓存
        self.file_cache = FileCache()                  # L3缓存
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        # L1: 内存缓存
        if value := self.memory_cache.get(key):
            return value
        
        # L2: Redis缓存
        if value := await self.redis_cache.get(key):
            self.memory_cache.set(key, value)
            return value
        
        # L3: 文件缓存
        if value := await self.file_cache.get(key):
            await self.redis_cache.set(key, value)
            self.memory_cache.set(key, value)
            return value
        
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None):
        """设置缓存数据"""
        self.memory_cache.set(key, value, ttl)
        await self.redis_cache.set(key, value, ttl)
        await self.file_cache.set(key, value, ttl)
```

## 6. 事件系统

### 6.1 事件总线
```python
class EventBus:
    """
    事件总线
    实现发布-订阅模式
    """
    
    def __init__(self):
        self._subscribers: Dict[str, List[Callable]] = defaultdict(list)
        self._running = False
        self._event_queue = asyncio.Queue()
    
    def subscribe(self, event_type: str, handler: Callable):
        """订阅事件"""
        self._subscribers[event_type].append(handler)
    
    def unsubscribe(self, event_type: str, handler: Callable):
        """取消订阅"""
        if handler in self._subscribers[event_type]:
            self._subscribers[event_type].remove(handler)
    
    async def publish(self, event: Event):
        """发布事件"""
        await self._event_queue.put(event)
    
    async def _process_events(self):
        """处理事件队列"""
        while self._running:
            try:
                event = await asyncio.wait_for(
                    self._event_queue.get(), timeout=1.0
                )
                await self._handle_event(event)
            except asyncio.TimeoutError:
                continue
    
    async def _handle_event(self, event: Event):
        """处理单个事件"""
        handlers = self._subscribers.get(event.type, [])
        if handlers:
            tasks = [handler(event) for handler in handlers]
            await asyncio.gather(*tasks, return_exceptions=True)
```

## 7. 配置管理

### 7.1 配置架构
```python
class ConfigManager:
    """
    配置管理器
    支持多环境配置和动态更新
    """
    
    def __init__(self, config_dir: Path):
        self.config_dir = config_dir
        self.configs: Dict[str, Any] = {}
        self.watchers: Dict[str, FileWatcher] = {}
    
    async def load_config(self, name: str) -> Dict[str, Any]:
        """加载配置文件"""
        config_file = self.config_dir / f"{name}.yaml"
        
        if not config_file.exists():
            raise ConfigNotFoundError(f"Config file {config_file} not found")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 环境变量替换
        config = self._substitute_env_vars(config)
        
        # 配置验证
        self._validate_config(name, config)
        
        self.configs[name] = config
        
        # 设置文件监听
        if name not in self.watchers:
            self.watchers[name] = FileWatcher(
                config_file, 
                self._on_config_changed
            )
        
        return config
    
    def get_config(self, name: str, key: str = None, default: Any = None) -> Any:
        """获取配置值"""
        if name not in self.configs:
            raise ConfigNotLoadedError(f"Config {name} not loaded")
        
        config = self.configs[name]
        
        if key is None:
            return config
        
        # 支持嵌套键访问 (e.g., "database.host")
        keys = key.split('.')
        value = config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
```

## 8. 错误处理和日志

### 8.1 异常层次结构
```python
class ApplicationError(Exception):
    """应用程序基础异常"""
    
    def __init__(self, message: str, error_code: str = None, details: Dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.timestamp = datetime.utcnow()

class BusinessLogicError(ApplicationError):
    """业务逻辑异常"""
    pass

class ServiceError(ApplicationError):
    """服务异常"""
    pass

class DataAccessError(ApplicationError):
    """数据访问异常"""
    pass

class UIError(ApplicationError):
    """UI异常"""
    pass
```

### 8.2 日志系统
```python
class StructuredLogger:
    """
    结构化日志记录器
    支持JSON格式和多种输出目标
    """
    
    def __init__(self, name: str, config: LogConfig):
        self.name = name
        self.config = config
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(self.name)
        logger.setLevel(self.config.level)
        
        # 控制台处理器
        if self.config.console_enabled:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(ColoredFormatter())
            logger.addHandler(console_handler)
        
        # 文件处理器
        if self.config.file_enabled:
            file_handler = RotatingFileHandler(
                self.config.file_path,
                maxBytes=self.config.max_file_size,
                backupCount=self.config.backup_count
            )
            file_handler.setFormatter(JSONFormatter())
            logger.addHandler(file_handler)
        
        return logger
    
    def info(self, message: str, **kwargs):
        """记录信息日志"""
        self._log(logging.INFO, message, **kwargs)
    
    def error(self, message: str, exception: Exception = None, **kwargs):
        """记录错误日志"""
        if exception:
            kwargs['exception'] = {
                'type': type(exception).__name__,
                'message': str(exception),
                'traceback': traceback.format_exc()
            }
        self._log(logging.ERROR, message, **kwargs)
    
    def _log(self, level: int, message: str, **kwargs):
        """记录日志"""
        log_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': logging.getLevelName(level),
            'logger': self.name,
            'message': message,
            **kwargs
        }
        
        self.logger.log(level, json.dumps(log_data, ensure_ascii=False))
```

## 9. 性能监控

### 9.1 性能指标收集
```python
class PerformanceMonitor:
    """
    性能监控器
    收集和分析应用程序性能指标
    """
    
    def __init__(self):
        self.metrics: Dict[str, List[float]] = defaultdict(list)
        self.counters: Dict[str, int] = defaultdict(int)
        self.gauges: Dict[str, float] = {}
    
    @contextmanager
    def measure_time(self, operation: str):
        """测量操作耗时"""
        start_time = time.perf_counter()
        try:
            yield
        finally:
            end_time = time.perf_counter()
            duration = end_time - start_time
            self.metrics[f"{operation}_duration"].append(duration)
    
    def increment_counter(self, name: str, value: int = 1):
        """增加计数器"""
        self.counters[name] += value
    
    def set_gauge(self, name: str, value: float):
        """设置仪表值"""
        self.gauges[name] = value
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            'counters': dict(self.counters),
            'gauges': dict(self.gauges),
            'metrics': {}
        }
        
        for name, values in self.metrics.items():
            if values:
                stats['metrics'][name] = {
                    'count': len(values),
                    'min': min(values),
                    'max': max(values),
                    'avg': sum(values) / len(values),
                    'p95': self._percentile(values, 0.95),
                    'p99': self._percentile(values, 0.99)
                }
        
        return stats
```

## 10. 部署架构

### 10.1 单体应用部署
```
应用程序包结构:
├── ai_video_generator.exe     # 主程序
├── config/                    # 配置文件
├── plugins/                   # 插件目录
├── themes/                    # 主题文件
├── templates/                 # 模板文件
├── docs/                      # 文档
└── data/                      # 数据目录
    ├── projects/              # 项目数据
    ├── cache/                 # 缓存数据
    └── logs/                  # 日志文件
```

### 10.2 微服务部署(可选)
```
微服务架构:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用       │    │   API网关        │    │   配置中心       │
│   (PyQt6)       │◄──►│   (FastAPI)     │◄──►│   (Consul)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │   LLM服务     │ │  图像服务    │ │  视频服务   │
        │  (FastAPI)   │ │ (FastAPI)   │ │ (FastAPI)  │
        └──────────────┘ └─────────────┘ └────────────┘
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │   Redis      │ │  PostgreSQL │ │  文件存储   │
        │   (缓存)      │ │  (数据库)    │ │  (MinIO)   │
        └──────────────┘ └─────────────┘ └────────────┘
```

---

**版本**: 1.0  
**创建日期**: 2024年12月  
**最后更新**: 2024年12月  
**维护者**: AI视频生成器开发团队