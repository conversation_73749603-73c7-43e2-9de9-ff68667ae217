"""
图像生成服务 - 提供AI图像生成功能
"""
import asyncio
import logging
import os
import time
import uuid
from typing import List, Optional
from dataclasses import dataclass
import aiohttp
import aiofiles
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class ImageResult:
    """图像生成结果"""
    success: bool
    image_path: str = ""
    prompt: str = ""
    error_message: str = ""
    generation_time: float = 0.0

class ImageService:
    """图像生成服务"""
    
    def __init__(self):
        self.output_dir = Path("output/images")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Pollinations API (免费)
        self.pollinations_base_url = "https://image.pollinations.ai/prompt"
        
    async def generate_image(self, prompt: str, width: int = 1024, height: int = 1024) -> ImageResult:
        """生成单张图像"""
        try:
            start_time = time.time()
            logger.info(f"开始生成图像: {prompt[:50]}...")
            
            # 增强提示词
            enhanced_prompt = self._enhance_prompt(prompt)
            
            # 生成图像
            image_path = await self._generate_with_pollinations(enhanced_prompt, width, height)
            
            generation_time = time.time() - start_time
            
            if image_path:
                logger.info(f"图像生成成功: {image_path}, 耗时: {generation_time:.2f}秒")
                return ImageResult(
                    success=True,
                    image_path=image_path,
                    prompt=enhanced_prompt,
                    generation_time=generation_time
                )
            else:
                return ImageResult(
                    success=False,
                    error_message="图像生成失败"
                )
                
        except Exception as e:
            logger.error(f"图像生成异常: {e}")
            return ImageResult(
                success=False,
                error_message=str(e)
            )
    
    async def generate_batch_images(self, prompts: List[str], width: int = 1024, height: int = 1024) -> List[ImageResult]:
        """批量生成图像"""
        logger.info(f"开始批量生成{len(prompts)}张图像")
        
        # 并发生成，但限制并发数
        semaphore = asyncio.Semaphore(3)  # 最多3个并发
        
        async def generate_single(prompt: str) -> ImageResult:
            async with semaphore:
                result = await self.generate_image(prompt, width, height)
                # 添加延迟避免API限制
                await asyncio.sleep(1)
                return result
        
        tasks = [generate_single(prompt) for prompt in prompts]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"图像{i+1}生成异常: {result}")
                final_results.append(ImageResult(
                    success=False,
                    error_message=str(result)
                ))
            else:
                final_results.append(result)
        
        success_count = sum(1 for r in final_results if r.success)
        logger.info(f"批量生成完成: {success_count}/{len(prompts)} 成功")
        
        return final_results
    
    def _enhance_prompt(self, prompt: str) -> str:
        """增强提示词"""
        # 添加质量关键词
        quality_keywords = [
            "high quality", "detailed", "professional photography",
            "cinematic lighting", "8k resolution", "masterpiece"
        ]
        
        # 检查是否已包含质量关键词
        prompt_lower = prompt.lower()
        has_quality = any(keyword in prompt_lower for keyword in quality_keywords)
        
        if not has_quality:
            prompt += ", high quality, detailed, professional photography, cinematic lighting"
        
        return prompt
    
    async def _generate_with_pollinations(self, prompt: str, width: int, height: int) -> Optional[str]:
        """使用Pollinations生成图像"""
        try:
            # 构建URL
            url = f"{self.pollinations_base_url}/{prompt}"
            params = {
                "width": width,
                "height": height,
                "model": "flux",
                "enhance": "true"
            }
            
            # 生成文件名
            timestamp = int(time.time() * 1000)
            filename = f"image_{timestamp}_{uuid.uuid4().hex[:8]}.png"
            output_path = self.output_dir / filename
            
            # 下载图像
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        content = await response.read()
                        
                        # 保存文件
                        async with aiofiles.open(output_path, 'wb') as f:
                            await f.write(content)
                        
                        logger.info(f"图像已保存: {output_path}")
                        return str(output_path)
                    else:
                        logger.error(f"Pollinations API错误: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"Pollinations生成失败: {e}")
            return None
    
    def get_generated_images(self) -> List[str]:
        """获取已生成的图像列表"""
        if not self.output_dir.exists():
            return []
        
        image_files = []
        for ext in ['*.png', '*.jpg', '*.jpeg']:
            image_files.extend(self.output_dir.glob(ext))
        
        # 按修改时间排序
        image_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        return [str(f) for f in image_files]
    
    def clear_images(self):
        """清理生成的图像"""
        try:
            for image_file in self.get_generated_images():
                os.remove(image_file)
            logger.info("已清理所有生成的图像")
        except Exception as e:
            logger.error(f"清理图像失败: {e}")

# 全局图像服务实例
image_service = ImageService()
