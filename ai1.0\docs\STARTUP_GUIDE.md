# AI视频生成器 - 启动指南

## 🚀 程序启动方式

### 方式一：使用原始启动入口（推荐）
```bash
python main.py
```

### 方式二：使用新的测试启动入口
```bash
python test_new_ui.py
```

**两种方式都能正常启动配音驱动的AI视频生成器！**

## 🎭 新界面功能介绍

### 标签页顺序（按配音驱动工作流程设计）

#### 1. 🎭 工作流程指南
- **功能**：可视化的配音驱动工作流程指导
- **特色**：
  - 步骤式引导界面
  - 智能状态跟踪
  - 一键跳转到对应功能
  - 实时进度显示
- **使用建议**：首次使用必看

#### 2. 📝 文本创作
- **功能**：AI创作故事或改写现有文本
- **支持**：多种创作风格和模式
- **目标**：为后续配音提供高质量文本内容

#### 3. 🎵 AI配音生成
- **功能**：配音驱动工作流程的核心步骤
- **新增功能**：
  - 🎭 生成配音驱动分镜按钮
  - 智能配音段落分析
  - 配音完成状态跟踪
- **重要性**：这是新工作流程的关键步骤

#### 4. 🎭 配音驱动分镜
- **功能**：基于配音内容的五阶段分镜生成
- **革命性改进**：
  - 完全基于实际配音内容
  - 智能场景分割算法
  - 内容一致性100%保证
- **前身**：原"五阶段分镜"的升级版

#### 5. 🎨 一致性控制
- **功能**：角色和场景的一致性管理
- **作用**：确保视觉风格统一

#### 6. 🖼️ 图像生成
- **功能**：基于配音内容的图像生成
- **优化**：
  - 完全基于配音内容生成图像描述
  - 支持配音驱动的图像数量分配
  - 多引擎支持

#### 7. 🎬 视频合成
- **功能**：最终视频制作
- **支持**：配音、图像、音效的完美合成

#### 8. ⚙️ 设置
- **功能**：系统配置和AI绘图设置
- **包含**：各种引擎的参数配置

## 🎯 配音驱动工作流程使用指南

### 第一步：文本创作
1. 在"📝 文本创作"标签页创作或改写文本
2. 确保文本内容适合配音

### 第二步：AI配音生成
1. 切换到"🎵 AI配音生成"标签页
2. 导入文本内容
3. 选择配音引擎和声音
4. 生成所有配音段落
5. **重要**：点击"🎭 生成配音驱动分镜"按钮

### 第三步：配音驱动分镜
1. 系统自动切换到"🎭 配音驱动分镜"标签页
2. 查看基于配音内容生成的五阶段分镜
3. 验证内容一致性

### 第四步：一致性控制
1. 在"🎨 一致性控制"标签页管理角色和场景
2. 确保视觉风格统一

### 第五步：图像生成
1. 切换到"🖼️ 图像生成"标签页
2. 系统将基于配音内容生成图像描述
3. 选择图像生成引擎
4. 批量生成图像

### 第六步：视频合成
1. 在"🎬 视频合成"标签页
2. 合成最终视频

## 🔧 程序运行状态

### ✅ 完全正常的功能
- **模块导入**：10/10 成功
- **项目结构**：12/12 完整
- **配音驱动工作流程**：完全正常
- **UI组件**：正常运行
- **现有项目**：数据完整

### 🎮 可用的AI引擎
- **LLM服务**：Deepseek、通义千问、智谱AI、Google Gemini
- **图像生成**：Pollinations AI（免费）
- **语音服务**：Edge-TTS（免费）

### ⚠️ 注意事项
- ComfyUI本地服务未启动（正常，可选功能）
- 首次启动可能需要下载模型文件
- 建议先查看工作流程指南

## 🎊 新功能亮点

### 1. 配音驱动工作流程
- **完美内容一致性**：分镜与配音100%匹配
- **智能场景分割**：基于配音自然停顿
- **精确时长同步**：视觉与听觉完美同步

### 2. 工作流程指导系统
- **可视化步骤**：清晰的操作指引
- **智能状态跟踪**：实时显示进度
- **一键跳转**：快速切换到对应功能

### 3. 优化的用户界面
- **图标化设计**：直观的功能标识
- **逻辑化顺序**：按实际工作流程排列
- **友好提示**：详细的操作说明

## 🚀 快速开始

### 新用户推荐流程
1. **启动程序**：`python main.py`
2. **查看指南**：点击"🎭 工作流程指南"标签页
3. **创建项目**：在文件菜单中创建新项目
4. **按步骤操作**：按照标签页顺序进行操作

### 现有用户升级指南
1. **启动程序**：使用原来的`python main.py`
2. **加载项目**：打开现有项目
3. **体验新功能**：尝试配音驱动工作流程
4. **数据兼容**：现有项目数据完全兼容

## 💡 使用提示

### 最佳实践
- 首次使用请查看工作流程指南
- 按照标签页顺序操作
- 配音完成后立即生成配音驱动分镜
- 定期保存项目数据

### 故障排除
- 如果界面异常，重启程序即可
- 如果模块导入失败，检查Python环境
- 如果配音生成失败，检查网络连接

## 🎬 享受配音驱动的完美体验！

新的配音驱动工作流程将彻底解决内容不匹配问题，让您的视频制作更加高效和专业！
