// qsqlresult.sip generated by MetaSIP
//
// This file is part of the QtSql Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSqlResult /Supertype=PyQt6.sip.wrapper/
{
%TypeHeaderCode
#include <qsqlresult.h>
%End

public:
    virtual ~QSqlResult();
    virtual QVariant handle() const;

protected:
    enum BindingSyntax
    {
        PositionalBinding,
        NamedBinding,
    };

    explicit QSqlResult(const QSqlDriver *db);
    int at() const;
    QString lastQuery() const;
    QSqlError lastError() const;
    bool isValid() const;
    bool isActive() const;
    bool isSelect() const;
    bool isForwardOnly() const;
    const QSqlDriver *driver() const;
    virtual void setAt(int at);
    virtual void setActive(bool a);
    virtual void setLastError(const QSqlError &e);
    virtual void setQuery(const QString &query);
    virtual void setSelect(bool s);
    virtual void setForwardOnly(bool forward);
    virtual bool exec() /ReleaseGIL/;
    virtual bool prepare(const QString &query) /ReleaseGIL/;
    virtual bool savePrepare(const QString &sqlquery);
    virtual void bindValue(int pos, const QVariant &val, QSql::ParamType type);
    virtual void bindValue(const QString &placeholder, const QVariant &val, QSql::ParamType type);
    void addBindValue(const QVariant &val, QSql::ParamType type);
    QVariant boundValue(const QString &placeholder) const;
    QVariant boundValue(int pos) const;
    QSql::ParamType bindValueType(const QString &placeholder) const;
    QSql::ParamType bindValueType(int pos) const;
    int boundValueCount() const;
%If (Qt_6_6_0 -)
    QVariantList boundValues() const [QVariantList (Qt::Disambiguated_t = Qt::Disambiguated)];
%MethodCode
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            sipRes = new ::QVariantList(sipCpp->boundValues());
        #else
            sipRes = new ::QVariantList(sipCpp->sipProtect_boundValues(Qt::Disambiguated));
        #endif
%End

%End
%If (- Qt_6_6_0)
    QList<QVariant> &boundValues() const;
%End
    QString executedQuery() const;
    QString boundValueName(int pos) const;
    void clear();
    bool hasOutValues() const;
    QSqlResult::BindingSyntax bindingSyntax() const;
    virtual QVariant data(int i) = 0;
    virtual bool isNull(int i) = 0;
    virtual bool reset(const QString &sqlquery) = 0;
    virtual bool fetch(int i) = 0 /ReleaseGIL/;
    virtual bool fetchNext() /ReleaseGIL/;
    virtual bool fetchPrevious() /ReleaseGIL/;
    virtual bool fetchFirst() = 0 /ReleaseGIL/;
    virtual bool fetchLast() = 0 /ReleaseGIL/;
    virtual int size() = 0;
    virtual int numRowsAffected() = 0;
    virtual QSqlRecord record() const;
    virtual QVariant lastInsertId() const;
%If (Qt_6_6_0 -)
    QStringList boundValueNames() const;
%End
%If (Qt_6_7_0 -)
    void setPositionalBindingEnabled(bool enable);
%End
%If (Qt_6_7_0 -)
    bool isPositionalBindingEnabled() const;
%End

private:
    QSqlResult(const QSqlResult &);
};
