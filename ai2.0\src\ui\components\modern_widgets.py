"""现代化UI组件

提供现代化的UI组件，包括卡片、按钮、输入框等。
"""

from typing import Optional, List, Any
from PyQt6.QtWidgets import (
    QWidget, QPushButton, QLabel, QFrame, QVBoxLayout, QHBoxLayout,
    QLineEdit, QTextEdit, QScrollArea, QGraphicsDropShadowEffect,
    QSizePolicy, QSpacerItem, QProgressBar
)
from PyQt6.QtCore import Qt, QPropertyAnimation, QEasingCurve, pyqtSignal, QRect
from PyQt6.QtGui import QPainter, QPainterPath, QColor, QFont, QPixmap, QIcon

from src.ui.themes.theme_manager import get_theme_manager


class ModernCard(QFrame):
    """现代化卡片组件"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.setObjectName("modern_card")
        self._setup_ui()
        self._apply_theme()
    
    def _setup_ui(self):
        """设置UI"""
        self.setFrameStyle(QFrame.Shape.NoFrame)
        
        # 设置阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)
        
        # 设置布局
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(16, 16, 16, 16)
        self.layout.setSpacing(12)
    
    def _apply_theme(self):
        """应用主题"""
        theme = get_theme_manager().current_theme
        self.setStyleSheet(f"""
            QFrame#modern_card {{
                background-color: {theme.colors.card};
                border: 1px solid {theme.colors.border};
                border-radius: {theme.border_radius.md}px;
            }}
        """)
    
    def add_widget(self, widget: QWidget):
        """添加组件"""
        self.layout.addWidget(widget)
    
    def add_title(self, text: str) -> QLabel:
        """添加标题"""
        title = QLabel(text)
        title.setObjectName("card_title")
        title.setStyleSheet(f"""
            QLabel#card_title {{
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 8px;
            }}
        """)
        self.layout.addWidget(title)
        return title
    
    def add_content(self, text: str) -> QLabel:
        """添加内容"""
        content = QLabel(text)
        content.setWordWrap(True)
        content.setObjectName("card_content")
        self.layout.addWidget(content)
        return content


class ModernButton(QPushButton):
    """现代化按钮组件"""
    
    def __init__(self, text: str = "", button_type: str = "primary", parent: Optional[QWidget] = None):
        super().__init__(text, parent)
        self.button_type = button_type
        self._setup_ui()
        self._apply_theme()
    
    def _setup_ui(self):
        """设置UI"""
        self.setMinimumHeight(36)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 设置字体
        font = QFont()
        font.setWeight(QFont.Weight.Medium)
        self.setFont(font)
    
    def _apply_theme(self):
        """应用主题"""
        theme = get_theme_manager().current_theme
        
        if self.button_type == "primary":
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: {theme.colors.primary};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 500;
                }}
                QPushButton:hover {{
                    background-color: {theme._darken_color(theme.colors.primary, 0.1)};
                }}
                QPushButton:pressed {{
                    background-color: {theme._darken_color(theme.colors.primary, 0.2)};
                }}
            """)
        elif self.button_type == "secondary":
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: {theme.colors.surface};
                    color: {theme.colors.text_primary};
                    border: 1px solid {theme.colors.border};
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 500;
                }}
                QPushButton:hover {{
                    background-color: {theme.colors.hover};
                }}
                QPushButton:pressed {{
                    background-color: {theme.colors.selected};
                }}
            """)
        elif self.button_type == "success":
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: {theme.colors.success};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 500;
                }}
                QPushButton:hover {{
                    background-color: {theme._darken_color(theme.colors.success, 0.1)};
                }}
            """)
        elif self.button_type == "danger":
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: {theme.colors.error};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 500;
                }}
                QPushButton:hover {{
                    background-color: {theme._darken_color(theme.colors.error, 0.1)};
                }}
            """)


class ModernInput(QLineEdit):
    """现代化输入框组件"""
    
    def __init__(self, placeholder: str = "", parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.setPlaceholderText(placeholder)
        self._setup_ui()
        self._apply_theme()
    
    def _setup_ui(self):
        """设置UI"""
        self.setMinimumHeight(40)
        
        # 设置字体
        font = QFont()
        font.setPointSize(14)
        self.setFont(font)
    
    def _apply_theme(self):
        """应用主题"""
        theme = get_theme_manager().current_theme
        self.setStyleSheet(f"""
            QLineEdit {{
                background-color: {theme.colors.card};
                border: 1px solid {theme.colors.border};
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 14px;
                color: {theme.colors.text_primary};
            }}
            QLineEdit:focus {{
                border-color: {theme.colors.primary};
                outline: none;
            }}
            QLineEdit::placeholder {{
                color: {theme.colors.text_secondary};
            }}
        """)


class ModernTextArea(QTextEdit):
    """现代化文本区域组件"""
    
    def __init__(self, placeholder: str = "", parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.setPlaceholderText(placeholder)
        self._setup_ui()
        self._apply_theme()
    
    def _setup_ui(self):
        """设置UI"""
        self.setMinimumHeight(100)
        
        # 设置字体
        font = QFont()
        font.setPointSize(14)
        self.setFont(font)
    
    def _apply_theme(self):
        """应用主题"""
        theme = get_theme_manager().current_theme
        self.setStyleSheet(f"""
            QTextEdit {{
                background-color: {theme.colors.card};
                border: 1px solid {theme.colors.border};
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 14px;
                color: {theme.colors.text_primary};
            }}
            QTextEdit:focus {{
                border-color: {theme.colors.primary};
                outline: none;
            }}
        """)


class ModernProgressBar(QProgressBar):
    """现代化进度条组件"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self._setup_ui()
        self._apply_theme()
    
    def _setup_ui(self):
        """设置UI"""
        self.setMinimumHeight(8)
        self.setMaximumHeight(8)
        self.setTextVisible(False)
    
    def _apply_theme(self):
        """应用主题"""
        theme = get_theme_manager().current_theme
        self.setStyleSheet(f"""
            QProgressBar {{
                background-color: {theme.colors.surface};
                border: none;
                border-radius: 4px;
            }}
            QProgressBar::chunk {{
                background-color: {theme.colors.primary};
                border-radius: 4px;
            }}
        """)


class ModernLabel(QLabel):
    """现代化标签组件"""
    
    def __init__(self, text: str = "", label_type: str = "normal", parent: Optional[QWidget] = None):
        super().__init__(text, parent)
        self.label_type = label_type
        self._setup_ui()
        self._apply_theme()
    
    def _setup_ui(self):
        """设置UI"""
        if self.label_type == "title":
            font = QFont()
            font.setPointSize(20)
            font.setWeight(QFont.Weight.Bold)
            self.setFont(font)
        elif self.label_type == "subtitle":
            font = QFont()
            font.setPointSize(16)
            font.setWeight(QFont.Weight.Medium)
            self.setFont(font)
        elif self.label_type == "caption":
            font = QFont()
            font.setPointSize(12)
            self.setFont(font)
    
    def _apply_theme(self):
        """应用主题"""
        theme = get_theme_manager().current_theme
        
        if self.label_type == "title":
            color = theme.colors.text_primary
        elif self.label_type == "subtitle":
            color = theme.colors.text_primary
        elif self.label_type == "caption":
            color = theme.colors.text_secondary
        else:
            color = theme.colors.text_primary
        
        self.setStyleSheet(f"""
            QLabel {{
                color: {color};
                background-color: transparent;
            }}
        """)


class ModernSeparator(QFrame):
    """现代化分隔线组件"""
    
    def __init__(self, orientation: Qt.Orientation = Qt.Orientation.Horizontal, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.orientation = orientation
        self._setup_ui()
        self._apply_theme()
    
    def _setup_ui(self):
        """设置UI"""
        if self.orientation == Qt.Orientation.Horizontal:
            self.setFrameShape(QFrame.Shape.HLine)
            self.setMaximumHeight(1)
        else:
            self.setFrameShape(QFrame.Shape.VLine)
            self.setMaximumWidth(1)
        
        self.setFrameShadow(QFrame.Shadow.Plain)
    
    def _apply_theme(self):
        """应用主题"""
        theme = get_theme_manager().current_theme
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {theme.colors.divider};
                border: none;
            }}
        """)


class ModernScrollArea(QScrollArea):
    """现代化滚动区域组件"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self._setup_ui()
        self._apply_theme()
    
    def _setup_ui(self):
        """设置UI"""
        self.setWidgetResizable(True)
        self.setFrameShape(QFrame.Shape.NoFrame)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
    
    def _apply_theme(self):
        """应用主题"""
        theme = get_theme_manager().current_theme
        self.setStyleSheet(f"""
            QScrollArea {{
                background-color: transparent;
                border: none;
            }}
            QScrollBar:vertical {{
                background-color: {theme.colors.surface};
                width: 8px;
                border-radius: 4px;
                margin: 0;
            }}
            QScrollBar::handle:vertical {{
                background-color: {theme.colors.border};
                border-radius: 4px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {theme.colors.text_secondary};
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px;
            }}
            QScrollBar:horizontal {{
                background-color: {theme.colors.surface};
                height: 8px;
                border-radius: 4px;
                margin: 0;
            }}
            QScrollBar::handle:horizontal {{
                background-color: {theme.colors.border};
                border-radius: 4px;
                min-width: 20px;
            }}
            QScrollBar::handle:horizontal:hover {{
                background-color: {theme.colors.text_secondary};
            }}
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                width: 0px;
            }}
        """)


def create_spacer(width: int = 0, height: int = 0) -> QSpacerItem:
    """创建间距器"""
    if width > 0:
        return QSpacerItem(width, 0, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)
    elif height > 0:
        return QSpacerItem(0, height, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
    else:
        return QSpacerItem(0, 0, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
