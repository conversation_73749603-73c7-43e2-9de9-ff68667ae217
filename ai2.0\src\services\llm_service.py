"""
LLM服务 - 提供文本生成和分镜生成功能
"""
import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import aiohttp
import time

logger = logging.getLogger(__name__)

@dataclass
class StoryboardShot:
    """分镜镜头"""
    shot_id: int
    scene: str
    characters: List[str]
    image_prompt: str
    narration: str
    duration: float
    camera_angle: str = "中景"
    lighting: str = "自然光"

@dataclass
class StoryboardResult:
    """分镜结果"""
    success: bool
    shots: List[StoryboardShot]
    total_duration: float
    error_message: str = ""

class LLMService:
    """LLM服务类"""
    
    def __init__(self):
        self.api_key = None
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
        self.model = "glm-4-flash"
        
    def set_api_key(self, api_key: str):
        """设置API密钥"""
        self.api_key = api_key
        
    async def generate_storyboard(self, text: str, style: str = "现代") -> StoryboardResult:
        """生成分镜脚本"""
        try:
            logger.info(f"开始生成分镜脚本，文本长度: {len(text)}")
            
            # 构建提示词
            prompt = self._build_storyboard_prompt(text, style)
            
            # 调用LLM API
            response = await self._call_llm_api(prompt)
            
            if not response:
                return StoryboardResult(
                    success=False,
                    shots=[],
                    total_duration=0,
                    error_message="LLM API调用失败"
                )
            
            # 解析响应
            shots = self._parse_storyboard_response(response)
            
            total_duration = sum(shot.duration for shot in shots)
            
            logger.info(f"分镜生成完成，共{len(shots)}个镜头，总时长{total_duration}秒")
            
            return StoryboardResult(
                success=True,
                shots=shots,
                total_duration=total_duration
            )
            
        except Exception as e:
            logger.error(f"分镜生成失败: {e}")
            return StoryboardResult(
                success=False,
                shots=[],
                total_duration=0,
                error_message=str(e)
            )
    
    def _build_storyboard_prompt(self, text: str, style: str) -> str:
        """构建分镜生成提示词"""
        return f"""
请根据以下文本内容，生成详细的视频分镜脚本。

原始文本：
{text}

视频风格：{style}

请按照以下JSON格式输出分镜脚本：
{{
    "shots": [
        {{
            "shot_id": 1,
            "scene": "场景描述",
            "characters": ["角色1", "角色2"],
            "image_prompt": "详细的画面描述，用于AI图像生成",
            "narration": "这个镜头的旁白文本",
            "duration": 5.0,
            "camera_angle": "镜头角度",
            "lighting": "光线设置"
        }}
    ]
}}

要求：
1. 每个镜头时长3-8秒
2. 画面描述要详细具体，适合AI图像生成
3. 旁白要自然流畅
4. 总共生成3-6个镜头
5. 确保镜头之间有逻辑连贯性
"""

    async def _call_llm_api(self, prompt: str) -> Optional[str]:
        """调用LLM API"""
        if not self.api_key:
            # 如果没有API密钥，返回模拟数据
            logger.warning("未设置API密钥，使用模拟数据")
            return self._get_mock_response(prompt)
        
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.7,
                "max_tokens": 2000
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.base_url, headers=headers, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result["choices"][0]["message"]["content"]
                    else:
                        logger.error(f"API调用失败: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"LLM API调用异常: {e}")
            return None
    
    def _get_mock_response(self, prompt: str) -> str:
        """获取模拟响应"""
        # 从prompt中提取原始文本
        lines = prompt.split('\n')
        original_text = ""
        for i, line in enumerate(lines):
            if line.strip() == "原始文本：" and i + 1 < len(lines):
                original_text = lines[i + 1].strip()
                break
        
        # 生成模拟分镜
        mock_shots = []
        text_parts = original_text.split('。') if original_text else ["示例内容"]
        
        for i, part in enumerate(text_parts[:4]):  # 最多4个镜头
            if part.strip():
                shot = {
                    "shot_id": i + 1,
                    "scene": f"场景{i + 1}",
                    "characters": ["主角"],
                    "image_prompt": f"根据内容'{part.strip()}'生成画面，{self._get_style_description()}",
                    "narration": part.strip(),
                    "duration": 5.0,
                    "camera_angle": "中景",
                    "lighting": "自然光"
                }
                mock_shots.append(shot)
        
        return json.dumps({"shots": mock_shots}, ensure_ascii=False, indent=2)
    
    def _get_style_description(self) -> str:
        """获取风格描述"""
        return "高质量，细节丰富，专业摄影，电影级画质"
    
    def _parse_storyboard_response(self, response: str) -> List[StoryboardShot]:
        """解析分镜响应"""
        try:
            # 尝试解析JSON
            data = json.loads(response)
            shots = []
            
            for shot_data in data.get("shots", []):
                shot = StoryboardShot(
                    shot_id=shot_data.get("shot_id", 1),
                    scene=shot_data.get("scene", "默认场景"),
                    characters=shot_data.get("characters", []),
                    image_prompt=shot_data.get("image_prompt", ""),
                    narration=shot_data.get("narration", ""),
                    duration=float(shot_data.get("duration", 5.0)),
                    camera_angle=shot_data.get("camera_angle", "中景"),
                    lighting=shot_data.get("lighting", "自然光")
                )
                shots.append(shot)
            
            return shots
            
        except json.JSONDecodeError:
            logger.error("JSON解析失败，尝试文本解析")
            return self._parse_text_response(response)
    
    def _parse_text_response(self, response: str) -> List[StoryboardShot]:
        """解析文本响应"""
        # 简单的文本解析逻辑
        shots = []
        lines = response.split('\n')
        
        current_shot = None
        shot_id = 1
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            if "镜头" in line or "shot" in line.lower():
                if current_shot:
                    shots.append(current_shot)
                current_shot = StoryboardShot(
                    shot_id=shot_id,
                    scene=f"场景{shot_id}",
                    characters=["主角"],
                    image_prompt=line,
                    narration=line,
                    duration=5.0
                )
                shot_id += 1
        
        if current_shot:
            shots.append(current_shot)
        
        # 如果没有解析到镜头，创建默认镜头
        if not shots:
            shots.append(StoryboardShot(
                shot_id=1,
                scene="默认场景",
                characters=["主角"],
                image_prompt="根据用户输入生成画面",
                narration="默认旁白",
                duration=5.0
            ))
        
        return shots

# 全局LLM服务实例
llm_service = LLMService()
