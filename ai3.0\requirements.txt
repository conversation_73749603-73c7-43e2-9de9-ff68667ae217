# AI视频生成器2.0 依赖包

# 核心依赖
aiohttp>=3.8.0
aiofiles>=23.0.0
requests>=2.28.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Web界面
streamlit>=1.28.0
fastapi>=0.104.0
uvicorn>=0.24.0

# 异步处理
asyncio-throttle>=1.0.0

# 文本处理
openai>=1.0.0
anthropics>=0.3.0
zhipuai>=2.0.0
dashscope>=1.0.0
google-generativeai>=0.3.0

# 翻译服务
baidu-trans>=1.0.0
googletrans>=4.0.0

# 语音合成
edge-tts>=6.1.0
pyttsx3>=2.90
speech-recognition>=3.10.0

# 图像处理
Pillow>=10.0.0
opencv-python>=4.8.0
numpy>=1.24.0
requests-toolbelt>=1.0.0

# 视频处理
ffmpeg-python>=0.2.0
moviepy>=1.0.3

# 音频处理
pydub>=0.25.0
librosa>=0.10.0
soundfile>=0.12.0

# 社交媒体API
google-auth>=2.0.0
google-auth-oauthlib>=1.0.0
google-auth-httplib2>=0.1.0
google-api-python-client>=2.0.0
tiktok-api>=1.0.0
weibo-api>=1.0.0

# 数据处理
pandas>=2.0.0
numpy>=1.24.0

# 配置管理
python-dotenv>=1.0.0
PyYAML>=6.0
toml>=0.10.0

# 日志和监控
loguru>=0.7.0
tqdm>=4.65.0

# 网络请求
httpx>=0.24.0
websockets>=11.0.0

# 文件处理
pathlibplus>=1.0.0
watchdog>=3.0.0

# 加密和安全
cryptography>=41.0.0

# 时间处理
python-dateutil>=2.8.0
pytz>=2023.3

# 并发处理
concurrent-futures>=3.1.0

# 系统工具
psutil>=5.9.0

# 开发工具（可选）
black>=23.0.0
flake8>=6.0.0
pytest>=7.0.0
pytest-asyncio>=0.21.0

# 特定平台依赖
# Windows
win32api>=228; sys_platform == "win32"
win32con>=228; sys_platform == "win32"
win32gui>=228; sys_platform == "win32"

# 可选依赖（根据需要安装）
# GPU加速（如果有NVIDIA GPU）
# torch>=2.0.0
# torchvision>=0.15.0
# torchaudio>=2.0.0

# 机器学习（如果需要本地AI模型）
# transformers>=4.30.0
# diffusers>=0.20.0
# accelerate>=0.20.0

# 数据库（如果需要数据存储）
# sqlalchemy>=2.0.0
# sqlite3

# 缓存（如果需要缓存功能）
# redis>=4.5.0
# memcached>=1.6.0