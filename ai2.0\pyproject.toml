[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-video-generator"
version = "2.0.0"
description = "AI-powered video generation tool"
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}
authors = [
    {name = "AI Video Generator Team", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Multimedia :: Video",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
dependencies = [
    "PyQt6>=6.5.0",
    "aiohttp>=3.8.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.11.0",
    "pydantic>=2.0.0",
    "pillow>=10.0.0",
    "numpy>=1.24.0",
    "moviepy>=1.0.3",
    "pydub>=0.25.1",
    "edge-tts>=6.1.0",
    "pyyaml>=6.0",
    "click>=8.1.0",
    "rich>=13.0.0",
    "httpx>=0.24.0",
    "asyncio-mqtt>=0.13.0",
    "dependency-injector>=4.41.0",
    "structlog>=23.0.0",
    "tenacity>=8.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0",
    "pytest-cov>=4.0",
    "pytest-asyncio>=0.21",
    "pytest-qt>=4.2.0",
    "pytest-mock>=3.10.0",
    "black>=23.0",
    "isort>=5.0",
    "flake8>=6.0",
    "flake8-docstrings>=1.7.0",
    "mypy>=1.0",
    "pre-commit>=3.0",
    "factory-boy>=3.2.0",
]
docs = [
    "sphinx>=6.0",
    "sphinx-rtd-theme>=1.0",
    "myst-parser>=1.0",
    "sphinx-autodoc-typehints>=1.23.0",
]
test = [
    "pytest>=7.0",
    "pytest-cov>=4.0",
    "pytest-asyncio>=0.21",
    "pytest-qt>=4.2.0",
    "pytest-mock>=3.10.0",
    "factory-boy>=3.2.0",
]

[project.scripts]
ai-video-generator = "src.main:main"

[project.urls]
Homepage = "https://github.com/ai-video-generator/ai-video-generator"
Documentation = "https://ai-video-generator.readthedocs.io/"
Repository = "https://github.com/ai-video-generator/ai-video-generator"
"Bug Tracker" = "https://github.com/ai-video-generator/ai-video-generator/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]

[tool.setuptools.package-data]
"src" = ["assets/*", "themes/*", "templates/*", "config/*"]

[tool.black]
line-length = 127
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 127
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "ui: marks tests as UI tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
]
show_missing = true
skip_covered = false
fail_under = 90

[tool.flake8]
max-line-length = 127
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    ".venv",
    "venv",
    "build",
    "dist",
    "*.egg-info",
]
max-complexity = 10
docstring-convention = "google"
