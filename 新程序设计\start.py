# -*- coding: utf-8 -*-
"""
AI视频生成器2.0 - 快速启动脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main import AIVideoGenerator, print_banner

async def quick_start():
    """快速启动"""
    print_banner()
    
    print("🚀 AI视频生成器2.0 快速启动")
    print("\n正在检查配置...")
    
    # 创建生成器实例
    generator = AIVideoGenerator()
    
    try:
        # 初始化
        print("正在初始化服务...")
        success = await generator.initialize()
        
        if not success:
            print("❌ 初始化失败，请检查配置")
            print("\n💡 提示:")
            print("1. 确保配置文件存在: f:/DAI/config/")
            print("2. 检查API密钥是否正确配置")
            print("3. 确保网络连接正常")
            return
        
        print("✅ 初始化完成")
        
        # 显示服务状态
        status = generator.get_service_status()
        print("\n📊 可用服务:")
        
        for service_name, service_info in status.get('services', {}).items():
            if isinstance(service_info, dict):
                if 'clients' in service_info:
                    clients = service_info['clients']
                    print(f"  {service_name}: {len(clients)} 个客户端 ({', '.join(clients)})")
                elif 'available_platforms' in service_info:
                    platforms = service_info['available_platforms']
                    print(f"  {service_name}: {len(platforms)} 个平台 ({', '.join(platforms)})")
        
        # 示例生成
        print("\n🎬 开始示例视频生成...")
        
        example_text = """
人工智能正在改变我们的世界。从自动驾驶汽车到智能语音助手，AI技术已经深入到我们生活的方方面面。

在未来，AI将在医疗、教育、娱乐等领域发挥更大的作用。我们需要拥抱这个变化，同时也要思考如何让AI更好地服务人类。

让我们一起探索AI的无限可能，创造一个更美好的未来。
"""
        
        print(f"示例内容: {example_text[:50]}...")
        
        result = await generator.generate_video(
            input_text=example_text,
            title="AI改变世界 - 探索人工智能的无限可能",
            description="一个关于人工智能如何改变我们生活的短视频",
            tags=["AI", "人工智能", "科技", "未来"],
            target_language="zh-CN",
            video_duration=30.0,
            video_style="realistic"
        )
        
        # 显示结果
        if result.success:
            print(f"\n🎉 示例视频生成成功！")
            print(f"📁 输出目录: {os.path.dirname(result.final_video)}")
            print(f"🎥 最终视频: {result.final_video}")
            print(f"⏱️  总耗时: {result.total_time:.2f}秒")
            
            # 询问是否打开输出目录
            try:
                open_dir = input("\n是否打开输出目录? [y/N]: ").strip().lower()
                if open_dir == 'y':
                    import subprocess
                    import platform
                    
                    output_dir = os.path.dirname(result.final_video)
                    if platform.system() == "Windows":
                        subprocess.run(["explorer", output_dir])
                    elif platform.system() == "Darwin":  # macOS
                        subprocess.run(["open", output_dir])
                    else:  # Linux
                        subprocess.run(["xdg-open", output_dir])
            except:
                pass
        else:
            print(f"\n❌ 示例视频生成失败: {result.error_message}")
            if result.failed_steps:
                print(f"失败步骤: {', '.join(result.failed_steps)}")
        
        print("\n💡 使用提示:")
        print("1. 运行 'python main.py --interactive' 进入交互模式")
        print("2. 运行 'python main.py --input "你的文本" --title "视频标题"' 直接生成")
        print("3. 运行 'python main.py --help' 查看所有参数")
        print("4. 运行 'python main.py --status' 查看服务状态")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await generator.cleanup()
        print("\n👋 程序结束")

if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行快速启动
    asyncio.run(quick_start())