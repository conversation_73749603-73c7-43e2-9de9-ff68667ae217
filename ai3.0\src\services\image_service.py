# -*- coding: utf-8 -*-
"""
图像生成服务实现
集成Pollinations、ComfyUI、DALL-E、Stability AI等图像生成服务
"""

import asyncio
import base64
import json
import logging
import os
import tempfile
import time
from typing import Dict, Any, List, Optional, Union
import aiohttp
import aiofiles
from dataclasses import dataclass
from pathlib import Path
import hashlib

from ..core.mcp_service_manager import (
    MCPServiceInterface, ServiceRequest, ServiceResponse, ServiceType, ServiceStatus
)

logger = logging.getLogger(__name__)

@dataclass
class ImageRequest:
    """图像生成请求数据结构"""
    prompt: str
    negative_prompt: str = ""
    width: int = 1024
    height: int = 1024
    steps: int = 20
    guidance_scale: float = 7.5
    seed: int = -1  # -1表示随机
    style: str = "realistic"  # realistic, anime, artistic等
    quality: str = "standard"  # standard, hd
    output_path: Optional[str] = None
    model: str = ""  # 指定模型

@dataclass
class ImageResponse:
    """图像生成响应数据结构"""
    image_path: str
    width: int = 0
    height: int = 0
    file_size: int = 0
    generation_time: float = 0.0
    model_used: str = ""
    service_used: str = ""
    success: bool = True
    error_message: str = ""
    prompt_used: str = ""

class BaseImageClient(MCPServiceInterface):
    """图像生成客户端基类"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.session = None
        self.supported_models = self._get_supported_models()
        self.supported_styles = self._get_supported_styles()
    
    def _get_supported_models(self) -> List[str]:
        """获取支持的模型列表（子类实现）"""
        return []
    
    def _get_supported_styles(self) -> List[str]:
        """获取支持的风格列表（子类实现）"""
        return ['realistic', 'anime', 'artistic', 'cartoon']
    
    async def initialize(self) -> bool:
        """初始化图像生成客户端"""
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=120)
            )
            
            health_ok = await self.health_check()
            if health_ok:
                logger.info(f"{self.name}: 图像生成服务初始化成功")
                return True
            else:
                logger.warning(f"{self.name}: 图像生成服务健康检查失败")
                return False
                
        except Exception as e:
            logger.error(f"{self.name}: 图像生成服务初始化失败 - {e}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        if self.session:
            await self.session.close()
    
    async def process(self, request: ServiceRequest) -> ServiceResponse:
        """处理图像生成请求"""
        start_time = time.time()
        
        try:
            action = request.parameters.get('action', 'generate')
            
            if action == 'generate':
                image_request = ImageRequest(**request.parameters.get('image_request', {}))
                result = await self.generate_image(image_request)
                
                return ServiceResponse(
                    success=True,
                    data=result,
                    execution_time=time.time() - start_time,
                    service_name=self.name
                )
            
            elif action == 'enhance_prompt':
                prompt = request.parameters.get('prompt', '')
                style = request.parameters.get('style', 'realistic')
                result = await self.enhance_prompt(prompt, style)
                
                return ServiceResponse(
                    success=True,
                    data=result,
                    execution_time=time.time() - start_time,
                    service_name=self.name
                )
            
            elif action == 'batch_generate':
                prompts = request.parameters.get('prompts', [])
                output_dir = request.parameters.get('output_dir', tempfile.gettempdir())
                result = await self.batch_generate(prompts, output_dir)
                
                return ServiceResponse(
                    success=True,
                    data=result,
                    execution_time=time.time() - start_time,
                    service_name=self.name
                )
            
            else:
                return ServiceResponse(
                    success=False,
                    error=f"不支持的操作: {action}",
                    service_name=self.name
                )
                
        except Exception as e:
            return ServiceResponse(
                success=False,
                error=str(e),
                execution_time=time.time() - start_time,
                service_name=self.name
            )
    
    async def generate_image(self, request: ImageRequest) -> ImageResponse:
        """生成图像（子类实现）"""
        raise NotImplementedError
    
    async def enhance_prompt(self, prompt: str, style: str = "realistic") -> str:
        """增强提示词（子类实现）"""
        return prompt
    
    async def batch_generate(self, prompts: List[str], output_dir: str) -> List[ImageResponse]:
        """批量生成图像"""
        results = []
        for i, prompt in enumerate(prompts):
            output_path = os.path.join(output_dir, f"image_{i+1}.png")
            request = ImageRequest(
                prompt=prompt,
                output_path=output_path
            )
            result = await self.generate_image(request)
            results.append(result)
            # 添加延迟避免API限制
            await asyncio.sleep(1.0)
        return results

class PollinationsClient(BaseImageClient):
    """Pollinations AI客户端"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.base_url = config.get('base_url', 'https://image.pollinations.ai/prompt')
        self.enhance_url = config.get('enhance_url', 'https://text.pollinations.ai/openai')
    
    def _get_supported_models(self) -> List[str]:
        """Pollinations支持的模型"""
        return ['flux', 'flux-realism', 'flux-anime', 'flux-3d']
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            async with self.session.get('https://pollinations.ai') as response:
                return response.status == 200
        except:
            return False
    
    async def generate_image(self, request: ImageRequest) -> ImageResponse:
        """使用Pollinations生成图像"""
        try:
            start_time = time.time()
            
            # 设置输出路径
            if not request.output_path:
                output_dir = tempfile.gettempdir()
                timestamp = int(time.time() * 1000)
                request.output_path = os.path.join(output_dir, f"pollinations_{timestamp}.png")
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(request.output_path), exist_ok=True)
            
            # 构建请求URL
            params = {
                'width': request.width,
                'height': request.height,
                'seed': request.seed if request.seed > 0 else None,
                'model': request.model if request.model else 'flux',
                'enhance': 'true'  # 自动增强提示词
            }
            
            # 过滤None值
            params = {k: v for k, v in params.items() if v is not None}
            
            # 编码提示词
            encoded_prompt = request.prompt.replace(' ', '%20')
            url = f"{self.base_url}/{encoded_prompt}"
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    # 保存图像文件
                    async with aiofiles.open(request.output_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            await f.write(chunk)
                    
                    # 获取文件信息
                    file_size = os.path.getsize(request.output_path)
                    generation_time = time.time() - start_time
                    
                    return ImageResponse(
                        image_path=request.output_path,
                        width=request.width,
                        height=request.height,
                        file_size=file_size,
                        generation_time=generation_time,
                        model_used=request.model or 'flux',
                        service_used=self.name,
                        success=True,
                        prompt_used=request.prompt
                    )
                else:
                    error_text = await response.text()
                    raise Exception(f"Pollinations API错误 {response.status}: {error_text}")
                    
        except Exception as e:
            logger.error(f"Pollinations图像生成失败: {e}")
            return ImageResponse(
                image_path="",
                success=False,
                error_message=str(e),
                service_used=self.name
            )
    
    async def enhance_prompt(self, prompt: str, style: str = "realistic") -> str:
        """使用Pollinations增强提示词"""
        try:
            enhance_prompt = f"Enhance this image generation prompt to be more detailed and specific for {style} style: {prompt}"
            
            data = {
                'messages': [
                    {'role': 'user', 'content': enhance_prompt}
                ],
                'model': 'openai',
                'jsonMode': False
            }
            
            async with self.session.post(self.enhance_url, json=data) as response:
                if response.status == 200:
                    enhanced = await response.text()
                    return enhanced.strip()
                else:
                    return prompt
        except Exception as e:
            logger.error(f"提示词增强失败: {e}")
            return prompt

class ComfyUIClient(BaseImageClient):
    """ComfyUI客户端"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.base_url = config.get('base_url', 'http://localhost:8188')
        self.api_key = config.get('api_key', '')
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            async with self.session.get(f"{self.base_url}/system_stats") as response:
                return response.status == 200
        except:
            return False
    
    async def generate_image(self, request: ImageRequest) -> ImageResponse:
        """使用ComfyUI生成图像"""
        try:
            start_time = time.time()
            
            # 设置输出路径
            if not request.output_path:
                output_dir = tempfile.gettempdir()
                timestamp = int(time.time() * 1000)
                request.output_path = os.path.join(output_dir, f"comfyui_{timestamp}.png")
            
            # 构建ComfyUI工作流（简化版）
            workflow = {
                "3": {
                    "inputs": {
                        "seed": request.seed if request.seed > 0 else -1,
                        "steps": request.steps,
                        "cfg": request.guidance_scale,
                        "sampler_name": "euler",
                        "scheduler": "normal",
                        "denoise": 1,
                        "model": ["4", 0],
                        "positive": ["6", 0],
                        "negative": ["7", 0],
                        "latent_image": ["5", 0]
                    },
                    "class_type": "KSampler"
                },
                "4": {
                    "inputs": {
                        "ckpt_name": "sd_xl_base_1.0.safetensors"
                    },
                    "class_type": "CheckpointLoaderSimple"
                },
                "5": {
                    "inputs": {
                        "width": request.width,
                        "height": request.height,
                        "batch_size": 1
                    },
                    "class_type": "EmptyLatentImage"
                },
                "6": {
                    "inputs": {
                        "text": request.prompt,
                        "clip": ["4", 1]
                    },
                    "class_type": "CLIPTextEncode"
                },
                "7": {
                    "inputs": {
                        "text": request.negative_prompt,
                        "clip": ["4", 1]
                    },
                    "class_type": "CLIPTextEncode"
                },
                "8": {
                    "inputs": {
                        "samples": ["3", 0],
                        "vae": ["4", 2]
                    },
                    "class_type": "VAEDecode"
                },
                "9": {
                    "inputs": {
                        "filename_prefix": "ComfyUI",
                        "images": ["8", 0]
                    },
                    "class_type": "SaveImage"
                }
            }
            
            # 提交工作流
            headers = {}
            if self.api_key:
                headers['Authorization'] = f'Bearer {self.api_key}'
            
            async with self.session.post(
                f"{self.base_url}/prompt",
                json={"prompt": workflow},
                headers=headers
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    prompt_id = result['prompt_id']
                    
                    # 等待生成完成
                    await self._wait_for_completion(prompt_id)
                    
                    # 获取生成的图像
                    image_data = await self._get_generated_image(prompt_id)
                    if image_data:
                        # 保存图像
                        async with aiofiles.open(request.output_path, 'wb') as f:
                            await f.write(image_data)
                        
                        file_size = os.path.getsize(request.output_path)
                        generation_time = time.time() - start_time
                        
                        return ImageResponse(
                            image_path=request.output_path,
                            width=request.width,
                            height=request.height,
                            file_size=file_size,
                            generation_time=generation_time,
                            service_used=self.name,
                            success=True,
                            prompt_used=request.prompt
                        )
                    else:
                        raise Exception("无法获取生成的图像")
                else:
                    error_text = await response.text()
                    raise Exception(f"ComfyUI API错误 {response.status}: {error_text}")
                    
        except Exception as e:
            logger.error(f"ComfyUI图像生成失败: {e}")
            return ImageResponse(
                image_path="",
                success=False,
                error_message=str(e),
                service_used=self.name
            )
    
    async def _wait_for_completion(self, prompt_id: str, timeout: int = 120):
        """等待生成完成"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                async with self.session.get(f"{self.base_url}/history/{prompt_id}") as response:
                    if response.status == 200:
                        history = await response.json()
                        if prompt_id in history and history[prompt_id].get('status', {}).get('completed', False):
                            return
            except:
                pass
            await asyncio.sleep(2)
        raise Exception("生成超时")
    
    async def _get_generated_image(self, prompt_id: str) -> Optional[bytes]:
        """获取生成的图像数据"""
        try:
            async with self.session.get(f"{self.base_url}/history/{prompt_id}") as response:
                if response.status == 200:
                    history = await response.json()
                    outputs = history[prompt_id]['outputs']
                    
                    # 查找保存的图像
                    for node_id, output in outputs.items():
                        if 'images' in output:
                            for image_info in output['images']:
                                filename = image_info['filename']
                                subfolder = image_info.get('subfolder', '')
                                
                                # 下载图像
                                params = {'filename': filename}
                                if subfolder:
                                    params['subfolder'] = subfolder
                                
                                async with self.session.get(
                                    f"{self.base_url}/view",
                                    params=params
                                ) as img_response:
                                    if img_response.status == 200:
                                        return await img_response.read()
            return None
        except Exception as e:
            logger.error(f"获取ComfyUI生成图像失败: {e}")
            return None

class DALLEClient(BaseImageClient):
    """DALL-E客户端"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.api_key = config.get('api_key', '')
        self.base_url = config.get('base_url', 'https://api.openai.com/v1/images/generations')
    
    def _get_supported_models(self) -> List[str]:
        """DALL-E支持的模型"""
        return ['dall-e-2', 'dall-e-3']
    
    async def health_check(self) -> bool:
        """健康检查"""
        return bool(self.api_key)
    
    async def generate_image(self, request: ImageRequest) -> ImageResponse:
        """使用DALL-E生成图像"""
        if not self.api_key:
            raise Exception("OpenAI API密钥未配置")
        
        try:
            start_time = time.time()
            
            # 设置输出路径
            if not request.output_path:
                output_dir = tempfile.gettempdir()
                timestamp = int(time.time() * 1000)
                request.output_path = os.path.join(output_dir, f"dalle_{timestamp}.png")
            
            # 构建请求数据
            data = {
                'model': request.model if request.model in self.supported_models else 'dall-e-3',
                'prompt': request.prompt,
                'n': 1,
                'size': f"{request.width}x{request.height}" if request.width == request.height else "1024x1024",
                'quality': request.quality,
                'response_format': 'url'
            }
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            async with self.session.post(self.base_url, headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    image_url = result['data'][0]['url']
                    
                    # 下载图像
                    async with self.session.get(image_url) as img_response:
                        if img_response.status == 200:
                            async with aiofiles.open(request.output_path, 'wb') as f:
                                async for chunk in img_response.content.iter_chunked(8192):
                                    await f.write(chunk)
                            
                            file_size = os.path.getsize(request.output_path)
                            generation_time = time.time() - start_time
                            
                            return ImageResponse(
                                image_path=request.output_path,
                                width=request.width,
                                height=request.height,
                                file_size=file_size,
                                generation_time=generation_time,
                                model_used=data['model'],
                                service_used=self.name,
                                success=True,
                                prompt_used=request.prompt
                            )
                        else:
                            raise Exception("下载生成的图像失败")
                else:
                    error_text = await response.text()
                    raise Exception(f"DALL-E API错误 {response.status}: {error_text}")
                    
        except Exception as e:
            logger.error(f"DALL-E图像生成失败: {e}")
            return ImageResponse(
                image_path="",
                success=False,
                error_message=str(e),
                service_used=self.name
            )

class ImageServiceManager:
    """图像生成服务管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.clients = {}
        self.routing_strategy = config.get('routing_strategy', 'quality_first')
        self._initialize_clients()
    
    def _initialize_clients(self):
        """初始化图像生成客户端"""
        engines = self.config.get('engines', {})
        
        # Pollinations AI（免费）
        if 'pollinations' in engines:
            pollinations_config = engines['pollinations']
            self.clients['pollinations'] = PollinationsClient('Pollinations AI', pollinations_config)
        
        # ComfyUI（本地或云端）
        if 'comfyui_local' in engines:
            comfyui_config = engines['comfyui_local']
            self.clients['comfyui_local'] = ComfyUIClient('ComfyUI本地', comfyui_config)
        
        if 'comfyui_cloud' in engines:
            comfyui_config = engines['comfyui_cloud']
            self.clients['comfyui_cloud'] = ComfyUIClient('ComfyUI云端', comfyui_config)
        
        # DALL-E
        if 'dalle' in engines and engines['dalle'].get('api_key'):
            dalle_config = engines['dalle']
            self.clients['dalle'] = DALLEClient('DALL-E', dalle_config)
    
    def get_client(self, name: str) -> Optional[BaseImageClient]:
        """获取图像生成客户端"""
        return self.clients.get(name)
    
    def get_all_clients(self) -> List[BaseImageClient]:
        """获取所有图像生成客户端"""
        return list(self.clients.values())
    
    def get_best_client(self, style: str = "realistic", quality: str = "standard") -> Optional[BaseImageClient]:
        """获取最佳图像生成客户端"""
        if self.routing_strategy == 'quality_first':
            # 优先使用高质量服务
            if quality == 'hd' and 'dalle' in self.clients:
                return self.clients['dalle']
            elif 'comfyui_local' in self.clients:
                return self.clients['comfyui_local']
            elif 'comfyui_cloud' in self.clients:
                return self.clients['comfyui_cloud']
            elif 'pollinations' in self.clients:
                return self.clients['pollinations']
        
        elif self.routing_strategy == 'speed_first':
            # 优先使用快速服务
            if 'pollinations' in self.clients:
                return self.clients['pollinations']
            elif 'dalle' in self.clients:
                return self.clients['dalle']
        
        elif self.routing_strategy == 'cost_first':
            # 优先使用免费服务
            if 'pollinations' in self.clients:
                return self.clients['pollinations']
            elif 'comfyui_local' in self.clients:
                return self.clients['comfyui_local']
        
        # 默认返回第一个可用客户端
        return next(iter(self.clients.values())) if self.clients else None
    
    async def initialize_all(self):
        """初始化所有客户端"""
        for client in self.clients.values():
            await client.initialize()
    
    async def cleanup_all(self):
        """清理所有客户端"""
        for client in self.clients.values():
            await client.cleanup()
    
    async def generate_image(self, prompt: str, style: str = "realistic", 
                           width: int = 1024, height: int = 1024,
                           quality: str = "standard", output_path: str = None,
                           engine: str = None) -> Optional[ImageResponse]:
        """生成图像（便捷方法）"""
        # 选择引擎
        if engine:
            client = self.get_client(engine)
        else:
            client = self.get_best_client(style, quality)
        
        if not client:
            raise Exception("没有可用的图像生成服务")
        
        request = ImageRequest(
            prompt=prompt,
            width=width,
            height=height,
            style=style,
            quality=quality,
            output_path=output_path
        )
        
        return await client.generate_image(request)
    
    async def generate_storyboard_images(self, storyboard_data: Dict[str, Any], 
                                       output_dir: str, style: str = "realistic") -> Dict[str, List[str]]:
        """为分镜生成图像"""
        image_files = []
        
        try:
            shots = storyboard_data.get('shots', [])
            
            for i, shot in enumerate(shots):
                prompt = shot.get('visual_description', '')
                if prompt:
                    output_path = os.path.join(output_dir, f"shot_{i+1}.png")
                    
                    # 增强提示词
                    enhanced_prompt = await self._enhance_prompt_for_storyboard(prompt, style)
                    
                    response = await self.generate_image(
                        prompt=enhanced_prompt,
                        style=style,
                        output_path=output_path
                    )
                    
                    if response and response.success:
                        image_files.append(response.image_path)
                    else:
                        logger.warning(f"分镜 {i+1} 图像生成失败")
                
                # 添加延迟避免API限制
                await asyncio.sleep(1.0)
            
            return {'images': image_files}
            
        except Exception as e:
            logger.error(f"分镜图像生成失败: {e}")
            return {'images': []}
    
    async def _enhance_prompt_for_storyboard(self, prompt: str, style: str) -> str:
        """为分镜增强提示词"""
        style_prompts = {
            'realistic': 'photorealistic, high quality, detailed, cinematic lighting',
            'anime': 'anime style, manga, detailed, vibrant colors',
            'artistic': 'artistic, painterly, creative, expressive',
            'cartoon': 'cartoon style, colorful, fun, animated'
        }
        
        style_suffix = style_prompts.get(style, style_prompts['realistic'])
        enhanced = f"{prompt}, {style_suffix}, 16:9 aspect ratio, professional composition"
        
        return enhanced
    
    async def enhance_prompt(self, prompt: str, style: str = "realistic") -> str:
        """增强提示词"""
        # 尝试使用支持提示词增强的客户端
        if 'pollinations' in self.clients:
            client = self.clients['pollinations']
            return await client.enhance_prompt(prompt, style)
        
        # 如果没有支持的客户端，返回基础增强
        return await self._enhance_prompt_for_storyboard(prompt, style)

# 便捷函数
def create_image_service_manager(config: Dict[str, Any]) -> ImageServiceManager:
    """创建图像生成服务管理器"""
    return ImageServiceManager(config)

if __name__ == "__main__":
    # 测试代码
    async def test_image_service():
        # 模拟配置
        test_config = {
            'routing_strategy': 'quality_first',
            'engines': {
                'pollinations': {
                    'base_url': 'https://image.pollinations.ai/prompt',
                    'enhance_url': 'https://text.pollinations.ai/openai'
                },
                'dalle': {
                    'api_key': '',  # 需要配置实际API密钥
                    'base_url': 'https://api.openai.com/v1/images/generations'
                }
            }
        }
        
        manager = create_image_service_manager(test_config)
        await manager.initialize_all()
        
        # 测试图像生成
        try:
            response = await manager.generate_image(
                prompt="一只可爱的小猫坐在花园里，阳光明媚",
                style="realistic",
                width=1024,
                height=1024
            )
            if response and response.success:
                print(f"图像生成成功: {response.image_path}")
                print(f"使用服务: {response.service_used}")
                print(f"生成时间: {response.generation_time:.2f}秒")
                print(f"文件大小: {response.file_size} bytes")
        except Exception as e:
            print(f"图像生成失败: {e}")
        
        await manager.cleanup_all()
    
    # asyncio.run(test_image_service())
    print("图像生成服务模块已加载")