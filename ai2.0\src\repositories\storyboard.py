"""分镜仓库

实现分镜相关的数据访问操作。
"""

from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from sqlalchemy.orm import selectinload

from src.models import Storyboard, StoryboardStatus
from src.repositories.base import AsyncRepository
from src.core.exceptions import DataAccessError


class StoryboardRepository(AsyncRepository[Storyboard]):
    """分镜仓库"""
    
    def __init__(self, session: AsyncSession):
        super().__init__(session, Storyboard)
    
    async def get_by_project_id(self, project_id: UUID, skip: int = 0, limit: int = 100) -> List[Storyboard]:
        """根据项目ID获取分镜"""
        return await self.find_by(project_id=project_id, skip=skip, limit=limit)
    
    async def get_by_id_with_shots(self, storyboard_id: UUID) -> Optional[Storyboard]:
        """获取分镜及其镜头"""
        try:
            query = (
                select(Storyboard)
                .options(selectinload(Storyboard.shots))
                .where(and_(
                    Storyboard.id == storyboard_id,
                    Storyboard.is_deleted == False
                ))
            )
            
            result = await self.session.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            raise DataAccessError(f"Failed to get storyboard with shots: {str(e)}", cause=e)
    
    async def get_by_status(self, status: StoryboardStatus, skip: int = 0, limit: int = 100) -> List[Storyboard]:
        """根据状态获取分镜"""
        return await self.find_by(status=status, skip=skip, limit=limit)
    
    async def get_processing_storyboards(self) -> List[Storyboard]:
        """获取正在处理的分镜"""
        return await self.find_by(status=StoryboardStatus.GENERATING)
    
    async def get_failed_storyboards(self, skip: int = 0, limit: int = 100) -> List[Storyboard]:
        """获取处理失败的分镜"""
        return await self.get_by_status(StoryboardStatus.FAILED, skip, limit)
    
    async def get_completed_storyboards(self, skip: int = 0, limit: int = 100) -> List[Storyboard]:
        """获取已完成的分镜"""
        return await self.get_by_status(StoryboardStatus.COMPLETED, skip, limit)
    
    async def start_processing(self, storyboard_id: UUID) -> Optional[Storyboard]:
        """开始处理分镜"""
        try:
            storyboard = await self.get_by_id(storyboard_id)
            if not storyboard:
                return None
            
            storyboard.start_processing()
            await self.session.commit()
            await self.session.refresh(storyboard)
            
            return storyboard
        except Exception as e:
            await self.session.rollback()
            raise DataAccessError(f"Failed to start processing storyboard: {str(e)}", cause=e)
    
    async def complete_processing(self, storyboard_id: UUID) -> Optional[Storyboard]:
        """完成处理分镜"""
        try:
            storyboard = await self.get_by_id(storyboard_id)
            if not storyboard:
                return None
            
            storyboard.complete_processing()
            await self.session.commit()
            await self.session.refresh(storyboard)
            
            return storyboard
        except Exception as e:
            await self.session.rollback()
            raise DataAccessError(f"Failed to complete processing storyboard: {str(e)}", cause=e)
    
    async def fail_processing(self, storyboard_id: UUID, error_message: str) -> Optional[Storyboard]:
        """处理失败分镜"""
        try:
            storyboard = await self.get_by_id(storyboard_id)
            if not storyboard:
                return None
            
            storyboard.fail_processing(error_message)
            await self.session.commit()
            await self.session.refresh(storyboard)
            
            return storyboard
        except Exception as e:
            await self.session.rollback()
            raise DataAccessError(f"Failed to fail processing storyboard: {str(e)}", cause=e)
    
    async def update_statistics(self, storyboard_id: UUID) -> Optional[Storyboard]:
        """更新分镜统计信息"""
        try:
            storyboard = await self.get_by_id_with_shots(storyboard_id)
            if not storyboard:
                return None
            
            # 重新计算统计信息
            storyboard.update_statistics()
            
            # 保存更新
            await self.session.commit()
            await self.session.refresh(storyboard)
            
            return storyboard
        except Exception as e:
            await self.session.rollback()
            raise DataAccessError(f"Failed to update storyboard statistics: {str(e)}", cause=e)
    
    async def get_statistics_by_project(self, project_id: UUID) -> Dict[str, Any]:
        """获取项目的分镜统计信息"""
        try:
            # 总分镜数
            total_query = select(func.count(Storyboard.id)).where(and_(
                Storyboard.project_id == project_id,
                Storyboard.is_deleted == False
            ))
            total_result = await self.session.execute(total_query)
            total_storyboards = total_result.scalar()
            
            # 按状态统计
            status_stats = {}
            for status in StoryboardStatus:
                status_query = select(func.count(Storyboard.id)).where(and_(
                    Storyboard.project_id == project_id,
                    Storyboard.status == status,
                    Storyboard.is_deleted == False
                ))
                status_result = await self.session.execute(status_query)
                status_stats[status.value] = status_result.scalar()
            
            # 总镜头数
            shots_query = select(func.sum(Storyboard.shot_count)).where(and_(
                Storyboard.project_id == project_id,
                Storyboard.is_deleted == False
            ))
            shots_result = await self.session.execute(shots_query)
            total_shots = shots_result.scalar() or 0
            
            # 总时长
            duration_query = select(func.sum(Storyboard.total_duration)).where(and_(
                Storyboard.project_id == project_id,
                Storyboard.is_deleted == False
            ))
            duration_result = await self.session.execute(duration_query)
            total_duration = duration_result.scalar() or 0.0
            
            return {
                'total_storyboards': total_storyboards,
                'status_distribution': status_stats,
                'total_shots': total_shots,
                'total_duration': total_duration
            }
        except Exception as e:
            raise DataAccessError(f"Failed to get storyboard statistics: {str(e)}", cause=e)
    
    async def get_recent_storyboards(self, limit: int = 10) -> List[Storyboard]:
        """获取最近的分镜"""
        try:
            query = (
                select(Storyboard)
                .where(Storyboard.is_deleted == False)
                .order_by(Storyboard.updated_at.desc())
                .limit(limit)
            )
            
            result = await self.session.execute(query)
            return result.scalars().all()
        except Exception as e:
            raise DataAccessError(f"Failed to get recent storyboards: {str(e)}", cause=e)
    
    async def search_by_content(self, content: str, skip: int = 0, limit: int = 100) -> List[Storyboard]:
        """根据内容搜索分镜"""
        try:
            query = (
                select(Storyboard)
                .where(and_(
                    Storyboard.content.ilike(f"%{content}%"),
                    Storyboard.is_deleted == False
                ))
                .order_by(Storyboard.updated_at.desc())
                .offset(skip)
                .limit(limit)
            )
            
            result = await self.session.execute(query)
            return result.scalars().all()
        except Exception as e:
            raise DataAccessError(f"Failed to search storyboards by content: {str(e)}", cause=e)
    
    async def get_storyboards_with_filters(
        self,
        project_id: Optional[UUID] = None,
        status: Optional[StoryboardStatus] = None,
        content_search: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Storyboard]:
        """根据多个条件过滤分镜"""
        try:
            query = select(Storyboard).where(Storyboard.is_deleted == False)
            
            if project_id:
                query = query.where(Storyboard.project_id == project_id)
            
            if status:
                query = query.where(Storyboard.status == status)
            
            if content_search:
                query = query.where(Storyboard.content.ilike(f"%{content_search}%"))
            
            query = query.order_by(Storyboard.updated_at.desc()).offset(skip).limit(limit)
            
            result = await self.session.execute(query)
            return result.scalars().all()
        except Exception as e:
            raise DataAccessError(f"Failed to get storyboards with filters: {str(e)}", cause=e)
    
    async def get_processing_duration_stats(self) -> Dict[str, Any]:
        """获取处理时长统计"""
        try:
            # 平均处理时长
            avg_query = select(func.avg(
                func.extract('epoch', Storyboard.processing_completed_at - Storyboard.processing_started_at)
            )).where(and_(
                Storyboard.status == StoryboardStatus.COMPLETED,
                Storyboard.processing_started_at.isnot(None),
                Storyboard.processing_completed_at.isnot(None)
            ))
            avg_result = await self.session.execute(avg_query)
            avg_duration = avg_result.scalar() or 0.0
            
            # 最长处理时长
            max_query = select(func.max(
                func.extract('epoch', Storyboard.processing_completed_at - Storyboard.processing_started_at)
            )).where(and_(
                Storyboard.status == StoryboardStatus.COMPLETED,
                Storyboard.processing_started_at.isnot(None),
                Storyboard.processing_completed_at.isnot(None)
            ))
            max_result = await self.session.execute(max_query)
            max_duration = max_result.scalar() or 0.0
            
            # 最短处理时长
            min_query = select(func.min(
                func.extract('epoch', Storyboard.processing_completed_at - Storyboard.processing_started_at)
            )).where(and_(
                Storyboard.status == StoryboardStatus.COMPLETED,
                Storyboard.processing_started_at.isnot(None),
                Storyboard.processing_completed_at.isnot(None)
            ))
            min_result = await self.session.execute(min_query)
            min_duration = min_result.scalar() or 0.0
            
            return {
                'average_duration': avg_duration,
                'max_duration': max_duration,
                'min_duration': min_duration
            }
        except Exception as e:
            raise DataAccessError(f"Failed to get processing duration stats: {str(e)}", cause=e)
