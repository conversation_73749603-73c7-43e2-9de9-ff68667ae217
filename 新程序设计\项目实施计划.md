# AI视频生成器2.0 项目实施计划

## 📋 项目概述

本文档是AI视频生成器2.0的详细实施计划，基于现有程序的深入分析和完整的设计文档制定。所有开发工作必须严格按照此计划执行，确保项目按时、按质完成。

### 项目目标
- 基于现有AI视频生成器重写全新的2.0版本
- 采用现代化技术栈和架构设计
- 显著提升用户体验和系统性能
- 保持所有现有功能的完整性
- 增强系统的可维护性和扩展性

### 技术升级重点
- **UI框架**：PyQt5 → PyQt6 + Material Design
- **架构模式**：分层架构 + 依赖注入 + 事件驱动
- **异步处理**：全面采用async/await模式
- **数据库**：支持SQLite和PostgreSQL
- **测试覆盖**：单元测试覆盖率 > 90%
- **代码质量**：100%类型注解 + 严格代码规范

---

## 🚀 分阶段实施计划

### 第一阶段：基础架构搭建（1-2周）

#### 1.1 项目初始化

**目标**：建立新项目的基础结构和开发环境

**任务清单**：
- [ ] 创建新项目目录结构
- [ ] 配置开发环境和依赖管理
- [ ] 设置版本控制和分支策略
- [ ] 配置开发工具链

**项目结构**：
```
新程序/
├── src/
│   ├── core/           # 核心模块
│   │   ├── __init__.py
│   │   ├── container.py        # 依赖注入容器
│   │   ├── config.py          # 配置管理
│   │   ├── events.py          # 事件系统
│   │   └── exceptions.py      # 异常定义
│   ├── ui/             # UI层
│   │   ├── __init__.py
│   │   ├── main_window.py     # 主窗口
│   │   ├── components/        # UI组件
│   │   ├── themes/           # 主题系统
│   │   └── styles/           # 样式文件
│   ├── controllers/    # 控制器层
│   │   ├── __init__.py
│   │   ├── app_controller.py  # 应用控制器
│   │   ├── project_controller.py # 项目控制器
│   │   └── storyboard_controller.py # 分镜控制器
│   ├── services/       # 服务层
│   │   ├── __init__.py
│   │   ├── ai_service.py      # AI服务接口
│   │   ├── llm_service.py     # LLM服务
│   │   ├── image_service.py   # 图像服务
│   │   ├── video_service.py   # 视频服务
│   │   └── voice_service.py   # 语音服务
│   ├── repositories/   # 数据访问层
│   │   ├── __init__.py
│   │   ├── base_repository.py # 基础仓库
│   │   ├── project_repository.py # 项目仓库
│   │   └── storyboard_repository.py # 分镜仓库
│   ├── models/         # 数据模型
│   │   ├── __init__.py
│   │   ├── base_model.py      # 基础模型
│   │   ├── project.py         # 项目模型
│   │   ├── storyboard.py      # 分镜模型
│   │   └── shot.py           # 镜头模型
│   └── utils/          # 工具模块
│       ├── __init__.py
│       ├── logger.py          # 日志工具
│       ├── cache.py          # 缓存工具
│       └── validators.py     # 验证工具
├── tests/              # 测试代码
│   ├── unit/          # 单元测试
│   ├── integration/   # 集成测试
│   ├── ui/           # UI测试
│   └── conftest.py   # 测试配置
├── config/             # 配置文件
│   ├── environments/  # 环境配置
│   ├── services/     # 服务配置
│   └── ui/          # UI配置
├── docs/               # 文档
│   ├── api/          # API文档
│   ├── user/         # 用户文档
│   └── developer/    # 开发者文档
├── assets/             # 资源文件
│   ├── icons/        # 图标
│   ├── themes/       # 主题
│   └── templates/    # 模板
├── requirements.txt    # 依赖管理
├── requirements-dev.txt # 开发依赖
├── setup.py           # 安装配置
├── pyproject.toml     # 项目配置
├── .pre-commit-config.yaml # Pre-commit配置
└── README.md          # 项目说明
```

#### 1.2 核心基础设施

**依赖注入容器**：
```python
# src/core/container.py
from typing import Dict, Type, TypeVar, Callable, Any
from abc import ABC, abstractmethod

T = TypeVar('T')

class Container:
    """依赖注入容器"""
    
    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._factories: Dict[str, Callable] = {}
        self._singletons: Dict[str, Any] = {}
    
    def register_singleton(self, interface: Type[T], implementation: Type[T]) -> None:
        """注册单例服务"""
        key = interface.__name__
        self._factories[key] = implementation
    
    def register_transient(self, interface: Type[T], implementation: Type[T]) -> None:
        """注册瞬态服务"""
        key = interface.__name__
        self._factories[key] = implementation
    
    def get(self, interface: Type[T]) -> T:
        """获取服务实例"""
        key = interface.__name__
        
        if key in self._singletons:
            return self._singletons[key]
        
        if key in self._factories:
            instance = self._factories[key]()
            if self._is_singleton(key):
                self._singletons[key] = instance
            return instance
        
        raise ValueError(f"Service {key} not registered")
```

**配置管理系统**：
```python
# src/core/config.py
from typing import Dict, Any, Optional
from pathlib import Path
import yaml
import os

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Path):
        self.config_dir = config_dir
        self.environment = os.getenv('APP_ENV', 'development')
        self._config: Dict[str, Any] = {}
        self._load_config()
    
    def _load_config(self) -> None:
        """加载配置文件"""
        # 加载基础配置
        base_config_path = self.config_dir / 'base.yaml'
        if base_config_path.exists():
            with open(base_config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f) or {}
        
        # 加载环境特定配置
        env_config_path = self.config_dir / 'environments' / f'{self.environment}.yaml'
        if env_config_path.exists():
            with open(env_config_path, 'r', encoding='utf-8') as f:
                env_config = yaml.safe_load(f) or {}
                self._merge_config(self._config, env_config)
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def _merge_config(self, base: Dict, override: Dict) -> None:
        """合并配置"""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
```

**事件系统**：
```python
# src/core/events.py
from typing import Dict, List, Callable, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
import asyncio

@dataclass
class Event:
    """事件基类"""
    name: str
    data: Dict[str, Any]
    timestamp: datetime
    source: str

class EventHandler(ABC):
    """事件处理器接口"""
    
    @abstractmethod
    async def handle(self, event: Event) -> None:
        """处理事件"""
        pass

class EventBus:
    """事件总线"""
    
    def __init__(self):
        self._handlers: Dict[str, List[EventHandler]] = {}
    
    def subscribe(self, event_name: str, handler: EventHandler) -> None:
        """订阅事件"""
        if event_name not in self._handlers:
            self._handlers[event_name] = []
        self._handlers[event_name].append(handler)
    
    async def publish(self, event: Event) -> None:
        """发布事件"""
        if event.name in self._handlers:
            tasks = [
                handler.handle(event) 
                for handler in self._handlers[event.name]
            ]
            await asyncio.gather(*tasks, return_exceptions=True)
```

#### 1.3 开发工具配置

**Pre-commit配置**：
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: debug-statements
  
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3.11
  
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]
  
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        additional_dependencies: [flake8-docstrings]
  
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
```

**项目配置**：
```toml
# pyproject.toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-video-generator"
version = "2.0.0"
description = "AI-powered video generation tool"
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "PyQt6>=6.5.0",
    "aiohttp>=3.8.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.11.0",
    "pydantic>=2.0.0",
    "pillow>=10.0.0",
    "numpy>=1.24.0",
    "moviepy>=1.0.3",
    "pydub>=0.25.1",
    "edge-tts>=6.1.0",
    "pyyaml>=6.0",
    "click>=8.1.0",
    "rich>=13.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0",
    "pytest-cov>=4.0",
    "pytest-asyncio>=0.21",
    "pytest-qt>=4.2.0",
    "black>=23.0",
    "isort>=5.0",
    "flake8>=6.0",
    "mypy>=1.0",
    "pre-commit>=3.0",
]
docs = [
    "sphinx>=6.0",
    "sphinx-rtd-theme>=1.0",
    "myst-parser>=1.0",
]

[project.scripts]
ai-video-generator = "src.main:main"

[tool.black]
line-length = 127
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 127
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m "not slow"')",
    "integration: marks tests as integration tests",
    "ui: marks tests as UI tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
]
show_missing = true
skip_covered = false
fail_under = 90
```

---

### 第二阶段：核心业务逻辑（2-3周）

#### 2.1 数据模型层

**基础模型**：
```python
# src/models/base_model.py
from typing import Any, Dict, Optional
from datetime import datetime
from uuid import UUID, uuid4
from pydantic import BaseModel, Field
from sqlalchemy import Column, String, DateTime, Text
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()

class BaseEntity(Base):
    """数据库实体基类"""
    __abstract__ = True
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class BaseDomainModel(BaseModel):
    """领域模型基类"""
    id: UUID = Field(default_factory=uuid4)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }
```

**项目模型**：
```python
# src/models/project.py
from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID
from pydantic import Field
from sqlalchemy import Column, String, Text, JSON, Boolean
from sqlalchemy.orm import relationship

from .base_model import BaseEntity, BaseDomainModel

class ProjectEntity(BaseEntity):
    """项目数据库实体"""
    __tablename__ = 'projects'
    
    name = Column(String(255), nullable=False)
    description = Column(Text)
    settings = Column(JSON)
    is_active = Column(Boolean, default=True)
    
    # 关系
    storyboards = relationship("StoryboardEntity", back_populates="project")

class ProjectSettings(BaseDomainModel):
    """项目设置"""
    language: str = Field(default="zh-CN", description="项目语言")
    style: str = Field(default="cinematic", description="默认风格")
    resolution: str = Field(default="1920x1080", description="视频分辨率")
    fps: int = Field(default=24, description="帧率")
    duration_per_shot: int = Field(default=5, description="每个镜头默认时长（秒）")
    voice_settings: Dict[str, Any] = Field(default_factory=dict, description="语音设置")
    image_settings: Dict[str, Any] = Field(default_factory=dict, description="图像设置")
    video_settings: Dict[str, Any] = Field(default_factory=dict, description="视频设置")

class Project(BaseDomainModel):
    """项目领域模型"""
    name: str = Field(..., description="项目名称")
    description: Optional[str] = Field(None, description="项目描述")
    settings: ProjectSettings = Field(default_factory=ProjectSettings, description="项目设置")
    is_active: bool = Field(default=True, description="是否激活")
    
    # 统计信息
    storyboard_count: int = Field(default=0, description="分镜数量")
    total_shots: int = Field(default=0, description="总镜头数")
    generated_images: int = Field(default=0, description="已生成图像数")
    generated_videos: int = Field(default=0, description="已生成视频数")
```

**分镜模型**：
```python
# src/models/storyboard.py
from typing import List, Optional, Dict, Any
from enum import Enum
from uuid import UUID
from pydantic import Field
from sqlalchemy import Column, String, Text, JSON, Integer, ForeignKey, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import relationship

from .base_model import BaseEntity, BaseDomainModel

class StoryboardStatus(str, Enum):
    """分镜状态"""
    DRAFT = "draft"          # 草稿
    PROCESSING = "processing" # 处理中
    COMPLETED = "completed"   # 已完成
    FAILED = "failed"        # 失败

class StoryboardEntity(BaseEntity):
    """分镜数据库实体"""
    __tablename__ = 'storyboards'
    
    project_id = Column(PGUUID(as_uuid=True), ForeignKey('projects.id'), nullable=False)
    title = Column(String(255), nullable=False)
    original_text = Column(Text, nullable=False)
    processed_text = Column(Text)
    status = Column(SQLEnum(StoryboardStatus), default=StoryboardStatus.DRAFT)
    metadata = Column(JSON)
    
    # 关系
    project = relationship("ProjectEntity", back_populates="storyboards")
    shots = relationship("ShotEntity", back_populates="storyboard")

class StoryboardMetadata(BaseDomainModel):
    """分镜元数据"""
    style: str = Field(..., description="风格")
    language: str = Field(..., description="语言")
    processing_time: Optional[float] = Field(None, description="处理时间（秒）")
    word_count: int = Field(default=0, description="字数")
    estimated_duration: int = Field(default=0, description="预估时长（秒）")
    tags: List[str] = Field(default_factory=list, description="标签")
    characters: List[str] = Field(default_factory=list, description="角色列表")
    scenes: List[str] = Field(default_factory=list, description="场景列表")

class Storyboard(BaseDomainModel):
    """分镜领域模型"""
    project_id: UUID = Field(..., description="所属项目ID")
    title: str = Field(..., description="分镜标题")
    original_text: str = Field(..., description="原始文本")
    processed_text: Optional[str] = Field(None, description="处理后文本")
    status: StoryboardStatus = Field(default=StoryboardStatus.DRAFT, description="状态")
    metadata: StoryboardMetadata = Field(..., description="元数据")
    shots: List['Shot'] = Field(default_factory=list, description="镜头列表")
```

**镜头模型**：
```python
# src/models/shot.py
from typing import Optional, Dict, Any
from enum import Enum
from uuid import UUID
from pydantic import Field, HttpUrl
from sqlalchemy import Column, String, Text, JSON, Integer, Float, ForeignKey, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import relationship

from .base_model import BaseEntity, BaseDomainModel

class ShotType(str, Enum):
    """镜头类型"""
    WIDE = "wide"           # 远景
    MEDIUM = "medium"       # 中景
    CLOSE = "close"         # 近景
    EXTREME_CLOSE = "extreme_close" # 特写
    ESTABLISHING = "establishing"   # 建立镜头

class ShotStatus(str, Enum):
    """镜头状态"""
    PENDING = "pending"     # 待处理
    GENERATING = "generating" # 生成中
    COMPLETED = "completed" # 已完成
    FAILED = "failed"       # 失败

class ShotEntity(BaseEntity):
    """镜头数据库实体"""
    __tablename__ = 'shots'
    
    storyboard_id = Column(PGUUID(as_uuid=True), ForeignKey('storyboards.id'), nullable=False)
    sequence = Column(Integer, nullable=False)
    description = Column(Text, nullable=False)
    image_prompt = Column(Text)
    voice_text = Column(Text)
    shot_type = Column(SQLEnum(ShotType), default=ShotType.MEDIUM)
    status = Column(SQLEnum(ShotStatus), default=ShotStatus.PENDING)
    duration = Column(Float, default=5.0)
    
    # 生成的媒体文件
    image_url = Column(String(500))
    voice_url = Column(String(500))
    video_url = Column(String(500))
    
    # 元数据
    metadata = Column(JSON)
    
    # 关系
    storyboard = relationship("StoryboardEntity", back_populates="shots")

class ShotMetadata(BaseDomainModel):
    """镜头元数据"""
    characters: List[str] = Field(default_factory=list, description="出现的角色")
    objects: List[str] = Field(default_factory=list, description="物体")
    location: Optional[str] = Field(None, description="地点")
    time_of_day: Optional[str] = Field(None, description="时间")
    mood: Optional[str] = Field(None, description="情绪")
    camera_angle: Optional[str] = Field(None, description="摄像机角度")
    lighting: Optional[str] = Field(None, description="光照")
    color_palette: List[str] = Field(default_factory=list, description="色彩")
    generation_params: Dict[str, Any] = Field(default_factory=dict, description="生成参数")

class Shot(BaseDomainModel):
    """镜头领域模型"""
    storyboard_id: UUID = Field(..., description="所属分镜ID")
    sequence: int = Field(..., description="序号")
    description: str = Field(..., description="描述")
    image_prompt: Optional[str] = Field(None, description="图像提示词")
    voice_text: Optional[str] = Field(None, description="语音文本")
    shot_type: ShotType = Field(default=ShotType.MEDIUM, description="镜头类型")
    status: ShotStatus = Field(default=ShotStatus.PENDING, description="状态")
    duration: float = Field(default=5.0, description="时长（秒）")
    
    # 生成的媒体文件
    image_url: Optional[HttpUrl] = Field(None, description="图像URL")
    voice_url: Optional[HttpUrl] = Field(None, description="语音URL")
    video_url: Optional[HttpUrl] = Field(None, description="视频URL")
    
    # 元数据
    metadata: ShotMetadata = Field(default_factory=ShotMetadata, description="元数据")
```

#### 2.2 服务层抽象

**AI服务接口**：
```python
# src/services/ai_service.py
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, AsyncGenerator
from dataclasses import dataclass
from enum import Enum

class ServiceProvider(str, Enum):
    """服务提供商"""
    OPENAI = "openai"
    ZHIPU = "zhipu"
    QWEN = "qwen"
    DEEPSEEK = "deepseek"
    POLLINATIONS = "pollinations"
    STABILITY = "stability"
    COGVIDEOX = "cogvideox"
    REPLICATE = "replicate"

@dataclass
class GenerationRequest:
    """生成请求基类"""
    prompt: str
    model: Optional[str] = None
    parameters: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}

@dataclass
class GenerationResult:
    """生成结果基类"""
    success: bool
    data: Any
    error: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class AIServiceInterface(ABC):
    """AI服务接口"""
    
    @property
    @abstractmethod
    def provider(self) -> ServiceProvider:
        """服务提供商"""
        pass
    
    @property
    @abstractmethod
    def service_type(self) -> str:
        """服务类型"""
        pass
    
    @abstractmethod
    async def is_available(self) -> bool:
        """检查服务是否可用"""
        pass
    
    @abstractmethod
    async def generate(self, request: GenerationRequest) -> GenerationResult:
        """生成内容"""
        pass
    
    @abstractmethod
    async def generate_stream(self, request: GenerationRequest) -> AsyncGenerator[str, None]:
        """流式生成内容"""
        pass

class LLMServiceInterface(AIServiceInterface):
    """大语言模型服务接口"""
    
    @property
    def service_type(self) -> str:
        return "llm"
    
    @abstractmethod
    async def generate_text(self, prompt: str, **kwargs) -> GenerationResult:
        """生成文本"""
        pass
    
    @abstractmethod
    async def generate_storyboard(self, text: str, style: str, **kwargs) -> GenerationResult:
        """生成分镜"""
        pass

class ImageServiceInterface(AIServiceInterface):
    """图像生成服务接口"""
    
    @property
    def service_type(self) -> str:
        return "image"
    
    @abstractmethod
    async def generate_image(self, prompt: str, **kwargs) -> GenerationResult:
        """生成图像"""
        pass
    
    @abstractmethod
    async def enhance_prompt(self, prompt: str, **kwargs) -> str:
        """增强提示词"""
        pass

class VideoServiceInterface(AIServiceInterface):
    """视频生成服务接口"""
    
    @property
    def service_type(self) -> str:
        return "video"
    
    @abstractmethod
    async def generate_video(self, prompt: str, image_url: Optional[str] = None, **kwargs) -> GenerationResult:
        """生成视频"""
        pass

class VoiceServiceInterface(AIServiceInterface):
    """语音生成服务接口"""
    
    @property
    def service_type(self) -> str:
        return "voice"
    
    @abstractmethod
    async def generate_speech(self, text: str, voice: str, **kwargs) -> GenerationResult:
        """生成语音"""
        pass
    
    @abstractmethod
    async def list_voices(self) -> List[Dict[str, Any]]:
        """获取可用语音列表"""
        pass
```

**服务注册中心**：
```python
# src/services/service_registry.py
from typing import Dict, List, Optional, Type
from .ai_service import AIServiceInterface, ServiceProvider

class ServiceRegistry:
    """服务注册中心"""
    
    def __init__(self):
        self._services: Dict[str, Dict[str, AIServiceInterface]] = {
            "llm": {},
            "image": {},
            "video": {},
            "voice": {}
        }
        self._default_providers: Dict[str, str] = {}
    
    def register_service(
        self, 
        service_type: str, 
        provider: str, 
        service: AIServiceInterface
    ) -> None:
        """注册服务"""
        if service_type not in self._services:
            self._services[service_type] = {}
        
        self._services[service_type][provider] = service
    
    def get_service(
        self, 
        service_type: str, 
        provider: Optional[str] = None
    ) -> Optional[AIServiceInterface]:
        """获取服务"""
        if service_type not in self._services:
            return None
        
        if provider is None:
            provider = self._default_providers.get(service_type)
        
        if provider is None:
            # 返回第一个可用的服务
            services = self._services[service_type]
            if services:
                return next(iter(services.values()))
            return None
        
        return self._services[service_type].get(provider)
    
    def set_default_provider(self, service_type: str, provider: str) -> None:
        """设置默认服务提供商"""
        self._default_providers[service_type] = provider
    
    def list_providers(self, service_type: str) -> List[str]:
        """列出服务提供商"""
        return list(self._services.get(service_type, {}).keys())
    
    async def check_service_health(self) -> Dict[str, Dict[str, bool]]:
        """检查服务健康状态"""
        health_status = {}
        
        for service_type, providers in self._services.items():
            health_status[service_type] = {}
            for provider, service in providers.items():
                try:
                    is_available = await service.is_available()
                    health_status[service_type][provider] = is_available
                except Exception:
                    health_status[service_type][provider] = False
        
        return health_status
```

#### 2.3 业务逻辑处理器

**分镜生成器**：
```python
# src/processors/storyboard_generator.py
from typing import List, Dict, Any, Optional
from uuid import UUID
import re
import asyncio
from datetime import datetime

from ..models.storyboard import Storyboard, StoryboardMetadata, StoryboardStatus
from ..models.shot import Shot, ShotMetadata, ShotType
from ..services.ai_service import LLMServiceInterface, GenerationRequest
from ..core.events import EventBus, Event

class StoryboardGenerator:
    """分镜生成器"""
    
    def __init__(
        self, 
        llm_service: LLMServiceInterface,
        event_bus: EventBus
    ):
        self.llm_service = llm_service
        self.event_bus = event_bus
    
    async def generate_storyboard(
        self, 
        project_id: UUID,
        title: str,
        text: str, 
        style: str = "cinematic",
        language: str = "zh-CN"
    ) -> Storyboard:
        """生成分镜"""
        
        # 发布开始事件
        await self.event_bus.publish(Event(
            name="storyboard.generation.started",
            data={"project_id": str(project_id), "title": title},
            timestamp=datetime.now(),
            source="StoryboardGenerator"
        ))
        
        try:
            # 第一阶段：文本预处理
            processed_text = await self._preprocess_text(text)
            
            # 第二阶段：生成分镜描述
            storyboard_data = await self._generate_storyboard_structure(
                processed_text, style, language
            )
            
            # 第三阶段：解析分镜数据
            shots = await self._parse_shots(storyboard_data)
            
            # 第四阶段：增强镜头描述
            enhanced_shots = await self._enhance_shots(shots, style)
            
            # 第五阶段：生成元数据
            metadata = await self._generate_metadata(
                processed_text, enhanced_shots, style, language
            )
            
            # 创建分镜对象
            storyboard = Storyboard(
                project_id=project_id,
                title=title,
                original_text=text,
                processed_text=processed_text,
                status=StoryboardStatus.COMPLETED,
                metadata=metadata,
                shots=enhanced_shots
            )
            
            # 发布完成事件
            await self.event_bus.publish(Event(
                name="storyboard.generation.completed",
                data={
                    "project_id": str(project_id),
                    "storyboard_id": str(storyboard.id),
                    "shot_count": len(enhanced_shots)
                },
                timestamp=datetime.now(),
                source="StoryboardGenerator"
            ))
            
            return storyboard
            
        except Exception as e:
            # 发布失败事件
            await self.event_bus.publish(Event(
                name="storyboard.generation.failed",
                data={
                    "project_id": str(project_id),
                    "error": str(e)
                },
                timestamp=datetime.now(),
                source="StoryboardGenerator"
            ))
            raise
    
    async def _preprocess_text(self, text: str) -> str:
        """文本预处理"""
        # 清理文本
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 分段处理
        paragraphs = text.split('\n')
        processed_paragraphs = []
        
        for paragraph in paragraphs:
            if paragraph.strip():
                processed_paragraphs.append(paragraph.strip())
        
        return '\n'.join(processed_paragraphs)
    
    async def _generate_storyboard_structure(
        self, 
        text: str, 
        style: str, 
        language: str
    ) -> str:
        """生成分镜结构"""
        
        prompt = self._build_storyboard_prompt(text, style, language)
        
        request = GenerationRequest(
            prompt=prompt,
            parameters={
                "max_tokens": 4000,
                "temperature": 0.7,
                "top_p": 0.9
            }
        )
        
        result = await self.llm_service.generate(request)
        
        if not result.success:
            raise Exception(f"Failed to generate storyboard: {result.error}")
        
        return result.data
    
    def _build_storyboard_prompt(self, text: str, style: str, language: str) -> str:
        """构建分镜提示词"""
        
        style_descriptions = {
            "cinematic": "电影级别的视觉效果，注重光影和构图",
            "anime": "动漫风格，色彩鲜艳，表情丰富",
            "realistic": "写实风格，注重细节和真实感",
            "artistic": "艺术风格，富有创意和想象力"
        }
        
        style_desc = style_descriptions.get(style, "电影级别的视觉效果")
        
        return f"""
你是一个专业的分镜师，需要将以下文本转换为详细的分镜脚本。

风格要求：{style_desc}
语言：{language}

原文本：
{text}

请按照以下格式输出分镜脚本：

镜头1：
描述：[详细的视觉描述]
类型：[wide/medium/close/extreme_close/establishing]
时长：[秒数]
语音：[如果有对话或旁白]

镜头2：
...

要求：
1. 每个镜头都要有清晰的视觉描述
2. 合理安排镜头类型和时长
3. 保持故事的连贯性和节奏感
4. 描述要具体，便于图像生成
5. 总镜头数控制在5-20个之间
"""
    
    async def _parse_shots(self, storyboard_data: str) -> List[Shot]:
        """解析镜头数据"""
        shots = []
        
        # 使用正则表达式解析镜头
        shot_pattern = r'镜头(\d+)：\s*\n描述：([^\n]+)\n类型：([^\n]+)\n时长：([^\n]+)\n(?:语音：([^\n]+)\n)?'
        
        matches = re.findall(shot_pattern, storyboard_data, re.MULTILINE)
        
        for match in matches:
            sequence = int(match[0])
            description = match[1].strip()
            shot_type_str = match[2].strip().lower()
            duration_str = match[3].strip()
            voice_text = match[4].strip() if len(match) > 4 and match[4] else None
            
            # 解析镜头类型
            shot_type = self._parse_shot_type(shot_type_str)
            
            # 解析时长
            duration = self._parse_duration(duration_str)
            
            shot = Shot(
                storyboard_id=UUID('00000000-0000-0000-0000-000000000000'),  # 临时ID
                sequence=sequence,
                description=description,
                voice_text=voice_text,
                shot_type=shot_type,
                duration=duration
            )
            
            shots.append(shot)
        
        return shots
    
    def _parse_shot_type(self, shot_type_str: str) -> ShotType:
        """解析镜头类型"""
        type_mapping = {
            "wide": ShotType.WIDE,
            "远景": ShotType.WIDE,
            "medium": ShotType.MEDIUM,
            "中景": ShotType.MEDIUM,
            "close": ShotType.CLOSE,
            "近景": ShotType.CLOSE,
            "extreme_close": ShotType.EXTREME_CLOSE,
            "特写": ShotType.EXTREME_CLOSE,
            "establishing": ShotType.ESTABLISHING,
            "建立": ShotType.ESTABLISHING
        }
        
        return type_mapping.get(shot_type_str, ShotType.MEDIUM)
    
    def _parse_duration(self, duration_str: str) -> float:
        """解析时长"""
        # 提取数字
        import re
        numbers = re.findall(r'\d+(?:\.\d+)?', duration_str)
        if numbers:
            return float(numbers[0])
        return 5.0  # 默认5秒
    
    async def _enhance_shots(self, shots: List[Shot], style: str) -> List[Shot]:
        """增强镜头描述"""
        enhanced_shots = []
        
        for shot in shots:
            # 生成图像提示词
            image_prompt = await self._generate_image_prompt(shot.description, style)
            
            # 分析镜头元数据
            metadata = await self._analyze_shot_metadata(shot.description)
            
            enhanced_shot = Shot(
                storyboard_id=shot.storyboard_id,
                sequence=shot.sequence,
                description=shot.description,
                image_prompt=image_prompt,
                voice_text=shot.voice_text,
                shot_type=shot.shot_type,
                duration=shot.duration,
                metadata=metadata
            )
            
            enhanced_shots.append(enhanced_shot)
        
        return enhanced_shots
    
    async def _generate_image_prompt(self, description: str, style: str) -> str:
        """生成图像提示词"""
        
        style_prompts = {
            "cinematic": "cinematic lighting, film grain, depth of field, professional photography",
            "anime": "anime style, vibrant colors, detailed illustration, manga art",
            "realistic": "photorealistic, high detail, natural lighting, 8k resolution",
            "artistic": "artistic style, creative composition, unique perspective, masterpiece"
        }
        
        style_prompt = style_prompts.get(style, style_prompts["cinematic"])
        
        prompt = f"""
将以下场景描述转换为详细的图像生成提示词：

场景描述：{description}
风格：{style}

请生成一个详细的英文提示词，包含：
1. 主要内容和构图
2. 风格特征
3. 光照和色彩
4. 画质要求

提示词：
"""
        
        request = GenerationRequest(
            prompt=prompt,
            parameters={
                "max_tokens": 200,
                "temperature": 0.8
            }
        )
        
        result = await self.llm_service.generate(request)
        
        if result.success:
            generated_prompt = result.data.strip()
            return f"{generated_prompt}, {style_prompt}"
        else:
            # 回退到基础提示词
            return f"{description}, {style_prompt}"
    
    async def _analyze_shot_metadata(self, description: str) -> ShotMetadata:
        """分析镜头元数据"""
        
        # 使用LLM分析镜头内容
        analysis_prompt = f"""
分析以下镜头描述，提取关键信息：

描述：{description}

请以JSON格式返回以下信息：
{{
    "characters": ["角色1", "角色2"],
    "objects": ["物体1", "物体2"],
    "location": "地点",
    "time_of_day": "时间",
    "mood": "情绪",
    "camera_angle": "摄像机角度",
    "lighting": "光照",
    "color_palette": ["颜色1", "颜色2"]
}}
"""
        
        request = GenerationRequest(
            prompt=analysis_prompt,
            parameters={
                "max_tokens": 300,
                "temperature": 0.3
            }
        )
        
        result = await self.llm_service.generate(request)
        
        if result.success:
            try:
                import json
                analysis_data = json.loads(result.data)
                return ShotMetadata(**analysis_data)
            except (json.JSONDecodeError, TypeError):
                pass
        
        # 回退到默认元数据
        return ShotMetadata()
    
    async def _generate_metadata(
        self, 
        text: str, 
        shots: List[Shot], 
        style: str, 
        language: str
    ) -> StoryboardMetadata:
        """生成分镜元数据"""
        
        # 统计信息
        word_count = len(text)
        estimated_duration = sum(shot.duration for shot in shots)
        
        # 提取角色和场景
        all_characters = set()
        all_scenes = set()
        
        for shot in shots:
            all_characters.update(shot.metadata.characters)
            if shot.metadata.location:
                all_scenes.add(shot.metadata.location)
        
        return StoryboardMetadata(
            style=style,
            language=language,
            word_count=word_count,
            estimated_duration=int(estimated_duration),
            characters=list(all_characters),
            scenes=list(all_scenes)
        )
```

---

### 第三阶段：现代化UI开发（2-3周）

#### 3.1 UI架构设计

**主窗口架构**：
```python
# src/ui/main_window.py
from typing import Optional
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QSplitter, QStackedWidget, QMenuBar, QStatusBar,
    QToolBar, QLabel, QPushButton
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon, QAction

from ..controllers.app_controller import AppController
from .components.navigation_panel import NavigationPanel
from .components.content_area import ContentArea
from .components.info_panel import InfoPanel
from .components.status_bar import ModernStatusBar
from .themes.theme_manager import ThemeManager

class MainWindow(QMainWindow):
    """主窗口"""
    
    # 信号定义
    theme_changed = pyqtSignal(str)
    window_state_changed = pyqtSignal(str)
    
    def __init__(self, app_controller: AppController):
        super().__init__()
        
        self.app_controller = app_controller
        self.theme_manager = ThemeManager()
        
        self._setup_window()
        self._setup_ui()
        self._setup_menu_bar()
        self._setup_tool_bar()
        self._setup_status_bar()
        self._setup_signals()
        self._apply_theme()
        
        # 启动定时器用于状态更新
        self._status_timer = QTimer()
        self._status_timer.timeout.connect(self._update_status)
        self._status_timer.start(1000)  # 每秒更新一次
    
    def _setup_window(self) -> None:
        """设置窗口属性"""
        self.setWindowTitle("AI视频生成器 2.0")
        self.setMinimumSize(1200, 800)
        self.resize(1600, 1000)
        
        # 设置窗口图标
        self.setWindowIcon(QIcon("assets/icons/app_icon.png"))
        
        # 设置窗口属性
        self.setDockNestingEnabled(True)
        self.setAnimated(True)
    
    def _setup_ui(self) -> None:
        """设置UI布局"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(main_splitter)
        
        # 左侧导航面板
        self.navigation_panel = NavigationPanel(self.app_controller)
        main_splitter.addWidget(self.navigation_panel)
        
        # 中央内容区域
        self.content_area = ContentArea(self.app_controller)
        main_splitter.addWidget(self.content_area)
        
        # 右侧信息面板
        self.info_panel = InfoPanel(self.app_controller)
        main_splitter.addWidget(self.info_panel)
        
        # 设置分割器比例
        main_splitter.setSizes([250, 1000, 350])
        main_splitter.setCollapsible(0, True)
        main_splitter.setCollapsible(2, True)
    
    def _setup_menu_bar(self) -> None:
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件")
        
        new_project_action = QAction("新建项目", self)
        new_project_action.setShortcut("Ctrl+N")
        new_project_action.triggered.connect(self._on_new_project)
        file_menu.addAction(new_project_action)
        
        open_project_action = QAction("打开项目", self)
        open_project_action.setShortcut("Ctrl+O")
        open_project_action.triggered.connect(self._on_open_project)
        file_menu.addAction(open_project_action)
        
        file_menu.addSeparator()
        
        save_action = QAction("保存", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self._on_save)
        file_menu.addAction(save_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu("编辑")
        
        undo_action = QAction("撤销", self)
        undo_action.setShortcut("Ctrl+Z")
        edit_menu.addAction(undo_action)
        
        redo_action = QAction("重做", self)
        redo_action.setShortcut("Ctrl+Y")
        edit_menu.addAction(redo_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("视图")
        
        toggle_nav_action = QAction("显示/隐藏导航面板", self)
        toggle_nav_action.setCheckable(True)
        toggle_nav_action.setChecked(True)
        toggle_nav_action.triggered.connect(self._toggle_navigation_panel)
        view_menu.addAction(toggle_nav_action)
        
        toggle_info_action = QAction("显示/隐藏信息面板", self)
        toggle_info_action.setCheckable(True)
        toggle_info_action.setChecked(True)
        toggle_info_action.triggered.connect(self._toggle_info_panel)
        view_menu.addAction(toggle_info_action)
        
        view_menu.addSeparator()
        
        # 主题子菜单
        theme_menu = view_menu.addMenu("主题")
        
        light_theme_action = QAction("浅色主题", self)
        light_theme_action.triggered.connect(lambda: self._change_theme("light"))
        theme_menu.addAction(light_theme_action)
        
        dark_theme_action = QAction("深色主题", self)
        dark_theme_action.triggered.connect(lambda: self._change_theme("dark"))
        theme_menu.addAction(dark_theme_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具")
        
        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self._open_settings)
        tools_menu.addAction(settings_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助")
        
        about_action = QAction("关于", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
    
    def _setup_tool_bar(self) -> None:
        """设置工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setMovable(False)
        
        # 新建项目按钮
        new_btn = QPushButton("新建项目")
        new_btn.clicked.connect(self._on_new_project)
        toolbar.addWidget(new_btn)
        
        # 打开项目按钮
        open_btn = QPushButton("打开项目")
        open_btn.clicked.connect(self._on_open_project)
        toolbar.addWidget(open_btn)
        
        toolbar.addSeparator()
        
        # 生成分镜按钮
        generate_btn = QPushButton("生成分镜")
        generate_btn.clicked.connect(self._on_generate_storyboard)
        toolbar.addWidget(generate_btn)
    
    def _setup_status_bar(self) -> None:
        """设置状态栏"""
        self.status_bar = ModernStatusBar()
        self.setStatusBar(self.status_bar)
    
    def _setup_signals(self) -> None:
        """设置信号连接"""
        # 导航面板信号
        self.navigation_panel.page_changed.connect(self.content_area.switch_page)
        
        # 内容区域信号
        self.content_area.status_message.connect(self.status_bar.show_message)
        
        # 应用控制器信号
        self.app_controller.project_loaded.connect(self._on_project_loaded)
        self.app_controller.project_saved.connect(self._on_project_saved)
    
    def _apply_theme(self) -> None:
        """应用主题"""
        theme = self.theme_manager.get_current_theme()
        self.setStyleSheet(theme.get_stylesheet())
    
    def _update_status(self) -> None:
        """更新状态信息"""
        # 更新项目状态
        if self.app_controller.current_project:
            project = self.app_controller.current_project
            self.status_bar.update_project_info(
                project.name,
                project.storyboard_count,
                project.total_shots
            )
    
    # 事件处理方法
    def _on_new_project(self) -> None:
        """新建项目"""
        self.content_area.show_new_project_dialog()
    
    def _on_open_project(self) -> None:
        """打开项目"""
        self.content_area.show_open_project_dialog()
    
    def _on_save(self) -> None:
        """保存项目"""
        if self.app_controller.current_project:
            self.app_controller.save_current_project()
    
    def _on_generate_storyboard(self) -> None:
        """生成分镜"""
        self.content_area.show_storyboard_generator()
    
    def _toggle_navigation_panel(self, checked: bool) -> None:
        """切换导航面板显示"""
        self.navigation_panel.setVisible(checked)
    
    def _toggle_info_panel(self, checked: bool) -> None:
        """切换信息面板显示"""
        self.info_panel.setVisible(checked)
    
    def _change_theme(self, theme_name: str) -> None:
        """更改主题"""
        self.theme_manager.set_theme(theme_name)
        self._apply_theme()
        self.theme_changed.emit(theme_name)
    
    def _open_settings(self) -> None:
        """打开设置"""
        self.content_area.show_settings_dialog()
    
    def _show_about(self) -> None:
        """显示关于对话框"""
        self.content_area.show_about_dialog()
    
    def _on_project_loaded(self, project_name: str) -> None:
        """项目加载完成"""
        self.setWindowTitle(f"AI视频生成器 2.0 - {project_name}")
        self.status_bar.show_message(f"项目 '{project_name}' 加载完成")
    
    def _on_project_saved(self, project_name: str) -> None:
        """项目保存完成"""
        self.status_bar.show_message(f"项目 '{project_name}' 保存完成")
```

#### 3.2 主题系统

**主题管理器**：
```python
# src/ui/themes/theme_manager.py
from typing import Dict, Optional
from pathlib import Path
from PyQt6.QtCore import QObject, pyqtSignal
from .base_theme import BaseTheme
from .light_theme import LightTheme
from .dark_theme import DarkTheme

class ThemeManager(QObject):
    """主题管理器"""
    
    theme_changed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        
        self._themes: Dict[str, BaseTheme] = {
            "light": LightTheme(),
            "dark": DarkTheme()
        }
        
        self._current_theme = "light"
    
    def get_current_theme(self) -> BaseTheme:
        """获取当前主题"""
        return self._themes[self._current_theme]
    
    def set_theme(self, theme_name: str) -> None:
        """设置主题"""
        if theme_name in self._themes:
            self._current_theme = theme_name
            self.theme_changed.emit(theme_name)
    
    def get_available_themes(self) -> list[str]:
        """获取可用主题列表"""
        return list(self._themes.keys())
    
    def register_theme(self, name: str, theme: BaseTheme) -> None:
        """注册新主题"""
        self._themes[name] = theme
```

**基础主题类**：
```python
# src/ui/themes/base_theme.py
from abc import ABC, abstractmethod
from typing import Dict, Any

class BaseTheme(ABC):
    """主题基类"""
    
    def __init__(self):
        self.colors = self._define_colors()
        self.fonts = self._define_fonts()
        self.sizes = self._define_sizes()
    
    @abstractmethod
    def _define_colors(self) -> Dict[str, str]:
        """定义颜色"""
        pass
    
    @abstractmethod
    def _define_fonts(self) -> Dict[str, str]:
        """定义字体"""
        pass
    
    @abstractmethod
    def _define_sizes(self) -> Dict[str, int]:
        """定义尺寸"""
        pass
    
    def get_stylesheet(self) -> str:
        """获取样式表"""
        return self._build_stylesheet()
    
    def _build_stylesheet(self) -> str:
        """构建样式表"""
        return f"""
        /* 主窗口样式 */
        QMainWindow {{
            background-color: {self.colors['background']};
            color: {self.colors['text']};
            font-family: {self.fonts['primary']};
            font-size: {self.sizes['font_base']}px;
        }}
        
        /* 按钮样式 */
        QPushButton {{
            background-color: {self.colors['button_bg']};
            color: {self.colors['button_text']};
            border: 1px solid {self.colors['border']};
            border-radius: {self.sizes['border_radius']}px;
            padding: {self.sizes['padding_small']}px {self.sizes['padding_medium']}px;
            font-weight: 500;
        }}
        
        QPushButton:hover {{
            background-color: {self.colors['button_hover']};
        }}
        
        QPushButton:pressed {{
            background-color: {self.colors['button_pressed']};
        }}
        
        QPushButton:disabled {{
            background-color: {self.colors['disabled']};
            color: {self.colors['text_disabled']};
        }}
        
        /* 输入框样式 */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background-color: {self.colors['input_bg']};
            color: {self.colors['text']};
            border: 1px solid {self.colors['border']};
            border-radius: {self.sizes['border_radius']}px;
            padding: {self.sizes['padding_small']}px;
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {self.colors['primary']};
        }}
        
        /* 面板样式 */
        QWidget[class="panel"] {{
            background-color: {self.colors['panel_bg']};
            border: 1px solid {self.colors['border']};
            border-radius: {self.sizes['border_radius']}px;
        }}
        
        /* 分割器样式 */
        QSplitter::handle {{
            background-color: {self.colors['splitter']};
        }}
        
        QSplitter::handle:horizontal {{
            width: 2px;
        }}
        
        QSplitter::handle:vertical {{
            height: 2px;
        }}
        
        /* 菜单样式 */
        QMenuBar {{
            background-color: {self.colors['menu_bg']};
            color: {self.colors['text']};
            border-bottom: 1px solid {self.colors['border']};
        }}
        
        QMenuBar::item {{
            padding: {self.sizes['padding_small']}px {self.sizes['padding_medium']}px;
        }}
        
        QMenuBar::item:selected {{
            background-color: {self.colors['menu_hover']};
        }}
        
        QMenu {{
            background-color: {self.colors['menu_bg']};
            color: {self.colors['text']};
            border: 1px solid {self.colors['border']};
        }}
        
        QMenu::item {{
            padding: {self.sizes['padding_small']}px {self.sizes['padding_medium']}px;
        }}
        
        QMenu::item:selected {{
            background-color: {self.colors['menu_hover']};
        }}
        
        /* 工具栏样式 */
        QToolBar {{
            background-color: {self.colors['toolbar_bg']};
            border: none;
            spacing: {self.sizes['spacing_small']}px;
        }}
        
        /* 状态栏样式 */
        QStatusBar {{
            background-color: {self.colors['statusbar_bg']};
            color: {self.colors['text']};
            border-top: 1px solid {self.colors['border']};
        }}
        
        /* 滚动条样式 */
        QScrollBar:vertical {{
            background-color: {self.colors['scrollbar_bg']};
            width: {self.sizes['scrollbar_width']}px;
            border-radius: {self.sizes['scrollbar_width'] // 2}px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {self.colors['scrollbar_handle']};
            border-radius: {self.sizes['scrollbar_width'] // 2}px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {self.colors['scrollbar_handle_hover']};
        }}
        
        QScrollBar:horizontal {{
            background-color: {self.colors['scrollbar_bg']};
            height: {self.sizes['scrollbar_width']}px;
            border-radius: {self.sizes['scrollbar_width'] // 2}px;
        }}
        
        QScrollBar::handle:horizontal {{
            background-color: {self.colors['scrollbar_handle']};
            border-radius: {self.sizes['scrollbar_width'] // 2}px;
            min-width: 20px;
        }}
        
        QScrollBar::handle:horizontal:hover {{
            background-color: {self.colors['scrollbar_handle_hover']};
        }}
        """
```

---

### 第四阶段：服务集成与优化（2-3周）

#### 4.1 LLM服务实现

**OpenAI服务**：
```python
# src/services/llm/openai_service.py
from typing import AsyncGenerator, Dict, Any
import aiohttp
import json
from ..ai_service import LLMServiceInterface, ServiceProvider, GenerationRequest, GenerationResult

class OpenAIService(LLMServiceInterface):
    """OpenAI LLM服务"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.openai.com/v1"):
        self.api_key = api_key
        self.base_url = base_url
        self.default_model = "gpt-4"
    
    @property
    def provider(self) -> ServiceProvider:
        return ServiceProvider.OPENAI
    
    async def is_available(self) -> bool:
        """检查服务可用性"""
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
                
                async with session.get(
                    f"{self.base_url}/models",
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
        except Exception:
            return False
    
    async def generate(self, request: GenerationRequest) -> GenerationResult:
        """生成文本"""
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
                
                data = {
                    "model": request.model or self.default_model,
                    "messages": [
                        {"role": "user", "content": request.prompt}
                    ],
                    **request.parameters
                }
                
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        content = result["choices"][0]["message"]["content"]
                        
                        return GenerationResult(
                            success=True,
                            data=content,
                            metadata={
                                "model": result.get("model"),
                                "usage": result.get("usage"),
                                "finish_reason": result["choices"][0].get("finish_reason")
                            }
                        )
                    else:
                        error_text = await response.text()
                        return GenerationResult(
                            success=False,
                            data=None,
                            error=f"API错误 {response.status}: {error_text}"
                        )
                        
        except Exception as e:
            return GenerationResult(
                success=False,
                data=None,
                error=f"请求失败: {str(e)}"
            )
    
    async def generate_stream(self, request: GenerationRequest) -> AsyncGenerator[str, None]:
        """流式生成文本"""
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
                
                data = {
                    "model": request.model or self.default_model,
                    "messages": [
                        {"role": "user", "content": request.prompt}
                    ],
                    "stream": True,
                    **request.parameters
                }
                
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=aiohttp.ClientTimeout(total=120)
                ) as response:
                    
                    if response.status == 200:
                        async for line in response.content:
                            line = line.decode('utf-8').strip()
                            if line.startswith('data: '):
                                data_str = line[6:]
                                if data_str == '[DONE]':
                                    break
                                
                                try:
                                    data = json.loads(data_str)
                                    if 'choices' in data and len(data['choices']) > 0:
                                        delta = data['choices'][0].get('delta', {})
                                        if 'content' in delta:
                                            yield delta['content']
                                except json.JSONDecodeError:
                                    continue
                    else:
                        error_text = await response.text()
                        yield f"错误: {response.status} - {error_text}"
                        
        except Exception as e:
            yield f"流式请求失败: {str(e)}"
    
    async def generate_text(self, prompt: str, **kwargs) -> GenerationResult:
        """生成文本"""
        request = GenerationRequest(prompt=prompt, parameters=kwargs)
        return await self.generate(request)
    
    async def generate_storyboard(self, text: str, style: str, **kwargs) -> GenerationResult:
        """生成分镜"""
        # 构建分镜生成提示词
        storyboard_prompt = self._build_storyboard_prompt(text, style)
        
        request = GenerationRequest(
            prompt=storyboard_prompt,
            parameters={
                "max_tokens": 4000,
                "temperature": 0.7,
                **kwargs
            }
        )
        
        return await self.generate(request)
    
    def _build_storyboard_prompt(self, text: str, style: str) -> str:
        """构建分镜提示词"""
        return f"""
你是一个专业的分镜师，需要将以下文本转换为详细的分镜脚本。

风格要求：{style}

原文本：
{text}

请按照以下格式输出分镜脚本：

镜头1：
描述：[详细的视觉描述]
类型：[wide/medium/close/extreme_close/establishing]
时长：[秒数]
语音：[如果有对话或旁白]

要求：
1. 每个镜头都要有清晰的视觉描述
2. 合理安排镜头类型和时长
3. 保持故事的连贯性和节奏感
4. 描述要具体，便于图像生成
5. 总镜头数控制在5-20个之间
"""
```

#### 4.2 图像服务实现

**Pollinations服务**：
```python
# src/services/image/pollinations_service.py
from typing import Optional
import aiohttp
import asyncio
from urllib.parse import quote
from ..ai_service import ImageServiceInterface, ServiceProvider, GenerationRequest, GenerationResult

class PollinationsService(ImageServiceInterface):
    """Pollinations图像生务"""
    
    def __init__(self):
        self.base_url = "https://image.pollinations.ai/prompt"
        self.default_params = {
            "width": 1024,
            "height": 1024,
            "seed": -1,
            "model": "flux"
        }
    
    @property
    def provider(self) -> ServiceProvider:
        return ServiceProvider.POLLINATIONS
    
    async def is_available(self) -> bool:
        """检查服务可用性"""
        try:
            async with aiohttp.ClientSession() as session:
                test_url = f"{self.base_url}/test?width=64&height=64"
                async with session.head(
                    test_url,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
        except Exception:
            return False
    
    async def generate(self, request: GenerationRequest) -> GenerationResult:
        """生成图像"""
        try:
            # 构建URL参数
            params = {**self.default_params, **request.parameters}
            
            # 编码提示词
            encoded_prompt = quote(request.prompt)
            
            # 构建完整URL
            url_params = "&".join([f"{k}={v}" for k, v in params.items()])
            image_url = f"{self.base_url}/{encoded_prompt}?{url_params}"
            
            # 请求图像
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    image_url,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    
                    if response.status == 200:
                        # 读取图像数据
                        image_data = await response.read()
                        
                        return GenerationResult(
                            success=True,
                            data={
                                "url": image_url,
                                "data": image_data,
                                "format": "png"
                            },
                            metadata={
                                "prompt": request.prompt,
                                "parameters": params,
                                "size": len(image_data)
                            }
                        )
                    else:
                        return GenerationResult(
                            success=False,
                            data=None,
                            error=f"图像生成失败: HTTP {response.status}"
                        )
                        
        except Exception as e:
            return GenerationResult(
                success=False,
                data=None,
                error=f"图像生成异常: {str(e)}"
            )
    
    async def generate_stream(self, request: GenerationRequest):
        """Pollinations不支持流式生成"""
        result = await self.generate(request)
        if result.success:
            yield result.data
        else:
            yield f"错误: {result.error}"
    
    async def generate_image(self, prompt: str, **kwargs) -> GenerationResult:
        """生成图像"""
        request = GenerationRequest(prompt=prompt, parameters=kwargs)
        return await self.generate(request)
    
    async def enhance_prompt(self, prompt: str, **kwargs) -> str:
        """增强提示词"""
        # 添加质量增强词
        quality_terms = [
            "high quality", "detailed", "professional", 
            "8k resolution", "masterpiece"
        ]
        
        enhanced_prompt = prompt
        
        # 根据参数添加风格词
        style = kwargs.get("style", "realistic")
        if style == "cinematic":
            enhanced_prompt += ", cinematic lighting, film grain, depth of field"
        elif style == "anime":
            enhanced_prompt += ", anime style, vibrant colors, detailed illustration"
        elif style == "artistic":
            enhanced_prompt += ", artistic style, creative composition, unique perspective"
        
        # 添加质量词
        enhanced_prompt += ", " + ", ".join(quality_terms)
        
        return enhanced_prompt
```

---

### 第五阶段：测试与文档完善（1-2周）

#### 5.1 测试策略

**单元测试示例**：
```python
# tests/unit/test_storyboard_generator.py
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from uuid import uuid4

from src.processors.storyboard_generator import StoryboardGenerator
from src.models.storyboard import StoryboardStatus
from src.services.ai_service import GenerationResult
from src.core.events import EventBus

class TestStoryboardGenerator:
    """分镜生成器测试"""
    
    @pytest.fixture
    def mock_llm_service(self):
        """模拟LLM服务"""
        service = Mock()
        service.generate = AsyncMock()
        return service
    
    @pytest.fixture
    def event_bus(self):
        """事件总线"""
        return EventBus()
    
    @pytest.fixture
    def generator(self, mock_llm_service, event_bus):
        """分镜生成器"""
        return StoryboardGenerator(mock_llm_service, event_bus)
    
    @pytest.mark.asyncio
    async def test_generate_storyboard_success(self, generator, mock_llm_service):
        """测试成功生成分镜"""
        # 准备测试数据
        project_id = uuid4()
        title = "测试分镜"
        text = "一个关于冒险的故事"
        
        # 模拟LLM响应
        mock_response = """
镜头1：
描述：主角站在山顶眺望远方
类型：wide
时长：5
语音：这是一个新的开始

镜头2：
描述：主角的特写镜头，眼神坚定
类型：close
时长：3
语音：我要去寻找传说中的宝藏
"""
        
        mock_llm_service.generate.return_value = GenerationResult(
            success=True,
            data=mock_response
        )
        
        # 执行测试
        result = await generator.generate_storyboard(
            project_id=project_id,
            title=title,
            text=text,
            style="cinematic"
        )
        
        # 验证结果
        assert result.project_id == project_id
        assert result.title == title
        assert result.original_text == text
        assert result.status == StoryboardStatus.COMPLETED
        assert len(result.shots) == 2
        
        # 验证第一个镜头
        shot1 = result.shots[0]
        assert shot1.sequence == 1
        assert "主角站在山顶" in shot1.description
        assert shot1.duration == 5.0
        assert shot1.voice_text == "这是一个新的开始"
    
    @pytest.mark.asyncio
    async def test_generate_storyboard_llm_failure(self, generator, mock_llm_service):
        """测试LLM服务失败"""
        # 模拟LLM失败
        mock_llm_service.generate.return_value = GenerationResult(
            success=False,
            data=None,
            error="API限制"
        )
        
        # 执行测试并验证异常
        with pytest.raises(Exception) as exc_info:
            await generator.generate_storyboard(
                project_id=uuid4(),
                title="测试",
                text="测试文本"
            )
        
        assert "Failed to generate storyboard" in str(exc_info.value)
    
    def test_parse_shot_type(self, generator):
        """测试镜头类型解析"""
        assert generator._parse_shot_type("wide") == ShotType.WIDE
        assert generator._parse_shot_type("远景") == ShotType.WIDE
        assert generator._parse_shot_type("close") == ShotType.CLOSE
        assert generator._parse_shot_type("unknown") == ShotType.MEDIUM
    
    def test_parse_duration(self, generator):
        """测试时长解析"""
        assert generator._parse_duration("5秒") == 5.0
        assert generator._parse_duration("3.5") == 3.5
        assert generator._parse_duration("无效") == 5.0
```

**集成测试示例**：
```python
# tests/integration/test_project_workflow.py
import pytest
import asyncio
from pathlib import Path
from uuid import uuid4

from src.controllers.app_controller import AppController
from src.models.project import Project, ProjectSettings
from src.core.container import Container

class TestProjectWorkflow:
    """项目工作流集成测试"""
    
    @pytest.fixture
    async def app_controller(self, tmp_path):
        """应用控制器"""
        # 创建临时配置
        config_dir = tmp_path / "config"
        config_dir.mkdir()
        
        # 创建容器和控制器
        container = Container()
        controller = AppController(container, config_dir)
        
        await controller.initialize()
        return controller
    
    @pytest.mark.asyncio
    async def test_complete_project_workflow(self, app_controller):
        """测试完整的项目工作流"""
        # 1. 创建新项目
        project_data = {
            "name": "测试项目",
            "description": "这是一个测试项目",
            "settings": ProjectSettings()
        }
        
        project = await app_controller.create_project(project_data)
        assert project.name == "测试项目"
        
        # 2. 生成分镜
        storyboard_data = {
            "title": "测试分镜",
            "text": "一个简单的故事，主角去冒险。",
            "style": "cinematic"
        }
        
        storyboard = await app_controller.generate_storyboard(
            project.id, 
            storyboard_data
        )
        
        assert storyboard.title == "测试分镜"
        assert len(storyboard.shots) > 0
        
        # 3. 生成图像
        shot = storyboard.shots[0]
        image_result = await app_controller.generate_shot_image(
            shot.id,
            shot.image_prompt
        )
        
        assert image_result.success
        assert image_result.data is not None
        
        # 4. 保存项目
        save_result = await app_controller.save_project(project.id)
        assert save_result.success
        
        # 5. 加载项目
        loaded_project = await app_controller.load_project(project.id)
        assert loaded_project.name == project.name
        assert len(loaded_project.storyboards) == 1
```

#### 5.2 性能测试

**性能基准测试**：
```python
# tests/performance/test_performance.py
import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor

class TestPerformance:
    """性能测试"""
    
    @pytest.mark.asyncio
    async def test_storyboard_generation_performance(self, app_controller):
        """测试分镜生成性能"""
        project = await app_controller.create_project({
            "name": "性能测试项目",
            "description": "用于性能测试"
        })
        
        # 测试文本
        test_text = "这是一个长篇故事" * 100  # 模拟长文本
        
        start_time = time.time()
        
        storyboard = await app_controller.generate_storyboard(
            project.id,
            {
                "title": "性能测试分镜",
                "text": test_text,
                "style": "cinematic"
            }
        )
        
        end_time = time.time()
        generation_time = end_time - start_time
        
        # 性能断言
        assert generation_time < 30.0  # 应在30秒内完成
        assert len(storyboard.shots) > 0
        
        print(f"分镜生成时间: {generation_time:.2f}秒")
        print(f"生成镜头数: {len(storyboard.shots)}")
        print(f"平均每镜头时间: {generation_time/len(storyboard.shots):.2f}秒")
    
    @pytest.mark.asyncio
    async def test_concurrent_image_generation(self, app_controller):
        """测试并发图像生成性能"""
        # 创建测试数据
        prompts = [
            "a beautiful landscape",
            "a futuristic city",
            "a magical forest",
            "a space station",
            "an underwater scene"
        ]
        
        start_time = time.time()
        
        # 并发生成图像
        tasks = [
            app_controller.generate_image(prompt)
            for prompt in prompts
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 验证结果
        successful_results = [
            r for r in results 
            if not isinstance(r, Exception) and r.success
        ]
        
        assert len(successful_results) >= 3  # 至少3个成功
        assert total_time < 60.0  # 总时间不超过60秒
        
        print(f"并发图像生成时间: {total_time:.2f}秒")
        print(f"成功生成数量: {len(successful_results)}/{len(prompts)}")
        print(f"平均每图像时间: {total_time/len(prompts):.2f}秒")
```

---

## 📊 项目管理与质量保证

### 开发流程

1. **功能开发流程**：
   - 创建功能分支 (`feature/功能名称`)
   - 编写测试用例 (TDD方式)
   - 实现功能代码
   - 运行所有测试确保通过
   - 代码审查
   - 合并到主分支

2. **代码质量检查**：
   - Pre-commit钩子自动检查
   - 代码覆盖率必须 > 90%
   - 类型检查必须通过
   - 文档字符串完整性检查

3. **持续集成**：
   - 自动运行测试套件
   - 自动构建和打包
   - 自动部署到测试环境

### 里程碑检查点

**第一阶段完成标准**：
- [ ] 项目结构创建完成
- [ ] 依赖注入容器可用
- [ ] 配置管理系统可用
- [ ] 事件系统可用
- [ ] 基础测试框架搭建完成

**第二阶段完成标准**：
- [ ] 所有数据模型定义完成
- [ ] 服务接口定义完成
- [ ] 分镜生成器实现完成
- [ ] 核心业务逻辑测试覆盖率 > 90%

**第三阶段完成标准**：
- [ ] 主窗口UI实现完成
- [ ] 主题系统实现完成
- [ ] 所有主要UI组件实现完成
- [ ] UI响应性能测试通过

**第四阶段完成标准**：
- [ ] 至少2个LLM服务集成完成
- [ ] 至少2个图像服务集成完成
- [ ] 至少1个视频服务集成完成
- [ ] 至少1个语音服务集成完成
- [ ] 服务健康检查系统可用

**第五阶段完成标准**：
- [ ] 单元测试覆盖率 > 90%
- [ ] 集成测试覆盖主要工作流
- [ ] 性能测试通过基准要求
- [ ] 用户文档完成
- [ ] API文档完成

### 风险管理

**技术风险**：
- AI服务API变更：使用适配器模式隔离变化
- 性能瓶颈：提前进行性能测试和优化
- 依赖库兼容性：锁定版本并定期更新

**进度风险**：
- 功能复杂度超预期：采用MVP方式，优先核心功能
- 第三方服务不稳定：准备多个备选方案
- 测试时间不足：并行开发和测试

### 成功标准

**功能完整性**：
- 所有现有功能完全迁移
- 新增功能按计划实现
- 用户体验显著提升

**技术指标**：
- 启动时间 < 3秒
- 分镜生成时间 < 30秒
- 图像生成时间 < 60秒
- 内存使用 < 1GB
- 测试覆盖率 > 90%

**质量标准**：
- 零严重Bug
- 代码规范100%遵守
- 文档完整性100%
- 用户满意度 > 90%

---

## 🎯 总结

本实施计划为AI视频生成器2.0的开发提供了详细的路线图，涵盖了从基础架构到最终交付的全过程。通过分阶段实施、严格的质量控制和风险管理，确保项目能够按时、按质完成。

**关键成功因素**：
1. 严格遵循分层架构和设计原则
2. 保持高质量的代码和测试覆盖率
3. 持续的性能监控和优化
4. 充分的文档和用户支持
5. 灵活应对技术变化和需求调整

**预期成果**：
- 一个现代化、高性能的AI视频生成工具
- 优秀的用户体验和界面设计
- 可扩展和可维护的代码架构
- 完整的测试和文档体系
- 为未来功能扩展奠定坚实基础