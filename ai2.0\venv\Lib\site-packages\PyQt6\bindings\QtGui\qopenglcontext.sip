// qopenglcontext.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_OpenGL)

class QOpenGLContextGroup : public QObject
{
%TypeHeaderCode
#include <qopenglcontext.h>
%End

public:
    virtual ~QOpenGLContextGroup();
    QList<QOpenGLContext *> shares() const;
    static QOpenGLContextGroup *currentContextGroup();

private:
    QOpenGLContextGroup();
};

%End
%If (PyQt_OpenGL)

class QOpenGLContext : public QObject
{
%TypeHeaderCode
#include <qopenglcontext.h>
%End

public:
    explicit QOpenGLContext(QObject *parent /TransferThis/ = 0);
    virtual ~QOpenGLContext();
    void setFormat(const QSurfaceFormat &format);
    void setShareContext(QOpenGLContext *shareContext);
    void setScreen(QScreen *screen);
    bool create();
    bool isValid() const;
    QSurfaceFormat format() const;
    QOpenGLContext *shareContext() const;
    QOpenGLContextGroup *shareGroup() const;
    QScreen *screen() const;
    GLuint defaultFramebufferObject() const;
    bool makeCurrent(QSurface *surface);
    void doneCurrent();
    void swapBuffers(QSurface *surface);
    QFunctionPointer getProcAddress(const QByteArray &procName) const;
    QSurface *surface() const;
    static QOpenGLContext *currentContext();
    static bool areSharing(QOpenGLContext *first, QOpenGLContext *second);
    QSet<QByteArray> extensions() const;
    bool hasExtension(const QByteArray &extension) const;

signals:
    void aboutToBeDestroyed();

public:
    enum OpenGLModuleType
    {
        LibGL,
        LibGLES,
    };

    static QOpenGLContext::OpenGLModuleType openGLModuleType();
    bool isOpenGLES() const;
    static bool supportsThreadedOpenGL();
    static QOpenGLContext *globalShareContext();
};

%End
