# -*- coding: utf-8 -*-
"""
MCP服务管理器
统一管理所有MCP工具和服务的核心组件
"""

import asyncio
import json
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Type
from dataclasses import dataclass
from enum import Enum
import aiohttp
from pathlib import Path

# 配置日志
logger = logging.getLogger(__name__)

class ServiceStatus(Enum):
    """服务状态枚举"""
    INACTIVE = "inactive"
    INITIALIZING = "initializing"
    ACTIVE = "active"
    ERROR = "error"
    MAINTENANCE = "maintenance"

class ServiceType(Enum):
    """服务类型枚举"""
    LLM = "llm"
    TRANSLATION = "translation"
    TTS = "tts"
    IMAGE_GENERATION = "image_generation"
    VIDEO_GENERATION = "video_generation"
    SOCIAL_MEDIA = "social_media"
    STORAGE = "storage"

@dataclass
class ServiceRequest:
    """服务请求数据结构"""
    service_type: ServiceType
    action: str
    parameters: Dict[str, Any]
    priority: int = 1
    timeout: int = 300
    retry_count: int = 3

@dataclass
class ServiceResponse:
    """服务响应数据结构"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    service_name: str = ""
    metadata: Dict[str, Any] = None

class MCPServiceInterface(ABC):
    """MCP服务统一接口"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.status = ServiceStatus.INACTIVE
        self.last_error = None
        self.request_count = 0
        self.success_count = 0
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化服务"""
        pass
    
    @abstractmethod
    async def process(self, request: ServiceRequest) -> ServiceResponse:
        """处理服务请求"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """健康检查"""
        pass
    
    async def cleanup(self):
        """清理资源"""
        pass
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取服务指标"""
        success_rate = (self.success_count / self.request_count) if self.request_count > 0 else 0
        return {
            'name': self.name,
            'status': self.status.value,
            'request_count': self.request_count,
            'success_count': self.success_count,
            'success_rate': success_rate,
            'last_error': self.last_error
        }

class ConfigLoader:
    """配置加载器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.configs = {}
    
    def load_all_configs(self) -> Dict[str, Any]:
        """加载所有配置文件"""
        try:
            # 加载LLM配置
            self.configs['llm'] = self._load_json_config('llm_config.json')
            
            # 加载TTS配置
            self.configs['tts'] = self._load_json_config('tts_config.json')
            
            # 加载翻译配置
            self.configs['translation'] = self._load_python_config('baidu_translate_config.py')
            
            # 加载图像生成配置
            self.configs['image'] = self._load_python_config('image_generation_config.py')
            
            # 加载视频生成配置
            self.configs['video'] = self._load_python_config('video_generation_config.py')
            
            logger.info("所有配置文件加载完成")
            return self.configs
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            raise
    
    def _load_json_config(self, filename: str) -> Dict[str, Any]:
        """加载JSON配置文件"""
        config_path = self.config_dir / filename
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def _load_python_config(self, filename: str) -> Dict[str, Any]:
        """加载Python配置文件"""
        config_path = self.config_dir / filename
        if config_path.exists():
            import importlib.util
            spec = importlib.util.spec_from_file_location("config", config_path)
            config_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(config_module)
            
            # 提取配置变量
            config = {}
            for attr_name in dir(config_module):
                if not attr_name.startswith('_'):
                    attr_value = getattr(config_module, attr_name)
                    if isinstance(attr_value, (dict, list, str, int, float, bool)):
                        config[attr_name] = attr_value
            return config
        return {}

class ServiceRegistry:
    """服务注册中心"""
    
    def __init__(self):
        self.services: Dict[str, MCPServiceInterface] = {}
        self.service_types: Dict[ServiceType, List[str]] = {
            service_type: [] for service_type in ServiceType
        }
    
    def register_service(self, service: MCPServiceInterface, service_type: ServiceType):
        """注册服务"""
        self.services[service.name] = service
        if service.name not in self.service_types[service_type]:
            self.service_types[service_type].append(service.name)
        logger.info(f"服务已注册: {service.name} ({service_type.value})")
    
    def get_service(self, name: str) -> Optional[MCPServiceInterface]:
        """获取服务实例"""
        return self.services.get(name)
    
    def get_services_by_type(self, service_type: ServiceType) -> List[MCPServiceInterface]:
        """根据类型获取服务列表"""
        service_names = self.service_types.get(service_type, [])
        return [self.services[name] for name in service_names if name in self.services]
    
    def get_active_services(self, service_type: ServiceType) -> List[MCPServiceInterface]:
        """获取活跃的服务"""
        services = self.get_services_by_type(service_type)
        return [service for service in services if service.status == ServiceStatus.ACTIVE]
    
    async def health_check_all(self) -> Dict[str, bool]:
        """检查所有服务健康状态"""
        results = {}
        for name, service in self.services.items():
            try:
                results[name] = await service.health_check()
            except Exception as e:
                logger.error(f"服务 {name} 健康检查失败: {e}")
                results[name] = False
        return results

class ServiceRouter:
    """服务路由器"""
    
    def __init__(self, registry: ServiceRegistry):
        self.registry = registry
        self.routing_strategies = {
            'priority': self._priority_routing,
            'load_balance': self._load_balance_routing,
            'cost_optimize': self._cost_optimize_routing,
            'free_first': self._free_first_routing
        }
    
    async def route_request(self, request: ServiceRequest, strategy: str = 'free_first') -> MCPServiceInterface:
        """路由请求到最佳服务"""
        routing_func = self.routing_strategies.get(strategy, self._free_first_routing)
        return await routing_func(request)
    
    async def _priority_routing(self, request: ServiceRequest) -> MCPServiceInterface:
        """优先级路由"""
        services = self.registry.get_active_services(request.service_type)
        if not services:
            raise Exception(f"没有可用的 {request.service_type.value} 服务")
        return services[0]  # 返回第一个可用服务
    
    async def _load_balance_routing(self, request: ServiceRequest) -> MCPServiceInterface:
        """负载均衡路由"""
        services = self.registry.get_active_services(request.service_type)
        if not services:
            raise Exception(f"没有可用的 {request.service_type.value} 服务")
        
        # 选择请求数最少的服务
        return min(services, key=lambda s: s.request_count)
    
    async def _cost_optimize_routing(self, request: ServiceRequest) -> MCPServiceInterface:
        """成本优化路由"""
        services = self.registry.get_active_services(request.service_type)
        if not services:
            raise Exception(f"没有可用的 {request.service_type.value} 服务")
        
        # 优先选择免费服务
        free_services = [s for s in services if s.config.get('cost_per_request', 0) == 0]
        if free_services:
            return free_services[0]
        
        # 选择成本最低的服务
        return min(services, key=lambda s: s.config.get('cost_per_request', float('inf')))
    
    async def _free_first_routing(self, request: ServiceRequest) -> MCPServiceInterface:
        """免费优先路由"""
        services = self.registry.get_active_services(request.service_type)
        if not services:
            raise Exception(f"没有可用的 {request.service_type.value} 服务")
        
        # 优先选择免费且成功率高的服务
        free_services = [s for s in services if s.config.get('cost_per_request', 0) == 0]
        if free_services:
            return max(free_services, key=lambda s: s.success_count / max(s.request_count, 1))
        
        return services[0]

class MCPServiceManager:
    """MCP服务管理器主类"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_loader = ConfigLoader(config_dir)
        self.registry = ServiceRegistry()
        self.router = ServiceRouter(self.registry)
        self.configs = {}
        self.session = None
        self._initialized = False
    
    async def initialize(self):
        """初始化服务管理器"""
        if self._initialized:
            return
        
        try:
            # 加载配置
            self.configs = self.config_loader.load_all_configs()
            
            # 创建HTTP会话
            self.session = aiohttp.ClientSession()
            
            # 注册和初始化服务
            await self._register_all_services()
            await self._initialize_all_services()
            
            self._initialized = True
            logger.info("MCP服务管理器初始化完成")
            
        except Exception as e:
            logger.error(f"MCP服务管理器初始化失败: {e}")
            raise
    
    async def _register_all_services(self):
        """注册所有服务"""
        # 这里将在后续实现中添加具体的服务注册逻辑
        # 例如: LLM服务、TTS服务、图像生成服务等
        pass
    
    async def _initialize_all_services(self):
        """初始化所有已注册的服务"""
        for service in self.registry.services.values():
            try:
                service.status = ServiceStatus.INITIALIZING
                success = await service.initialize()
                service.status = ServiceStatus.ACTIVE if success else ServiceStatus.ERROR
                if not success:
                    logger.warning(f"服务 {service.name} 初始化失败")
            except Exception as e:
                service.status = ServiceStatus.ERROR
                service.last_error = str(e)
                logger.error(f"服务 {service.name} 初始化异常: {e}")
    
    async def process_request(self, request: ServiceRequest, routing_strategy: str = 'free_first') -> ServiceResponse:
        """处理服务请求"""
        if not self._initialized:
            await self.initialize()
        
        try:
            # 路由到最佳服务
            service = await self.router.route_request(request, routing_strategy)
            
            # 处理请求
            service.request_count += 1
            response = await service.process(request)
            
            if response.success:
                service.success_count += 1
            else:
                service.last_error = response.error
            
            return response
            
        except Exception as e:
            logger.error(f"请求处理失败: {e}")
            return ServiceResponse(
                success=False,
                error=str(e)
            )
    
    async def get_service_metrics(self) -> Dict[str, Any]:
        """获取所有服务指标"""
        metrics = {
            'total_services': len(self.registry.services),
            'active_services': len([s for s in self.registry.services.values() if s.status == ServiceStatus.ACTIVE]),
            'services': {}
        }
        
        for service_type in ServiceType:
            services = self.registry.get_services_by_type(service_type)
            metrics['services'][service_type.value] = [
                service.get_metrics() for service in services
            ]
        
        return metrics
    
    async def cleanup(self):
        """清理资源"""
        # 清理所有服务
        for service in self.registry.services.values():
            try:
                await service.cleanup()
            except Exception as e:
                logger.error(f"服务 {service.name} 清理失败: {e}")
        
        # 关闭HTTP会话
        if self.session:
            await self.session.close()
        
        self._initialized = False
        logger.info("MCP服务管理器已清理")
    
    def __del__(self):
        """析构函数"""
        if self._initialized and self.session and not self.session.closed:
            # 在事件循环中清理
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.cleanup())
            except:
                pass

# 全局服务管理器实例
_service_manager = None

def get_service_manager() -> MCPServiceManager:
    """获取全局服务管理器实例"""
    global _service_manager
    if _service_manager is None:
        _service_manager = MCPServiceManager()
    return _service_manager

async def initialize_mcp_services():
    """初始化MCP服务（便捷函数）"""
    manager = get_service_manager()
    await manager.initialize()
    return manager

if __name__ == "__main__":
    # 测试代码
    async def test_service_manager():
        manager = await initialize_mcp_services()
        metrics = await manager.get_service_metrics()
        print(json.dumps(metrics, indent=2, ensure_ascii=False))
        await manager.cleanup()
    
    asyncio.run(test_service_manager())