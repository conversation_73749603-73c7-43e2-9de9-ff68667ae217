"""启动画面

应用程序启动时显示的启动画面。
"""

from typing import Optional
from PyQt6.QtWidgets import QSplashScreen, QLabel, QVBoxLayout, QWidget, QProgressBar
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QPixmap, QPainter, QFont, QColor, QLinearGradient, QBrush

from src.ui.themes import get_theme_manager
from src.ui.components.modern_widgets import ModernProgressBar, ModernLabel


class SplashScreen(QSplashScreen):
    """启动画面类"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, str)
    loading_finished = pyqtSignal()
    
    def __init__(self, width: int = 600, height: int = 400):
        # 创建启动画面背景
        pixmap = self._create_background(width, height)
        super().__init__(pixmap)
        
        self.width = width
        self.height = height
        self._current_progress = 0
        self._current_message = "正在启动..."
        
        self._setup_ui()
        self._setup_animations()
    
    def _create_background(self, width: int, height: int) -> QPixmap:
        """创建启动画面背景"""
        pixmap = QPixmap(width, height)
        pixmap.fill(Qt.GlobalColor.transparent)

        # 使用try-finally确保painter正确释放
        painter = QPainter()
        try:
            if not painter.begin(pixmap):
                # 如果无法开始绘制，返回简单的背景
                pixmap.fill(QColor("#2196F3"))
                return pixmap

            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # 创建渐变背景
            gradient = QLinearGradient(0, 0, 0, height)
            gradient.setColorAt(0, QColor("#2196F3"))  # 使用固定颜色避免主题依赖
            gradient.setColorAt(1, QColor("#6f42c1"))

            painter.setBrush(QBrush(gradient))
            painter.setPen(Qt.PenStyle.NoPen)
            painter.drawRect(0, 0, width, height)

            # 绘制应用程序标题
            painter.setPen(QColor("white"))
            title_font = QFont("Segoe UI", 28, QFont.Weight.Bold)
            painter.setFont(title_font)

            title_rect = painter.fontMetrics().boundingRect("AI视频生成器 2.0")
            title_x = (width - title_rect.width()) // 2
            title_y = height // 2 - 50
            painter.drawText(title_x, title_y, "AI视频生成器 2.0")

            # 绘制版本信息
            version_font = QFont("Segoe UI", 12)
            painter.setFont(version_font)
            painter.setPen(QColor(255, 255, 255, 180))

            version_text = "Version 2.0.0"
            version_rect = painter.fontMetrics().boundingRect(version_text)
            version_x = (width - version_rect.width()) // 2
            version_y = title_y + 40
            painter.drawText(version_x, version_y, version_text)

        finally:
            painter.end()

        return pixmap
    
    def _setup_ui(self):
        """设置UI"""
        # 设置窗口属性
        self.setWindowFlags(
            Qt.WindowType.SplashScreen | 
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.WindowStaysOnTopHint
        )
        
        # 创建内容组件
        self._create_content_widget()
    
    def _create_content_widget(self):
        """创建内容组件"""
        # 创建透明的内容组件
        content_widget = QWidget()
        content_widget.setFixedSize(self.width, self.height)
        content_widget.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(40, 0, 40, 40)
        layout.setSpacing(20)
        
        # 添加弹性空间
        layout.addStretch()
        
        # 进度条
        self.progress_bar = ModernProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setFixedHeight(4)
        
        # 状态标签
        self.status_label = ModernLabel("正在启动...", "caption")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 200);
                font-size: 14px;
                background-color: transparent;
            }
        """)
        
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.status_label)
        
        # 设置内容组件为子组件
        content_widget.setParent(self)
        content_widget.move(0, 0)
        content_widget.show()
    
    def _setup_animations(self):
        """设置动画"""
        # 进度条动画
        self.progress_animation = QPropertyAnimation(self.progress_bar, b"value")
        self.progress_animation.setDuration(300)
        self.progress_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
    
    def update_progress(self, value: int, message: str = ""):
        """更新进度"""
        self._current_progress = value
        if message:
            self._current_message = message
        
        # 动画更新进度条
        self.progress_animation.setStartValue(self.progress_bar.value())
        self.progress_animation.setEndValue(value)
        self.progress_animation.start()
        
        # 更新状态文本
        self.status_label.setText(self._current_message)
        
        # 发送信号
        self.progress_updated.emit(value, self._current_message)
        
        # 处理事件
        self.repaint()
        
        # 如果完成，延迟关闭
        if value >= 100:
            QTimer.singleShot(500, self._finish_loading)
    
    def _finish_loading(self):
        """完成加载"""
        self.loading_finished.emit()
        self.close()
    
    def show_message(self, message: str, color: Optional[str] = None):
        """显示消息"""
        if color:
            self.status_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 14px;
                    background-color: transparent;
                }}
            """)
        self.status_label.setText(message)
        self.repaint()


class LoadingManager:
    """加载管理器"""
    
    def __init__(self, splash_screen: SplashScreen):
        self.splash_screen = splash_screen
        self.total_steps = 0
        self.current_step = 0
        self.step_messages = []
    
    def set_steps(self, steps: list):
        """设置加载步骤"""
        self.step_messages = steps
        self.total_steps = len(steps)
        self.current_step = 0
    
    def next_step(self, custom_message: str = ""):
        """进入下一步"""
        if self.current_step < self.total_steps:
            message = custom_message or self.step_messages[self.current_step]
            progress = int((self.current_step + 1) / self.total_steps * 100)
            
            self.splash_screen.update_progress(progress, message)
            self.current_step += 1
            
            return True
        return False
    
    def set_progress(self, progress: int, message: str = ""):
        """设置进度"""
        self.splash_screen.update_progress(progress, message)
    
    def finish(self):
        """完成加载"""
        self.splash_screen.update_progress(100, "启动完成")


def create_splash_screen() -> tuple[SplashScreen, LoadingManager]:
    """创建启动画面和加载管理器"""
    splash = SplashScreen()
    manager = LoadingManager(splash)
    
    # 设置默认加载步骤
    steps = [
        "正在初始化配置...",
        "正在加载主题...",
        "正在初始化数据库...",
        "正在加载AI服务...",
        "正在初始化UI组件...",
        "正在完成启动..."
    ]
    manager.set_steps(steps)
    
    return splash, manager


# 使用示例
if __name__ == "__main__":
    import sys
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    splash, manager = create_splash_screen()
    splash.show()
    
    # 模拟加载过程
    def simulate_loading():
        import time
        for i in range(6):
            time.sleep(0.5)
            manager.next_step()
    
    # 在实际应用中，这里会是真实的初始化过程
    QTimer.singleShot(100, simulate_loading)
    
    sys.exit(app.exec())
