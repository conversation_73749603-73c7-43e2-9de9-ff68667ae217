# AI视频生成器 2.0 开发文档

## 目录
1. [项目概述](#项目概述)
2. [架构设计](#架构设计)
3. [开发环境](#开发环境)
4. [代码结构](#代码结构)
5. [核心模块](#核心模块)
6. [API文档](#api文档)
7. [测试指南](#测试指南)
8. [部署指南](#部署指南)

## 项目概述

AI视频生成器 2.0 是一个基于Python和PyQt6的桌面应用程序，集成了多种AI服务来实现智能视频内容生成。

### 技术栈
- **前端框架**：PyQt6
- **后端语言**：Python 3.8+
- **数据库**：SQLite (SQLAlchemy ORM)
- **异步框架**：asyncio, aiohttp
- **AI服务**：OpenAI, 智谱AI, Pollinations等
- **打包工具**：PyInstaller

### 设计原则
- **模块化设计**：清晰的模块分离和接口定义
- **异步优先**：使用异步编程提高性能
- **可扩展性**：支持插件式的AI服务扩展
- **用户体验**：现代化的UI设计和流畅的交互

## 架构设计

### 整体架构
```
┌─────────────────┐
│   UI Layer      │  PyQt6界面层
├─────────────────┤
│ Business Layer  │  业务逻辑层
├─────────────────┤
│ Service Layer   │  AI服务层
├─────────────────┤
│   Data Layer    │  数据访问层
└─────────────────┘
```

### 核心组件
- **UI组件**：主窗口、对话框、自定义控件
- **业务逻辑**：分镜生成、媒体处理、一致性检查
- **服务管理**：AI服务注册、配置、健康检查
- **数据管理**：项目、分镜、镜头数据的CRUD操作

## 开发环境

### 环境要求
- Python 3.8+
- pip 或 conda
- Git

### 环境搭建
1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd ai2.0
   ```

2. **创建虚拟环境**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/macOS
   venv\Scripts\activate     # Windows
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **运行程序**
   ```bash
   python run.py
   ```

### 开发工具推荐
- **IDE**：PyCharm, VS Code
- **调试**：Python Debugger
- **测试**：pytest
- **代码格式化**：black, isort
- **类型检查**：mypy

## 代码结构

```
ai2.0/
├── src/                    # 源代码目录
│   ├── core/              # 核心模块
│   │   ├── config.py      # 配置管理
│   │   ├── database.py    # 数据库管理
│   │   ├── events.py      # 事件系统
│   │   └── exceptions.py  # 异常定义
│   ├── models/            # 数据模型
│   │   ├── base.py        # 基础模型
│   │   ├── project.py     # 项目模型
│   │   ├── storyboard.py  # 分镜模型
│   │   ├── shot.py        # 镜头模型
│   │   └── media.py       # 媒体模型
│   ├── repositories/      # 数据访问层
│   │   ├── base.py        # 基础仓库
│   │   ├── project.py     # 项目仓库
│   │   ├── storyboard.py  # 分镜仓库
│   │   └── shot.py        # 镜头仓库
│   ├── business/          # 业务逻辑层
│   │   ├── storyboard/    # 分镜业务逻辑
│   │   ├── media/         # 媒体处理
│   │   └── consistency/   # 一致性检查
│   ├── services/          # AI服务层
│   │   ├── base.py        # 服务基类
│   │   ├── llm.py         # LLM服务
│   │   ├── image.py       # 图像服务
│   │   ├── voice.py       # 语音服务
│   │   ├── video.py       # 视频服务
│   │   └── registry.py    # 服务注册
│   ├── ui/                # 用户界面
│   │   ├── themes/        # 主题系统
│   │   ├── components/    # UI组件
│   │   ├── dialogs/       # 对话框
│   │   └── main_window.py # 主窗口
│   └── utils/             # 工具模块
│       ├── logger.py      # 日志工具
│       ├── cache.py       # 缓存系统
│       └── performance.py # 性能监控
├── tests/                 # 测试代码
├── docs/                  # 文档
├── config/                # 配置文件
├── logs/                  # 日志文件
├── generated/             # 生成的文件
├── requirements.txt       # 依赖列表
├── main.py               # 主程序入口
└── run.py                # 启动脚本
```

## 核心模块

### 1. 配置管理 (src/core/config.py)
```python
class ConfigManager:
    """配置管理器"""
    
    async def load_config(self) -> None:
        """加载配置"""
        
    async def save_config(self) -> None:
        """保存配置"""
        
    def get(self, key: str, default=None):
        """获取配置值"""
```

### 2. 数据库管理 (src/core/database.py)
```python
class DatabaseManager:
    """数据库管理器"""
    
    async def initialize(self) -> None:
        """初始化数据库"""
        
    async def get_async_session(self) -> AsyncSession:
        """获取异步会话"""
        
    async def health_check(self) -> bool:
        """健康检查"""
```

### 3. 服务注册 (src/services/registry.py)
```python
class ServiceRegistry:
    """服务注册表"""
    
    def register_service(self, registration: ServiceRegistration) -> None:
        """注册服务"""
        
    async def create_service(self, name: str, config: Dict) -> BaseService:
        """创建服务实例"""
        
    async def start_health_monitoring(self) -> None:
        """开始健康监控"""
```

### 4. 业务逻辑 (src/business/)
```python
class StoryboardGenerator:
    """分镜生成器"""
    
    async def generate_from_text(self, text: str, style: str) -> ServiceResponse:
        """从文本生成分镜"""

class MediaProcessor:
    """媒体处理器"""
    
    async def process_shot_images(self, shots: List[Shot]) -> ServiceResponse:
        """处理镜头图像"""
```

## API文档

### 服务接口

#### LLM服务接口
```python
class LLMService(AIService):
    async def chat_completion(self, messages: List[Dict]) -> ServiceResponse:
        """聊天完成"""
        
    async def text_completion(self, prompt: str) -> ServiceResponse:
        """文本完成"""
```

#### 图像服务接口
```python
class ImageGenerationService(AIService):
    async def generate_image(self, prompt: str, **kwargs) -> ServiceResponse:
        """生成图像"""
        
    async def edit_image(self, image_path: str, prompt: str) -> ServiceResponse:
        """编辑图像"""
```

#### 语音服务接口
```python
class VoiceGenerationService(AIService):
    async def text_to_speech(self, text: str, **kwargs) -> ServiceResponse:
        """文本转语音"""
        
    async def clone_voice(self, audio_path: str, text: str) -> ServiceResponse:
        """语音克隆"""
```

### 数据模型

#### 项目模型
```python
class Project(Base):
    id: UUID
    title: str
    description: str
    content: str
    style: str
    language: str
    video_width: int
    video_height: int
    video_fps: float
    created_at: datetime
    updated_at: datetime
```

#### 分镜模型
```python
class Storyboard(Base):
    id: UUID
    project_id: UUID
    name: str
    content: str
    generation_settings: Dict
    created_at: datetime
    updated_at: datetime
```

## 测试指南

### 测试结构
```
tests/
├── unit/              # 单元测试
│   ├── test_services.py
│   ├── test_business.py
│   └── test_models.py
├── integration/       # 集成测试
│   ├── test_workflow.py
│   └── test_database.py
└── conftest.py       # 测试配置
```

### 运行测试
```bash
# 运行所有测试
pytest

# 运行单元测试
pytest tests/unit/

# 运行集成测试
pytest tests/integration/

# 生成覆盖率报告
pytest --cov=src --cov-report=html
```

### 测试编写规范
```python
class TestStoryboardGenerator:
    @pytest.fixture
    def generator(self, mock_llm_service):
        return StoryboardGenerator(mock_llm_service)
    
    @pytest.mark.asyncio
    async def test_generate_from_text_success(self, generator):
        result = await generator.generate_from_text("测试文本", "动画")
        assert result.success is True
```

## 部署指南

### 开发部署
```bash
# 启动开发服务器
python run.py

# 启动调试模式
python run.py --debug
```

### 生产部署
```bash
# 构建可执行文件
python build.py

# 创建安装包
# 使用NSIS编译installer.nsi
```

### Docker部署
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "run.py"]
```

### 性能优化
- 使用异步编程提高并发性能
- 实现缓存机制减少重复计算
- 使用连接池管理数据库连接
- 启用性能监控和日志记录

## 贡献指南

### 代码规范
- 使用Python PEP 8编码规范
- 添加类型注解
- 编写文档字符串
- 保持测试覆盖率>90%

### 提交规范
```
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式化
refactor: 重构代码
test: 添加测试
chore: 构建过程或辅助工具的变动
```

### 开发流程
1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request
5. 代码审查
6. 合并到主分支

---

**欢迎参与AI视频生成器 2.0的开发！**
