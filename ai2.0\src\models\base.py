"""基础数据模型

定义所有数据模型的基类和通用字段。
"""

from typing import Any, Dict, Optional
from datetime import datetime
from uuid import uuid4

from sqlalchemy import Column, String, DateTime, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from pydantic import BaseModel, Field


# SQLAlchemy基类
Base = declarative_base()


class TimestampMixin:
    """时间戳混入类"""
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )


class SoftDeleteMixin:
    """软删除混入类"""
    
    deleted_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="删除时间"
    )
    
    is_deleted = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否已删除"
    )


class BaseEntity(Base, TimestampMixin, SoftDeleteMixin):
    """基础实体类"""
    
    __abstract__ = True
    
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        comment="主键ID"
    )
    
    name = Column(
        String(255),
        nullable=False,
        comment="名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="描述"
    )
    
    metadata_json = Column(
        Text,
        nullable=True,
        comment="元数据JSON"
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.isoformat()
            elif hasattr(value, '__str__'):
                value = str(value)
            result[column.name] = value
        return result
    
    def update_from_dict(self, data: Dict[str, Any]) -> None:
        """从字典更新属性"""
        for key, value in data.items():
            if hasattr(self, key) and key not in ['id', 'created_at']:
                setattr(self, key, value)
    
    def soft_delete(self) -> None:
        """软删除"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
    
    def restore(self) -> None:
        """恢复删除"""
        self.is_deleted = False
        self.deleted_at = None
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.id}, name='{self.name}')>"


# Pydantic基础模型
class BaseSchema(BaseModel):
    """基础Pydantic模型"""
    
    class Config:
        from_attributes = True
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class TimestampSchema(BaseSchema):
    """时间戳模式"""
    
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class BaseEntitySchema(TimestampSchema):
    """基础实体模式"""
    
    id: str = Field(..., description="主键ID")
    name: str = Field(..., max_length=255, description="名称")
    description: Optional[str] = Field(None, description="描述")
    metadata_json: Optional[str] = Field(None, description="元数据JSON")
    is_deleted: bool = Field(False, description="是否已删除")
    deleted_at: Optional[datetime] = Field(None, description="删除时间")


class CreateEntitySchema(BaseSchema):
    """创建实体模式"""
    
    name: str = Field(..., max_length=255, description="名称")
    description: Optional[str] = Field(None, description="描述")
    metadata_json: Optional[str] = Field(None, description="元数据JSON")


class UpdateEntitySchema(BaseSchema):
    """更新实体模式"""
    
    name: Optional[str] = Field(None, max_length=255, description="名称")
    description: Optional[str] = Field(None, description="描述")
    metadata_json: Optional[str] = Field(None, description="元数据JSON")


# 枚举类型
from enum import Enum


class ProjectStatus(str, Enum):
    """项目状态"""
    DRAFT = "draft"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ARCHIVED = "archived"


class StoryboardStatus(str, Enum):
    """分镜状态"""
    DRAFT = "draft"
    GENERATING = "generating"
    COMPLETED = "completed"
    FAILED = "failed"


class ShotType(str, Enum):
    """镜头类型"""
    WIDE_SHOT = "wide_shot"
    MEDIUM_SHOT = "medium_shot"
    CLOSE_UP = "close_up"
    EXTREME_CLOSE_UP = "extreme_close_up"
    ESTABLISHING_SHOT = "establishing_shot"
    OVER_SHOULDER = "over_shoulder"
    POINT_OF_VIEW = "point_of_view"


class MediaType(str, Enum):
    """媒体类型"""
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"


class MediaStatus(str, Enum):
    """媒体状态"""
    PENDING = "pending"
    GENERATING = "generating"
    COMPLETED = "completed"
    FAILED = "failed"


class GenerationProvider(str, Enum):
    """生成服务提供商"""
    OPENAI = "openai"
    ZHIPU = "zhipu"
    TONGYI = "tongyi"
    DEEPSEEK = "deepseek"
    POLLINATIONS = "pollinations"
    COGVIDEOX = "cogvideox"
    EDGE_TTS = "edge_tts"


# 常量定义
class Constants:
    """常量定义"""
    
    # 默认值
    DEFAULT_PROJECT_NAME = "新项目"
    DEFAULT_STORYBOARD_NAME = "新分镜"
    DEFAULT_SHOT_DURATION = 3.0  # 秒
    
    # 限制
    MAX_NAME_LENGTH = 255
    MAX_DESCRIPTION_LENGTH = 2000
    MAX_SHOTS_PER_STORYBOARD = 100
    MAX_PROJECTS_PER_USER = 50
    
    # 文件路径
    MEDIA_UPLOAD_PATH = "uploads"
    GENERATED_MEDIA_PATH = "generated"
    TEMP_PATH = "temp"
    
    # 支持的文件格式
    SUPPORTED_IMAGE_FORMATS = [".jpg", ".jpeg", ".png", ".webp", ".bmp"]
    SUPPORTED_VIDEO_FORMATS = [".mp4", ".avi", ".mov", ".mkv", ".webm"]
    SUPPORTED_AUDIO_FORMATS = [".mp3", ".wav", ".flac", ".aac"]
