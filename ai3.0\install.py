#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器3.0 - 自动安装脚本
帮助用户快速安装所需依赖和配置环境
"""

import subprocess
import sys
import os
import json
from pathlib import Path

def print_banner():
    """打印安装横幅"""
    print("\n" + "=" * 60)
    print("🔧 AI视频生成器 3.0 - 自动安装程序")
    print("=" * 60)
    print("正在为您安装必要的依赖包和配置环境...")
    print("=" * 60)

def check_python_version():
    """检查Python版本"""
    print("\n🐍 检查Python版本...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro} (符合要求)")
        return True
    else:
        print(f"❌ Python版本: {version.major}.{version.minor}.{version.micro}")
        print("⚠️ 需要Python 3.8或更高版本")
        return False

def install_requirements():
    """安装依赖包"""
    print("\n📦 安装Python依赖包...")
    
    # 核心依赖列表（简化版）
    core_packages = [
        "aiohttp>=3.8.0",
        "asyncio",
        "requests>=2.28.0",
        "Pillow>=9.0.0",
        "opencv-python>=4.6.0",
        "moviepy>=1.0.3",
        "edge-tts>=6.1.0",
        "openai>=1.0.0",
        "google-generativeai>=0.3.0",
        "zhipuai>=2.0.0",
        "dashscope>=1.14.0",
        "pydantic>=2.0.0",
        "python-dotenv>=1.0.0",
        "colorama>=0.4.6",
        "tqdm>=4.64.0"
    ]
    
    try:
        # 升级pip
        print("  📈 升级pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # 安装核心依赖
        print("  📚 安装核心依赖...")
        for package in core_packages:
            print(f"    安装 {package}...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                              check=True, capture_output=True)
                print(f"    ✅ {package} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"    ⚠️ {package} 安装失败，将在后续尝试")
        
        # 尝试从requirements.txt安装（如果存在）
        if Path("requirements.txt").exists():
            print("  📋 从requirements.txt安装完整依赖...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                              check=True, capture_output=True)
                print("  ✅ 完整依赖安装成功")
            except subprocess.CalledProcessError:
                print("  ⚠️ 部分依赖安装失败，但核心功能应该可用")
        
        print("\n✅ 依赖安装完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 依赖安装失败: {e}")
        print("💡 请手动运行: pip install -r requirements.txt")
        return False

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建工作目录...")
    
    directories = [
        "output",
        "output/videos",
        "output/images", 
        "output/audio",
        "output/subtitles",
        "temp",
        "logs"
    ]
    
    for directory in directories:
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            print(f"  ✅ 创建目录: {directory}")
        except Exception as e:
            print(f"  ❌ 创建目录失败 {directory}: {e}")
    
    print("\n✅ 目录创建完成！")

def check_config():
    """检查配置文件"""
    print("\n⚙️ 检查配置文件...")
    
    config_file = Path("config.json")
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("  ✅ 配置文件存在且格式正确")
            
            # 检查关键API密钥
            api_keys_configured = 0
            total_keys = 0
            
            # 检查LLM API密钥
            llm_clients = config.get('llm', {}).get('clients', {})
            for client_name, client_config in llm_clients.items():
                total_keys += 1
                api_key = client_config.get('api_key', '')
                if api_key and api_key != 'YOUR_API_KEY_HERE':
                    api_keys_configured += 1
                    print(f"    ✅ {client_name} API密钥已配置")
                else:
                    print(f"    ❌ {client_name} API密钥未配置")
            
            print(f"\n  📊 API密钥配置状态: {api_keys_configured}/{total_keys}")
            
            if api_keys_configured > 0:
                print("  ✅ 至少有一个服务已配置，可以开始使用")
                return True
            else:
                print("  ⚠️ 没有配置任何API密钥，部分功能将无法使用")
                return False
                
        except json.JSONDecodeError:
            print("  ❌ 配置文件格式错误")
            return False
    else:
        print("  ❌ 配置文件不存在")
        print("  💡 请确保config.json文件存在")
        return False

def run_quick_test():
    """运行快速测试"""
    print("\n🧪 运行快速测试...")
    
    try:
        # 检查是否可以导入核心模块
        print("  🔍 检查核心模块...")
        
        # 测试基本导入
        import aiohttp
        import requests
        import json
        print("    ✅ 基础模块导入成功")
        
        # 测试图像处理
        try:
            from PIL import Image
            import cv2
            print("    ✅ 图像处理模块正常")
        except ImportError as e:
            print(f"    ⚠️ 图像处理模块问题: {e}")
        
        # 测试音频处理
        try:
            import edge_tts
            print("    ✅ 语音合成模块正常")
        except ImportError as e:
            print(f"    ⚠️ 语音合成模块问题: {e}")
        
        # 测试视频处理
        try:
            import moviepy.editor as mp
            print("    ✅ 视频处理模块正常")
        except ImportError as e:
            print(f"    ⚠️ 视频处理模块问题: {e}")
        
        print("\n  ✅ 基础功能测试通过")
        return True
        
    except Exception as e:
        print(f"\n  ❌ 测试失败: {e}")
        return False

def print_next_steps():
    """打印后续步骤"""
    print("\n" + "=" * 60)
    print("🎉 安装完成！")
    print("=" * 60)
    print("\n📋 后续步骤:")
    print("\n1. 🔑 配置API密钥:")
    print("   - 编辑 config.json 文件")
    print("   - 填入您的各项服务API密钥")
    print("\n2. 🧪 测试服务:")
    print("   - 运行: python quick_test.py")
    print("   - 检查所有服务连接状态")
    print("\n3. 🚀 开始使用:")
    print("   - 运行: python quick_start.py")
    print("   - 或者: python main.py")
    print("\n4. 📚 查看文档:")
    print("   - 阅读 README.md")
    print("   - 查看项目架构设计.md")
    print("\n💡 提示:")
    print("   - 首次使用建议先运行快速演示")
    print("   - 遇到问题请查看日志文件")
    print("   - 更多帮助请参考文档")
    print("\n" + "=" * 60)

def main():
    """主安装流程"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        print("\n❌ 安装终止：Python版本不符合要求")
        return False
    
    # 安装依赖
    if not install_requirements():
        print("\n⚠️ 依赖安装有问题，但继续安装过程...")
    
    # 创建目录
    create_directories()
    
    # 检查配置
    config_ok = check_config()
    
    # 运行测试
    test_ok = run_quick_test()
    
    # 打印结果
    if config_ok and test_ok:
        print("\n🎉 安装完全成功！")
    elif test_ok:
        print("\n✅ 基础安装成功，需要配置API密钥")
    else:
        print("\n⚠️ 安装完成，但可能存在问题")
    
    # 打印后续步骤
    print_next_steps()
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装过程中出现错误: {e}")
        print("💡 请手动安装依赖或联系技术支持")