"""日志系统

提供结构化日志记录功能。
"""

import logging
import logging.handlers
import json
import sys
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path
from enum import Enum


class LogLevel(Enum):
    """日志级别"""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # 添加额外字段
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        
        if hasattr(record, 'duration_ms'):
            log_entry['duration_ms'] = record.duration_ms
        
        if hasattr(record, 'component'):
            log_entry['component'] = record.component
        
        # 异常信息
        if record.exc_info:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': self.formatException(record.exc_info)
            }
        
        return json.dumps(log_entry, ensure_ascii=False)


class ColoredFormatter(logging.Formatter):
    """彩色控制台格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
        'RESET': '\033[0m'        # 重置
    }
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']
        
        # 格式化时间
        timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')
        
        # 构建日志消息
        message = f"{color}[{timestamp}] {record.levelname:8} {record.name:20} | {record.getMessage()}{reset}"
        
        # 添加异常信息
        if record.exc_info:
            message += f"\n{self.formatException(record.exc_info)}"
        
        return message


class LoggerManager:
    """日志管理器"""
    
    def __init__(self, log_dir: Optional[Path] = None):
        self.log_dir = log_dir or Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        self._loggers: Dict[str, logging.Logger] = {}
        self._configured = False
    
    def configure(
        self,
        level: LogLevel = LogLevel.INFO,
        console_enabled: bool = True,
        file_enabled: bool = True,
        structured_logs: bool = False
    ) -> None:
        """配置日志系统"""
        if self._configured:
            return
        
        # 设置根日志级别
        logging.getLogger().setLevel(level.value)
        
        # 清除现有处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 控制台处理器
        if console_enabled:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(level.value)
            
            if structured_logs:
                console_handler.setFormatter(StructuredFormatter())
            else:
                console_handler.setFormatter(ColoredFormatter())
            
            root_logger.addHandler(console_handler)
        
        # 文件处理器
        if file_enabled:
            # 应用日志文件
            app_log_file = self.log_dir / "app.log"
            app_handler = logging.handlers.RotatingFileHandler(
                app_log_file,
                maxBytes=10 * 1024 * 1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            app_handler.setLevel(level.value)
            app_handler.setFormatter(StructuredFormatter())
            root_logger.addHandler(app_handler)
            
            # 错误日志文件
            error_log_file = self.log_dir / "error.log"
            error_handler = logging.handlers.RotatingFileHandler(
                error_log_file,
                maxBytes=10 * 1024 * 1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(StructuredFormatter())
            root_logger.addHandler(error_handler)
        
        self._configured = True
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取日志记录器"""
        if name not in self._loggers:
            self._loggers[name] = logging.getLogger(name)
        return self._loggers[name]
    
    def log_user_action(self, logger_name: str, action: str, user_id: str, details: Dict[str, Any] = None):
        """记录用户操作"""
        logger = self.get_logger(logger_name)
        logger.info(
            f"User action: {action}",
            extra={
                'user_id': user_id,
                'action': action,
                'details': details or {},
                'component': 'user_action'
            }
        )
    
    def log_performance(self, logger_name: str, operation: str, duration_ms: float, details: Dict[str, Any] = None):
        """记录性能信息"""
        logger = self.get_logger(logger_name)
        level = logging.WARNING if duration_ms > 5000 else logging.INFO
        logger.log(
            level,
            f"Performance: {operation} completed in {duration_ms:.2f}ms",
            extra={
                'operation': operation,
                'duration_ms': duration_ms,
                'details': details or {},
                'component': 'performance'
            }
        )
    
    def log_api_call(self, logger_name: str, service: str, endpoint: str, status_code: int, duration_ms: float):
        """记录API调用"""
        logger = self.get_logger(logger_name)
        level = logging.ERROR if status_code >= 400 else logging.INFO
        logger.log(
            level,
            f"API call: {service}/{endpoint} returned {status_code}",
            extra={
                'service': service,
                'endpoint': endpoint,
                'status_code': status_code,
                'duration_ms': duration_ms,
                'component': 'api_call'
            }
        )
    
    def log_security_event(self, logger_name: str, event_type: str, details: Dict[str, Any]):
        """记录安全事件"""
        logger = self.get_logger(logger_name)
        logger.warning(
            f"Security event: {event_type}",
            extra={
                'security_event': event_type,
                'details': details,
                'component': 'security'
            }
        )


# 全局日志管理器实例
_logger_manager: Optional[LoggerManager] = None


def get_logger_manager() -> LoggerManager:
    """获取全局日志管理器实例"""
    global _logger_manager
    if _logger_manager is None:
        _logger_manager = LoggerManager()
    return _logger_manager


def get_logger(name: str) -> logging.Logger:
    """获取日志记录器"""
    return get_logger_manager().get_logger(name)


def configure_logging(
    level: LogLevel = LogLevel.INFO,
    console_enabled: bool = True,
    file_enabled: bool = True,
    structured_logs: bool = False,
    log_dir: Optional[Path] = None
) -> None:
    """配置日志系统"""
    global _logger_manager
    if log_dir:
        _logger_manager = LoggerManager(log_dir)
    
    manager = get_logger_manager()
    manager.configure(level, console_enabled, file_enabled, structured_logs)


# 性能监控装饰器
def log_performance(logger_name: str = None):
    """性能监控装饰器"""
    def decorator(func):
        import time
        import functools
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            logger = get_logger(logger_name or func.__module__)
            
            try:
                result = await func(*args, **kwargs)
                duration_ms = (time.perf_counter() - start_time) * 1000
                get_logger_manager().log_performance(
                    logger.name, func.__name__, duration_ms
                )
                return result
            except Exception as e:
                duration_ms = (time.perf_counter() - start_time) * 1000
                logger.error(f"Function {func.__name__} failed after {duration_ms:.2f}ms: {e}")
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            logger = get_logger(logger_name or func.__module__)
            
            try:
                result = func(*args, **kwargs)
                duration_ms = (time.perf_counter() - start_time) * 1000
                get_logger_manager().log_performance(
                    logger.name, func.__name__, duration_ms
                )
                return result
            except Exception as e:
                duration_ms = (time.perf_counter() - start_time) * 1000
                logger.error(f"Function {func.__name__} failed after {duration_ms:.2f}ms: {e}")
                raise
        
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator
