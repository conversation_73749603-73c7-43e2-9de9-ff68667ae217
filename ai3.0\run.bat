@echo off
chcp 65001 >nul
echo ========================================
echo    AI视频生成器 3.0 - Windows启动器
echo ========================================
echo.
echo 🎬 全自动AI视频生成系统
echo 📱 支持多平台发布
echo 🤖 集成多种AI服务
echo.

echo 请选择启动模式：
echo 1. 🚀 快速启动 (推荐)
echo 2. 🔧 自动安装依赖
echo 3. 🧪 测试服务状态
echo 4. ⚙️ 完整功能模式
echo 5. 📚 查看帮助
echo 6. 🚪 退出
echo.

set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" (
    echo 🚀 正在启动快速模式...
    python quick_start.py
) else if "%choice%"=="2" (
    echo 🔧 正在自动安装依赖...
    python install.py
) else if "%choice%"=="3" (
    echo 🧪 正在测试服务状态...
    python quick_test.py
) else if "%choice%"=="4" (
    echo ⚙️ 正在启动完整功能模式...
    python main.py --interactive
) else if "%choice%"=="5" (
    echo 📚 显示帮助信息...
    python main.py --help
) else if "%choice%"=="6" (
    echo 👋 退出程序
    exit /b 0
) else (
    echo ❌ 无效选择，请重新运行
)

echo.
echo 按任意键退出...
pause >nul