{"timestamp": "2025-06-29T16:37:39.665458", "level": "ERROR", "logger": "src.main", "message": "Failed to initialize application: 'ContentArea' object has no attribute '_pages'", "module": "main", "function": "initialize", "line": 54}
{"timestamp": "2025-06-29T16:38:58.337810", "level": "ERROR", "logger": "src.main", "message": "Failed to initialize application: 'ContentArea' object has no attribute '_pages'", "module": "main", "function": "initialize", "line": 54}
{"timestamp": "2025-06-29T16:40:07.320283", "level": "ERROR", "logger": "src.main", "message": "Failed to initialize application: 'LoadingWidget' object has no attribute 'message'", "module": "main", "function": "initialize", "line": 54}
{"timestamp": "2025-06-30T10:28:12.369977", "level": "ERROR", "logger": "sqlalchemy.pool.impl.AsyncAdaptedQueuePool", "message": "Exception closing connection <AdaptedConnection <Connection(Thread-1, started daemon 8908)>>", "module": "base", "function": "_close_connection", "line": 378, "exception": {"type": "CancelledError", "message": "", "traceback": "Traceback (most recent call last):\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 376, in _close_connection\n    self._dialect.do_close(connection)\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py\", line 712, in do_close\n    dbapi_connection.close()\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py\", line 277, in close\n    self.await_(self._connection.close())\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py\", line 132, in await_only\n    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py\", line 196, in greenlet_spawn\n    value = await result\n            ^^^^^^^^^^^^\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\aiosqlite\\core.py\", line 168, in close\n    await self._execute(self._conn.close)\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\aiosqlite\\core.py\", line 122, in _execute\n    return await future\n           ^^^^^^^^^^^^\nasyncio.exceptions.CancelledError"}}
{"timestamp": "2025-06-30T10:40:49.155239", "level": "ERROR", "logger": "sqlalchemy.pool.impl.AsyncAdaptedQueuePool", "message": "Exception closing connection <AdaptedConnection <Connection(Thread-1, started daemon 20492)>>", "module": "base", "function": "_close_connection", "line": 378, "exception": {"type": "CancelledError", "message": "", "traceback": "Traceback (most recent call last):\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 376, in _close_connection\n    self._dialect.do_close(connection)\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py\", line 712, in do_close\n    dbapi_connection.close()\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py\", line 277, in close\n    self.await_(self._connection.close())\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py\", line 132, in await_only\n    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py\", line 196, in greenlet_spawn\n    value = await result\n            ^^^^^^^^^^^^\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\aiosqlite\\core.py\", line 168, in close\n    await self._execute(self._conn.close)\n  File \"F:\\DAI\\ai2.0\\venv\\Lib\\site-packages\\aiosqlite\\core.py\", line 122, in _execute\n    return await future\n           ^^^^^^^^^^^^\nasyncio.exceptions.CancelledError"}}
