"""主题管理器

管理应用程序的主题切换和样式应用。
"""

from typing import Dict, Optional, Callable, List
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QObject, pyqtSignal

from .base_theme import Theme
from .light_theme import LightTheme
from .dark_theme import DarkTheme
from src.utils.logger import get_logger


class ThemeManager(QObject):
    """主题管理器"""
    
    # 信号定义
    theme_changed = pyqtSignal(str)  # 主题变化信号
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self._themes: Dict[str, Theme] = {}
        self._current_theme: Optional[Theme] = None
        self._theme_change_callbacks: List[Callable[[str], None]] = []
        
        # 注册默认主题
        self._register_default_themes()
    
    def _register_default_themes(self) -> None:
        """注册默认主题"""
        self.register_theme(LightTheme())
        self.register_theme(DarkTheme())
        
        # 设置默认主题
        self.set_theme("light")
    
    def register_theme(self, theme: Theme) -> None:
        """注册主题"""
        self._themes[theme.name] = theme
        self.logger.info(f"Registered theme: {theme.name}")
    
    def get_theme(self, name: str) -> Optional[Theme]:
        """获取主题"""
        return self._themes.get(name)
    
    def get_current_theme(self) -> Optional[Theme]:
        """获取当前主题"""
        return self._current_theme
    
    def get_current_theme_name(self) -> str:
        """获取当前主题名称"""
        return self._current_theme.name if self._current_theme else "unknown"
    
    def get_available_themes(self) -> List[str]:
        """获取可用主题列表"""
        return list(self._themes.keys())
    
    def set_theme(self, theme_name: str) -> bool:
        """设置主题"""
        if theme_name not in self._themes:
            self.logger.warning(f"Theme not found: {theme_name}")
            return False
        
        old_theme_name = self.get_current_theme_name()
        self._current_theme = self._themes[theme_name]
        
        # 应用主题到应用程序
        self._apply_theme()
        
        # 发出信号
        self.theme_changed.emit(theme_name)
        
        # 调用回调函数
        for callback in self._theme_change_callbacks:
            try:
                callback(theme_name)
            except Exception as e:
                self.logger.error(f"Theme change callback failed: {e}")
        
        self.logger.info(f"Theme changed from {old_theme_name} to {theme_name}")
        return True
    
    def _apply_theme(self) -> None:
        """应用主题到应用程序"""
        if not self._current_theme:
            return
        
        app = QApplication.instance()
        if app:
            stylesheet = self._current_theme.get_stylesheet()
            app.setStyleSheet(stylesheet)
            self.logger.debug(f"Applied stylesheet for theme: {self._current_theme.name}")
    
    def toggle_theme(self) -> str:
        """切换主题（在浅色和深色之间）"""
        current_name = self.get_current_theme_name()
        
        if current_name == "light":
            self.set_theme("dark")
            return "dark"
        else:
            self.set_theme("light")
            return "light"
    
    def add_theme_change_callback(self, callback: Callable[[str], None]) -> None:
        """添加主题变化回调"""
        self._theme_change_callbacks.append(callback)
    
    def remove_theme_change_callback(self, callback: Callable[[str], None]) -> None:
        """移除主题变化回调"""
        if callback in self._theme_change_callbacks:
            self._theme_change_callbacks.remove(callback)
    
    def get_color(self, color_name: str) -> str:
        """获取当前主题的颜色"""
        if not self._current_theme:
            return "#000000"
        
        colors = self._current_theme.colors
        return getattr(colors, color_name, "#000000")
    
    def get_spacing(self, size: str) -> int:
        """获取当前主题的间距"""
        if not self._current_theme:
            return 8
        
        spacing = self._current_theme.spacing
        return getattr(spacing, size, 8)
    
    def get_border_radius(self, size: str) -> int:
        """获取当前主题的圆角"""
        if not self._current_theme:
            return 4
        
        border_radius = self._current_theme.border_radius
        return getattr(border_radius, size, 4)
    
    def get_font_size(self, size: str) -> int:
        """获取当前主题的字体大小"""
        if not self._current_theme:
            return 14
        
        typography = self._current_theme.typography
        return getattr(typography, f"font_size_{size}", 14)
    
    def create_custom_stylesheet(self, widget_class: str, styles: Dict[str, str]) -> str:
        """创建自定义样式表"""
        if not self._current_theme:
            return ""
        
        style_rules = []
        for property_name, value in styles.items():
            # 替换主题变量
            value = self._replace_theme_variables(value)
            style_rules.append(f"{property_name}: {value};")
        
        return f"{widget_class} {{ {' '.join(style_rules)} }}"
    
    def _replace_theme_variables(self, value: str) -> str:
        """替换样式值中的主题变量"""
        if not self._current_theme:
            return value
        
        # 替换颜色变量
        colors = self._current_theme.colors
        for attr_name in dir(colors):
            if not attr_name.startswith('_'):
                var_name = f"${{{attr_name}}}"
                if var_name in value:
                    color_value = getattr(colors, attr_name)
                    value = value.replace(var_name, color_value)
        
        # 替换间距变量
        spacing = self._current_theme.spacing
        for attr_name in dir(spacing):
            if not attr_name.startswith('_'):
                var_name = f"${{{attr_name}}}"
                if var_name in value:
                    spacing_value = str(getattr(spacing, attr_name))
                    value = value.replace(var_name, f"{spacing_value}px")
        
        # 替换圆角变量
        border_radius = self._current_theme.border_radius
        for attr_name in dir(border_radius):
            if not attr_name.startswith('_'):
                var_name = f"${{{attr_name}}}"
                if var_name in value:
                    radius_value = str(getattr(border_radius, attr_name))
                    value = value.replace(var_name, f"{radius_value}px")
        
        return value
    
    def export_theme_config(self, theme_name: str) -> Optional[Dict]:
        """导出主题配置"""
        theme = self.get_theme(theme_name)
        if not theme:
            return None
        
        return {
            "name": theme.name,
            "colors": theme.colors.__dict__,
            "typography": theme.typography.__dict__,
            "spacing": theme.spacing.__dict__,
            "border_radius": theme.border_radius.__dict__,
            "shadow": theme.shadow.__dict__
        }
    
    def save_current_theme_preference(self) -> None:
        """保存当前主题偏好设置"""
        # 这里可以实现保存到配置文件的逻辑
        current_theme = self.get_current_theme_name()
        self.logger.info(f"Saving theme preference: {current_theme}")
        # TODO: 实现配置保存
    
    def load_theme_preference(self) -> str:
        """加载主题偏好设置"""
        # 这里可以实现从配置文件加载的逻辑
        # TODO: 实现配置加载
        return "light"  # 默认返回浅色主题


# 全局主题管理器实例
_theme_manager: Optional[ThemeManager] = None


def get_theme_manager() -> ThemeManager:
    """获取全局主题管理器实例"""
    global _theme_manager
    if _theme_manager is None:
        _theme_manager = ThemeManager()
    return _theme_manager
