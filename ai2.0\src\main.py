"""AI视频生成器 2.0 主程序入口

应用程序的启动入口点。
"""

import sys
import signal
from pathlib import Path
from typing import Optional

from PyQt6.QtWidgets import QApplication

from src.core.config import ConfigManager
from src.ui.main_window import MainWindow
from src.ui.themes import get_theme_manager
from src.utils.logger import configure_logging, get_logger


class Application:
    """应用程序主类"""

    def __init__(self):
        self.qt_app: Optional[QApplication] = None
        self.config: Optional[ConfigManager] = None
        self.main_window: Optional[MainWindow] = None
        self.logger = None
        self._shutdown_requested = False

    def initialize(self) -> bool:
        """初始化应用程序"""
        try:
            # 配置日志系统
            self._setup_logging()
            self.logger = get_logger(__name__)
            self.logger.info("Starting AI Video Generator 2.0...")

            # 加载配置
            self._load_config()

            # 初始化Qt应用
            self._setup_qt_application()

            # 初始化主题管理器
            theme_manager = get_theme_manager()

            # 创建主窗口
            self.main_window = MainWindow(self.config)

            self.logger.info("Application initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to initialize application: {e}")
            else:
                print(f"Failed to initialize application: {e}")
            return False
    
    def _setup_logging(self) -> None:
        """设置日志系统"""
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # 配置日志
        configure_logging()
    
    def _load_config(self) -> None:
        """加载配置"""
        try:
            self.config = ConfigManager()
        except Exception as e:
            print(f"Failed to load configuration: {e}")
            # 使用默认配置
            self.config = ConfigManager()
    
    def _setup_qt_application(self) -> None:
        """设置Qt应用程序"""
        if not QApplication.instance():
            self.qt_app = QApplication(sys.argv)
        else:
            self.qt_app = QApplication.instance()

        # 设置应用程序属性
        self.qt_app.setApplicationName("AI视频生成器")
        self.qt_app.setApplicationVersion("2.0.0")
        self.qt_app.setOrganizationName("AI Video Generator Team")

        # 设置信号处理
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self) -> None:
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, initiating shutdown...")
            self._shutdown_requested = True
            if self.qt_app:
                self.qt_app.quit()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def run(self) -> int:
        """运行应用程序"""
        try:
            if not self.initialize():
                return 1

            # 显示主窗口
            if self.main_window:
                self.main_window.show()

            # 运行Qt事件循环
            return self.qt_app.exec()

        except KeyboardInterrupt:
            self.logger.info("Application interrupted by user")
            return 0
        except Exception as e:
            if self.logger:
                self.logger.error(f"Application error: {e}")
            else:
                print(f"Application error: {e}")
            return 1
    
def main() -> int:
    """主函数"""
    app = Application()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())
