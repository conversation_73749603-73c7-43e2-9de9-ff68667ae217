"""AI视频生成器 2.0 主程序入口

应用程序的启动入口点。
"""

import sys
import asyncio
import signal
from pathlib import Path
from typing import Optional

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

from src.core.container import configure_container, get_container
from src.core.config import ConfigManager
from src.core.events import get_event_bus, ApplicationEvents, Event
from src.utils.logger import configure_logging, get_logger, LogLevel
from src.core.exceptions import ApplicationError, ConfigurationError


class Application:
    """应用程序主类"""
    
    def __init__(self):
        self.qt_app: Optional[QApplication] = None
        self.config: Optional[ConfigManager] = None
        self.container = None
        self.event_bus = None
        self.logger = None
        self._shutdown_requested = False
    
    async def initialize(self) -> None:
        """初始化应用程序"""
        try:
            # 配置日志系统
            self._setup_logging()
            self.logger = get_logger(__name__)
            self.logger.info("Starting AI Video Generator 2.0...")
            
            # 加载配置
            self._load_config()
            
            # 配置依赖注入容器
            self._setup_container()
            
            # 初始化事件系统
            await self._setup_events()
            
            # 初始化Qt应用
            self._setup_qt_application()
            
            # 发布启动事件
            await self.event_bus.publish(Event(
                name=ApplicationEvents.STARTUP,
                data={'version': '2.0.0'}
            ))
            
            self.logger.info("Application initialized successfully")
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to initialize application: {e}")
            raise ApplicationError(f"Application initialization failed: {e}", cause=e)
    
    def _setup_logging(self) -> None:
        """设置日志系统"""
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 配置日志
        configure_logging(
            level=LogLevel.INFO,
            console_enabled=True,
            file_enabled=True,
            structured_logs=False,
            log_dir=log_dir
        )
    
    def _load_config(self) -> None:
        """加载配置"""
        try:
            config_dir = Path("config")
            if not config_dir.exists():
                raise ConfigurationError(f"Configuration directory not found: {config_dir}")
            
            self.config = ConfigManager(config_dir)
            
            # 根据配置调整日志级别
            log_level_str = self.config.app.log_level.upper()
            if hasattr(LogLevel, log_level_str):
                log_level = getattr(LogLevel, log_level_str)
                configure_logging(level=log_level)
            
        except Exception as e:
            raise ConfigurationError(f"Failed to load configuration: {e}", cause=e)
    
    def _setup_container(self) -> None:
        """设置依赖注入容器"""
        self.container = configure_container()
        
        # 注册配置管理器
        self.container.register_singleton(ConfigManager, factory=lambda: self.config)
    
    async def _setup_events(self) -> None:
        """设置事件系统"""
        self.event_bus = get_event_bus()
        await self.event_bus.start()
        
        # 注册事件处理器
        self._register_event_handlers()
    
    def _register_event_handlers(self) -> None:
        """注册事件处理器"""
        # 应用程序关闭事件处理器
        async def handle_shutdown(event: Event):
            self.logger.info("Handling application shutdown...")
            await self._cleanup()
        
        self.event_bus.subscribe(ApplicationEvents.SHUTDOWN, handle_shutdown)
        
        # 错误事件处理器
        async def handle_error(event: Event):
            error_data = event.data
            self.logger.error(f"Application error: {error_data}")
        
        self.event_bus.subscribe(ApplicationEvents.ERROR, handle_error)
    
    def _setup_qt_application(self) -> None:
        """设置Qt应用程序"""
        if not QApplication.instance():
            self.qt_app = QApplication(sys.argv)
        else:
            self.qt_app = QApplication.instance()
        
        # 设置应用程序属性
        self.qt_app.setApplicationName(self.config.app.name)
        self.qt_app.setApplicationVersion(self.config.app.version)
        self.qt_app.setOrganizationName("AI Video Generator Team")
        
        # 设置样式
        self._setup_application_style()
        
        # 设置信号处理
        self._setup_signal_handlers()
    
    def _setup_application_style(self) -> None:
        """设置应用程序样式"""
        # 这里可以加载主题和样式
        pass
    
    def _setup_signal_handlers(self) -> None:
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, initiating shutdown...")
            self._shutdown_requested = True
            if self.qt_app:
                self.qt_app.quit()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def run(self) -> int:
        """运行应用程序"""
        try:
            await self.initialize()
            
            # 创建主窗口
            main_window = await self._create_main_window()
            main_window.show()
            
            # 运行Qt事件循环
            return await self._run_qt_event_loop()
            
        except KeyboardInterrupt:
            self.logger.info("Application interrupted by user")
            return 0
        except Exception as e:
            if self.logger:
                self.logger.error(f"Application error: {e}")
            else:
                print(f"Application error: {e}")
            return 1
        finally:
            await self._cleanup()
    
    async def _create_main_window(self):
        """创建主窗口"""
        # 这里将创建主窗口
        # 暂时返回一个简单的窗口
        from PyQt6.QtWidgets import QMainWindow, QLabel
        
        window = QMainWindow()
        window.setWindowTitle(self.config.app.name)
        window.resize(self.config.ui.window_width, self.config.ui.window_height)
        window.setMinimumSize(self.config.ui.min_width, self.config.ui.min_height)
        
        # 临时标签
        label = QLabel("AI视频生成器 2.0 - 开发中...")
        label.setStyleSheet("font-size: 24px; text-align: center; padding: 50px;")
        window.setCentralWidget(label)
        
        return window
    
    async def _run_qt_event_loop(self) -> int:
        """运行Qt事件循环"""
        # 创建定时器来处理异步任务
        timer = QTimer()
        timer.timeout.connect(lambda: None)  # 保持事件循环活跃
        timer.start(100)  # 100ms间隔
        
        # 运行Qt应用
        return self.qt_app.exec()
    
    async def _cleanup(self) -> None:
        """清理资源"""
        try:
            if self.event_bus:
                await self.event_bus.publish(Event(
                    name=ApplicationEvents.SHUTDOWN,
                    data={'reason': 'normal_shutdown'}
                ))
                await self.event_bus.stop()
            
            if self.logger:
                self.logger.info("Application shutdown completed")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error during cleanup: {e}")


async def main() -> int:
    """主函数"""
    app = Application()
    return await app.run()


def sync_main() -> int:
    """同步主函数入口"""
    try:
        return asyncio.run(main())
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        return 0
    except Exception as e:
        print(f"Fatal error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(sync_main())
