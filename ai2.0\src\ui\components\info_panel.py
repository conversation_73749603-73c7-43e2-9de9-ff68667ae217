"""信息面板

右侧信息显示面板组件。
"""

from typing import Dict, Any, List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QScrollArea, QProgressBar, QPushButton, QTextEdit
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont

from src.ui.base import BaseWidget
from src.utils.logger import get_logger


class InfoPanel(BaseWidget):
    """信息面板"""
    
    # 信号定义
    action_requested = pyqtSignal(str, dict)  # 操作请求信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        self._setup_ui()
        self._setup_timer()
    
    def _setup_ui(self) -> None:
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # 内容widget
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(16, 16, 16, 16)
        content_layout.setSpacing(16)
        
        # 项目信息区域
        self.project_info_frame = self._create_project_info_section()
        content_layout.addWidget(self.project_info_frame)
        
        # 进度信息区域
        self.progress_frame = self._create_progress_section()
        content_layout.addWidget(self.progress_frame)
        
        # 系统状态区域
        self.system_status_frame = self._create_system_status_section()
        content_layout.addWidget(self.system_status_frame)
        
        # 日志区域
        self.log_frame = self._create_log_section()
        content_layout.addWidget(self.log_frame)
        
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
    
    def _create_project_info_section(self) -> QFrame:
        """创建项目信息区域"""
        frame = QFrame()
        frame.setProperty("class", "info-section")
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(12, 12, 12, 12)
        
        # 标题
        title = QLabel("项目信息")
        title.setProperty("class", "section-title")
        font = QFont()
        font.setPointSize(14)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # 项目名称
        self.project_name_label = QLabel("未选择项目")
        self.project_name_label.setProperty("class", "info-text")
        layout.addWidget(self.project_name_label)
        
        # 项目状态
        self.project_status_label = QLabel("状态: 空闲")
        self.project_status_label.setProperty("class", "info-text")
        layout.addWidget(self.project_status_label)
        
        # 分镜数量
        self.storyboard_count_label = QLabel("分镜数: 0")
        self.storyboard_count_label.setProperty("class", "info-text")
        layout.addWidget(self.storyboard_count_label)
        
        # 媒体文件数量
        self.media_count_label = QLabel("媒体文件: 0")
        self.media_count_label.setProperty("class", "info-text")
        layout.addWidget(self.media_count_label)
        
        return frame
    
    def _create_progress_section(self) -> QFrame:
        """创建进度信息区域"""
        frame = QFrame()
        frame.setProperty("class", "info-section")
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(12, 12, 12, 12)
        
        # 标题
        title = QLabel("处理进度")
        title.setProperty("class", "section-title")
        font = QFont()
        font.setPointSize(14)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # 当前任务
        self.current_task_label = QLabel("当前任务: 无")
        self.current_task_label.setProperty("class", "info-text")
        layout.addWidget(self.current_task_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 进度文本
        self.progress_text_label = QLabel("")
        self.progress_text_label.setProperty("class", "info-text")
        self.progress_text_label.setVisible(False)
        layout.addWidget(self.progress_text_label)
        
        return frame
    
    def _create_system_status_section(self) -> QFrame:
        """创建系统状态区域"""
        frame = QFrame()
        frame.setProperty("class", "info-section")
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(12, 12, 12, 12)
        
        # 标题
        title = QLabel("系统状态")
        title.setProperty("class", "section-title")
        font = QFont()
        font.setPointSize(14)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # AI服务状态
        self.ai_service_label = QLabel("AI服务: 检查中...")
        self.ai_service_label.setProperty("class", "info-text")
        layout.addWidget(self.ai_service_label)
        
        # 内存使用
        self.memory_usage_label = QLabel("内存使用: 0 MB")
        self.memory_usage_label.setProperty("class", "info-text")
        layout.addWidget(self.memory_usage_label)
        
        # CPU使用
        self.cpu_usage_label = QLabel("CPU使用: 0%")
        self.cpu_usage_label.setProperty("class", "info-text")
        layout.addWidget(self.cpu_usage_label)
        
        return frame
    
    def _create_log_section(self) -> QFrame:
        """创建日志区域"""
        frame = QFrame()
        frame.setProperty("class", "info-section")
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(12, 12, 12, 12)
        
        # 标题
        title_layout = QHBoxLayout()
        title = QLabel("系统日志")
        title.setProperty("class", "section-title")
        font = QFont()
        font.setPointSize(14)
        font.setBold(True)
        title.setFont(font)
        title_layout.addWidget(title)
        
        # 清除日志按钮
        clear_btn = QPushButton("清除")
        clear_btn.setProperty("class", "small")
        clear_btn.clicked.connect(self._clear_logs)
        title_layout.addWidget(clear_btn)
        
        layout.addLayout(title_layout)
        
        # 日志文本区域
        self.log_text = QTextEdit()
        self.log_text.setProperty("class", "log-text")
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(150)
        layout.addWidget(self.log_text)
        
        return frame
    
    def _setup_timer(self) -> None:
        """设置定时器"""
        # 系统状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_system_status)
        self.status_timer.start(5000)  # 每5秒更新一次
    
    def _update_system_status(self) -> None:
        """更新系统状态"""
        try:
            import psutil
            
            # 更新内存使用
            memory = psutil.virtual_memory()
            memory_mb = memory.used // (1024 * 1024)
            self.memory_usage_label.setText(f"内存使用: {memory_mb} MB")
            
            # 更新CPU使用
            cpu_percent = psutil.cpu_percent(interval=None)
            self.cpu_usage_label.setText(f"CPU使用: {cpu_percent:.1f}%")
            
        except ImportError:
            # 如果没有psutil，显示占位信息
            self.memory_usage_label.setText("内存使用: 未知")
            self.cpu_usage_label.setText("CPU使用: 未知")
        except Exception as e:
            self.logger.error(f"Failed to update system status: {e}")
    
    def update_project_info(self, project_data: Dict[str, Any]) -> None:
        """更新项目信息"""
        self.project_name_label.setText(f"项目: {project_data.get('name', '未知')}")
        self.project_status_label.setText(f"状态: {project_data.get('status', '未知')}")
        self.storyboard_count_label.setText(f"分镜数: {project_data.get('storyboard_count', 0)}")
        self.media_count_label.setText(f"媒体文件: {project_data.get('media_count', 0)}")
    
    def update_progress(self, task_name: str, progress: int, message: str = "") -> None:
        """更新进度信息"""
        self.current_task_label.setText(f"当前任务: {task_name}")
        
        if progress >= 0:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(progress)
            
            self.progress_text_label.setVisible(True)
            self.progress_text_label.setText(f"{progress}% - {message}")
        else:
            self.progress_bar.setVisible(False)
            self.progress_text_label.setVisible(False)
    
    def clear_progress(self) -> None:
        """清除进度信息"""
        self.current_task_label.setText("当前任务: 无")
        self.progress_bar.setVisible(False)
        self.progress_text_label.setVisible(False)
    
    def update_ai_service_status(self, status: str) -> None:
        """更新AI服务状态"""
        self.ai_service_label.setText(f"AI服务: {status}")
    
    def add_log_message(self, message: str, level: str = "INFO") -> None:
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        
        self.log_text.append(log_entry)
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
        # 限制日志行数
        document = self.log_text.document()
        if document.blockCount() > 100:
            cursor = self.log_text.textCursor()
            cursor.movePosition(cursor.MoveOperation.Start)
            cursor.select(cursor.SelectionType.BlockUnderCursor)
            cursor.removeSelectedText()
    
    def _clear_logs(self) -> None:
        """清除日志"""
        self.log_text.clear()
        self.add_log_message("日志已清除")
    
    def _apply_theme(self) -> None:
        """应用主题"""
        # 这里可以根据主题更新样式
        pass
