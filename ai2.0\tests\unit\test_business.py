"""业务逻辑层单元测试

测试核心业务逻辑处理器。
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import uuid

from src.business.storyboard.generator import StoryboardGenerator
from src.business.storyboard.analyzer import StoryboardAnalyzer
from src.business.media.processor import MediaProcessor
from src.business.consistency.manager import ConsistencyManager
from src.models.project import Project
from src.models.storyboard import Storyboard
from src.models.shot import Shot
from src.services.base import ServiceResponse
from src.core.exceptions import ValidationError, BusinessLogicError


class TestStoryboardGenerator:
    """测试分镜生成器"""
    
    @pytest.fixture
    def mock_llm_service(self):
        """模拟LLM服务"""
        service = AsyncMock()
        service.chat_completion.return_value = ServiceResponse.success_response(
            "这是一个生成的分镜内容"
        )
        return service
    
    @pytest.fixture
    def mock_project_repo(self):
        """模拟项目仓库"""
        repo = AsyncMock()
        return repo
    
    @pytest.fixture
    def storyboard_generator(self, mock_llm_service, mock_project_repo):
        """分镜生成器实例"""
        return StoryboardGenerator(
            llm_service=mock_llm_service,
            project_repository=mock_project_repo
        )
    
    @pytest.fixture
    def sample_project(self):
        """示例项目"""
        return Project(
            id=uuid.uuid4(),
            title="测试项目",
            description="测试描述",
            content="从前有一个小女孩，她住在森林里...",
            style="动画",
            language="zh-CN",
            video_width=1920,
            video_height=1080,
            video_fps=24.0,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    @pytest.mark.asyncio
    async def test_generate_from_text_success(self, storyboard_generator, sample_project):
        """测试从文本生成分镜成功"""
        # 模拟LLM返回结构化分镜数据
        storyboard_generator.llm_service.chat_completion.return_value = ServiceResponse.success_response(
            """
            {
                "shots": [
                    {
                        "sequence_number": 1,
                        "shot_type": "wide_shot",
                        "content": "森林全景，小女孩从远处走来",
                        "duration": 3.0,
                        "image_prompt": "Wide shot of forest with girl walking from distance",
                        "video_prompt": "Girl walking through forest",
                        "voice_prompt": "小女孩走在森林小径上"
                    },
                    {
                        "sequence_number": 2,
                        "shot_type": "medium_shot",
                        "content": "小女孩的中景镜头",
                        "duration": 2.5,
                        "image_prompt": "Medium shot of young girl in forest",
                        "video_prompt": "Girl looking around curiously",
                        "voice_prompt": "她好奇地四处张望"
                    }
                ]
            }
            """
        )
        
        result = await storyboard_generator.generate_from_text(
            text=sample_project.content,
            style=sample_project.style,
            project_id=sample_project.id
        )
        
        assert result.success is True
        storyboard = result.data
        assert isinstance(storyboard, Storyboard)
        assert storyboard.project_id == sample_project.id
        assert len(storyboard.shots) == 2
        assert storyboard.shots[0].sequence_number == 1
        assert storyboard.shots[1].sequence_number == 2
    
    @pytest.mark.asyncio
    async def test_generate_from_text_empty_input(self, storyboard_generator):
        """测试空输入处理"""
        with pytest.raises(ValidationError, match="输入文本不能为空"):
            await storyboard_generator.generate_from_text(
                text="",
                style="动画",
                project_id=uuid.uuid4()
            )
    
    @pytest.mark.asyncio
    async def test_generate_from_text_llm_error(self, storyboard_generator, sample_project):
        """测试LLM服务错误"""
        # 模拟LLM服务错误
        storyboard_generator.llm_service.chat_completion.return_value = ServiceResponse.error_response(
            "LLM service unavailable"
        )
        
        result = await storyboard_generator.generate_from_text(
            text=sample_project.content,
            style=sample_project.style,
            project_id=sample_project.id
        )
        
        assert result.success is False
        assert "LLM service unavailable" in result.error_message
    
    @pytest.mark.asyncio
    async def test_validate_storyboard_structure(self, storyboard_generator):
        """测试分镜结构验证"""
        # 有效结构
        valid_data = {
            "shots": [
                {
                    "sequence_number": 1,
                    "shot_type": "wide_shot",
                    "content": "测试内容",
                    "duration": 3.0,
                    "image_prompt": "test prompt",
                    "video_prompt": "test video",
                    "voice_prompt": "测试语音"
                }
            ]
        }
        
        assert storyboard_generator._validate_storyboard_structure(valid_data) is True
        
        # 无效结构 - 缺少shots
        invalid_data = {"title": "test"}
        assert storyboard_generator._validate_storyboard_structure(invalid_data) is False
        
        # 无效结构 - shots为空
        empty_shots = {"shots": []}
        assert storyboard_generator._validate_storyboard_structure(empty_shots) is False


class TestStoryboardAnalyzer:
    """测试分镜分析器"""
    
    @pytest.fixture
    def mock_llm_service(self):
        """模拟LLM服务"""
        service = AsyncMock()
        return service
    
    @pytest.fixture
    def storyboard_analyzer(self, mock_llm_service):
        """分镜分析器实例"""
        return StoryboardAnalyzer(llm_service=mock_llm_service)
    
    @pytest.fixture
    def sample_storyboard(self):
        """示例分镜"""
        storyboard = Storyboard(
            id=uuid.uuid4(),
            project_id=uuid.uuid4(),
            name="测试分镜",
            content="测试分镜内容",
            generation_settings={"style": "动画"},
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        # 添加镜头
        shot1 = Shot(
            id=uuid.uuid4(),
            storyboard_id=storyboard.id,
            name="镜头1",
            sequence_number=1,
            shot_type="wide_shot",
            content="森林全景",
            duration=3.0,
            image_prompt="Wide shot of forest",
            video_prompt="Forest scene",
            voice_prompt="森林场景"
        )
        
        shot2 = Shot(
            id=uuid.uuid4(),
            storyboard_id=storyboard.id,
            name="镜头2",
            sequence_number=2,
            shot_type="medium_shot",
            content="小女孩中景",
            duration=2.5,
            image_prompt="Medium shot of girl",
            video_prompt="Girl walking",
            voice_prompt="小女孩走路"
        )
        
        storyboard.shots = [shot1, shot2]
        return storyboard
    
    @pytest.mark.asyncio
    async def test_analyze_narrative_flow(self, storyboard_analyzer, sample_storyboard):
        """测试叙事流程分析"""
        # 模拟LLM分析结果
        storyboard_analyzer.llm_service.chat_completion.return_value = ServiceResponse.success_response(
            """
            {
                "narrative_coherence": 0.85,
                "pacing_score": 0.78,
                "emotional_arc": "rising",
                "suggestions": [
                    "考虑在镜头1和镜头2之间添加过渡镜头",
                    "镜头2的时长可以稍微延长"
                ],
                "strengths": [
                    "开场建立了良好的环境氛围",
                    "角色引入自然"
                ]
            }
            """
        )
        
        result = await storyboard_analyzer.analyze_narrative_flow(sample_storyboard)
        
        assert result.success is True
        analysis = result.data
        assert analysis["narrative_coherence"] == 0.85
        assert analysis["pacing_score"] == 0.78
        assert len(analysis["suggestions"]) == 2
        assert len(analysis["strengths"]) == 2
    
    @pytest.mark.asyncio
    async def test_analyze_visual_consistency(self, storyboard_analyzer, sample_storyboard):
        """测试视觉一致性分析"""
        storyboard_analyzer.llm_service.chat_completion.return_value = ServiceResponse.success_response(
            """
            {
                "consistency_score": 0.92,
                "style_coherence": 0.88,
                "character_consistency": 0.95,
                "environment_consistency": 0.90,
                "issues": [
                    "镜头2中的光照条件与镜头1不一致"
                ],
                "recommendations": [
                    "统一所有镜头的光照设置",
                    "确保角色服装在所有镜头中保持一致"
                ]
            }
            """
        )
        
        result = await storyboard_analyzer.analyze_visual_consistency(sample_storyboard)
        
        assert result.success is True
        analysis = result.data
        assert analysis["consistency_score"] == 0.92
        assert analysis["style_coherence"] == 0.88
        assert len(analysis["issues"]) == 1
        assert len(analysis["recommendations"]) == 2
    
    @pytest.mark.asyncio
    async def test_calculate_total_duration(self, storyboard_analyzer, sample_storyboard):
        """测试总时长计算"""
        total_duration = storyboard_analyzer.calculate_total_duration(sample_storyboard)
        
        # 3.0 + 2.5 = 5.5秒
        assert total_duration == 5.5
    
    def test_get_shot_type_distribution(self, storyboard_analyzer, sample_storyboard):
        """测试镜头类型分布"""
        distribution = storyboard_analyzer.get_shot_type_distribution(sample_storyboard)
        
        assert distribution["wide_shot"] == 1
        assert distribution["medium_shot"] == 1
        assert distribution["total_shots"] == 2


class TestMediaProcessor:
    """测试媒体处理器"""
    
    @pytest.fixture
    def mock_image_service(self):
        """模拟图像服务"""
        service = AsyncMock()
        service.generate_image.return_value = ServiceResponse.success_response(
            "/path/to/generated/image.png"
        )
        return service
    
    @pytest.fixture
    def mock_voice_service(self):
        """模拟语音服务"""
        service = AsyncMock()
        service.text_to_speech.return_value = ServiceResponse.success_response(
            "/path/to/generated/voice.mp3"
        )
        return service
    
    @pytest.fixture
    def mock_video_service(self):
        """模拟视频服务"""
        service = AsyncMock()
        service.text_to_video.return_value = ServiceResponse.success_response(
            "/path/to/generated/video.mp4"
        )
        return service
    
    @pytest.fixture
    def media_processor(self, mock_image_service, mock_voice_service, mock_video_service):
        """媒体处理器实例"""
        return MediaProcessor(
            image_service=mock_image_service,
            voice_service=mock_voice_service,
            video_service=mock_video_service
        )
    
    @pytest.fixture
    def sample_shot(self):
        """示例镜头"""
        return Shot(
            id=uuid.uuid4(),
            storyboard_id=uuid.uuid4(),
            name="测试镜头",
            sequence_number=1,
            shot_type="medium_shot",
            content="小女孩在森林中行走",
            duration=3.0,
            image_prompt="A young girl walking in forest, medium shot",
            video_prompt="Girl walking through forest path",
            voice_prompt="小女孩轻快地走在森林小径上"
        )
    
    @pytest.mark.asyncio
    async def test_process_shot_images(self, media_processor, sample_shot):
        """测试处理镜头图像"""
        result = await media_processor.process_shot_images([sample_shot])
        
        assert result.success is True
        processed_shots = result.data
        assert len(processed_shots) == 1
        assert processed_shots[0].image_path == "/path/to/generated/image.png"
        
        # 验证图像服务被调用
        media_processor.image_service.generate_image.assert_called_once_with(
            sample_shot.image_prompt,
            style="default"
        )
    
    @pytest.mark.asyncio
    async def test_process_shot_voices(self, media_processor, sample_shot):
        """测试处理镜头语音"""
        result = await media_processor.process_shot_voices([sample_shot])
        
        assert result.success is True
        processed_shots = result.data
        assert len(processed_shots) == 1
        assert processed_shots[0].voice_path == "/path/to/generated/voice.mp3"
        
        # 验证语音服务被调用
        media_processor.voice_service.text_to_speech.assert_called_once_with(
            sample_shot.voice_prompt,
            voice="default"
        )
    
    @pytest.mark.asyncio
    async def test_process_shot_videos(self, media_processor, sample_shot):
        """测试处理镜头视频"""
        result = await media_processor.process_shot_videos([sample_shot])
        
        assert result.success is True
        processed_shots = result.data
        assert len(processed_shots) == 1
        assert processed_shots[0].video_path == "/path/to/generated/video.mp4"
        
        # 验证视频服务被调用
        media_processor.video_service.text_to_video.assert_called_once_with(
            sample_shot.video_prompt,
            duration=sample_shot.duration
        )
    
    @pytest.mark.asyncio
    async def test_process_shot_service_error(self, media_processor, sample_shot):
        """测试服务错误处理"""
        # 模拟图像服务错误
        media_processor.image_service.generate_image.return_value = ServiceResponse.error_response(
            "Image generation failed"
        )
        
        result = await media_processor.process_shot_images([sample_shot])
        
        assert result.success is False
        assert "Image generation failed" in result.error_message


class TestConsistencyManager:
    """测试一致性管理器"""
    
    @pytest.fixture
    def mock_llm_service(self):
        """模拟LLM服务"""
        service = AsyncMock()
        return service
    
    @pytest.fixture
    def consistency_manager(self, mock_llm_service):
        """一致性管理器实例"""
        return ConsistencyManager(llm_service=mock_llm_service)
    
    @pytest.fixture
    def sample_shots(self):
        """示例镜头列表"""
        shots = []
        for i in range(3):
            shot = Shot(
                id=uuid.uuid4(),
                storyboard_id=uuid.uuid4(),
                name=f"镜头{i+1}",
                sequence_number=i+1,
                shot_type="medium_shot",
                content=f"镜头{i+1}内容",
                duration=3.0,
                image_prompt=f"Image prompt {i+1}",
                video_prompt=f"Video prompt {i+1}",
                voice_prompt=f"语音提示{i+1}"
            )
            shots.append(shot)
        return shots
    
    @pytest.mark.asyncio
    async def test_check_character_consistency(self, consistency_manager, sample_shots):
        """测试角色一致性检查"""
        # 模拟LLM分析结果
        consistency_manager.llm_service.chat_completion.return_value = ServiceResponse.success_response(
            """
            {
                "consistency_score": 0.88,
                "character_appearances": {
                    "小女孩": {
                        "consistent": true,
                        "variations": ["服装颜色在镜头2中略有不同"]
                    }
                },
                "issues": [
                    "镜头2中角色服装与其他镜头不一致"
                ],
                "suggestions": [
                    "统一角色在所有镜头中的服装描述",
                    "添加角色外观的详细设定"
                ]
            }
            """
        )
        
        result = await consistency_manager.check_character_consistency(sample_shots)
        
        assert result.success is True
        analysis = result.data
        assert analysis["consistency_score"] == 0.88
        assert "小女孩" in analysis["character_appearances"]
        assert len(analysis["issues"]) == 1
        assert len(analysis["suggestions"]) == 2
    
    @pytest.mark.asyncio
    async def test_check_environment_consistency(self, consistency_manager, sample_shots):
        """测试环境一致性检查"""
        consistency_manager.llm_service.chat_completion.return_value = ServiceResponse.success_response(
            """
            {
                "consistency_score": 0.92,
                "environment_elements": {
                    "森林": {
                        "consistent": true,
                        "description": "茂密的绿色森林"
                    },
                    "小径": {
                        "consistent": true,
                        "description": "蜿蜒的土路"
                    }
                },
                "lighting_consistency": 0.85,
                "weather_consistency": 0.95,
                "issues": [],
                "suggestions": [
                    "保持所有镜头中的光照方向一致"
                ]
            }
            """
        )
        
        result = await consistency_manager.check_environment_consistency(sample_shots)
        
        assert result.success is True
        analysis = result.data
        assert analysis["consistency_score"] == 0.92
        assert "森林" in analysis["environment_elements"]
        assert analysis["lighting_consistency"] == 0.85
        assert len(analysis["suggestions"]) == 1
    
    def test_extract_character_mentions(self, consistency_manager, sample_shots):
        """测试提取角色提及"""
        # 修改镜头内容包含角色
        sample_shots[0].content = "小女孩走在森林里"
        sample_shots[1].content = "小女孩停下来看花"
        sample_shots[2].content = "小女孩继续前行"
        
        characters = consistency_manager._extract_character_mentions(sample_shots)
        
        assert "小女孩" in characters
        assert characters["小女孩"] == 3  # 出现3次
