#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器3.0 - 快速测试脚本
用于验证配置和服务连接状态
"""

import json
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.mcp_service_manager import MCPServiceManager
from src.services.llm_service import LLMServiceManager
from src.services.translation_service import TranslationServiceManager
from src.services.tts_service import TTSServiceManager
from src.services.image_service import ImageServiceManager
from src.services.video_service import VideoServiceManager
from src.services.social_media_service import SocialMediaServiceManager

class QuickTester:
    def __init__(self, config_path: str = "config.json"):
        self.config_path = config_path
        self.config = None
        self.test_results = {}
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            print(f"✅ 配置文件加载成功: {self.config_path}")
            return True
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return False
    
    async def test_llm_service(self):
        """测试LLM服务"""
        print("\n🧠 测试LLM服务...")
        try:
            manager = LLMServiceManager(self.config.get('llm', {}))
            await manager.initialize()
            
            # 测试简单对话
            response = await manager.generate_text(
                prompt="你好，请简单介绍一下你自己。",
                max_tokens=100
            )
            
            if response and response.content:
                print(f"✅ LLM服务正常: {response.content[:50]}...")
                self.test_results['llm'] = {'status': 'success', 'response': response.content[:100]}
            else:
                print("❌ LLM服务响应为空")
                self.test_results['llm'] = {'status': 'failed', 'error': 'Empty response'}
                
        except Exception as e:
            print(f"❌ LLM服务测试失败: {e}")
            self.test_results['llm'] = {'status': 'failed', 'error': str(e)}
    
    async def test_translation_service(self):
        """测试翻译服务"""
        print("\n🌐 测试翻译服务...")
        try:
            manager = TranslationServiceManager(self.config.get('translation', {}))
            await manager.initialize()
            
            # 测试中英翻译
            response = await manager.translate(
                text="你好，世界！",
                target_language="en",
                source_language="zh"
            )
            
            if response and response.translated_text:
                print(f"✅ 翻译服务正常: {response.translated_text}")
                self.test_results['translation'] = {'status': 'success', 'response': response.translated_text}
            else:
                print("❌ 翻译服务响应为空")
                self.test_results['translation'] = {'status': 'failed', 'error': 'Empty response'}
                
        except Exception as e:
            print(f"❌ 翻译服务测试失败: {e}")
            self.test_results['translation'] = {'status': 'failed', 'error': str(e)}
    
    async def test_image_service(self):
        """测试图像生成服务"""
        print("\n🎨 测试图像生成服务...")
        try:
            manager = ImageServiceManager(self.config.get('image_generation', {}))
            await manager.initialize()
            
            # 测试图像生成（使用免费服务）
            response = await manager.generate_image(
                prompt="A beautiful sunset over mountains",
                width=512,
                height=512
            )
            
            if response and response.image_url:
                print(f"✅ 图像生成服务正常: {response.image_url}")
                self.test_results['image'] = {'status': 'success', 'url': response.image_url}
            else:
                print("❌ 图像生成服务响应为空")
                self.test_results['image'] = {'status': 'failed', 'error': 'Empty response'}
                
        except Exception as e:
            print(f"❌ 图像生成服务测试失败: {e}")
            self.test_results['image'] = {'status': 'failed', 'error': str(e)}
    
    def test_config_structure(self):
        """测试配置文件结构"""
        print("\n📋 检查配置文件结构...")
        required_sections = ['llm', 'translation', 'tts', 'image_generation', 'video_generation', 'social_media']
        missing_sections = []
        
        for section in required_sections:
            if section not in self.config:
                missing_sections.append(section)
        
        if missing_sections:
            print(f"❌ 缺少配置节: {', '.join(missing_sections)}")
            self.test_results['config'] = {'status': 'failed', 'missing': missing_sections}
        else:
            print("✅ 配置文件结构完整")
            self.test_results['config'] = {'status': 'success'}
    
    def check_api_keys(self):
        """检查API密钥配置"""
        print("\n🔑 检查API密钥配置...")
        api_keys_status = {}
        
        # 检查LLM API密钥
        llm_clients = self.config.get('llm', {}).get('clients', {})
        for client_name, client_config in llm_clients.items():
            api_key = client_config.get('api_key', '')
            if api_key and api_key != 'YOUR_API_KEY_HERE':
                api_keys_status[f'llm_{client_name}'] = '✅ 已配置'
            else:
                api_keys_status[f'llm_{client_name}'] = '❌ 未配置'
        
        # 检查翻译API密钥
        translation_clients = self.config.get('translation', {}).get('clients', {})
        for client_name, client_config in translation_clients.items():
            if client_name == 'baidu':
                app_id = client_config.get('app_id', '')
                secret_key = client_config.get('secret_key', '')
                if app_id and secret_key:
                    api_keys_status[f'translation_{client_name}'] = '✅ 已配置'
                else:
                    api_keys_status[f'translation_{client_name}'] = '❌ 未配置'
            else:
                api_key = client_config.get('api_key', '')
                if api_key and api_key != 'YOUR_API_KEY_HERE':
                    api_keys_status[f'translation_{client_name}'] = '✅ 已配置'
                else:
                    api_keys_status[f'translation_{client_name}'] = '❌ 未配置'
        
        # 打印API密钥状态
        for service, status in api_keys_status.items():
            print(f"  {service}: {status}")
        
        self.test_results['api_keys'] = api_keys_status
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始AI视频生成器3.0快速测试...")
        print("=" * 50)
        
        # 加载配置
        if not self.load_config():
            return
        
        # 检查配置结构
        self.test_config_structure()
        
        # 检查API密钥
        self.check_api_keys()
        
        # 测试服务（仅测试有API密钥的服务）
        await self.test_llm_service()
        await self.test_translation_service()
        await self.test_image_service()
        
        # 打印测试摘要
        self.print_summary()
        
        # 保存测试结果
        self.save_results()
    
    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "=" * 50)
        print("📊 测试摘要")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() 
                          if isinstance(result, dict) and result.get('status') == 'success')
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {total_tests - passed_tests}")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "成功率: 0%")
        
        # 详细结果
        for test_name, result in self.test_results.items():
            if isinstance(result, dict):
                status = result.get('status', 'unknown')
                if status == 'success':
                    print(f"✅ {test_name}: 通过")
                else:
                    error = result.get('error', 'Unknown error')
                    print(f"❌ {test_name}: 失败 - {error}")
            else:
                print(f"ℹ️ {test_name}: {result}")
    
    def save_results(self):
        """保存测试结果"""
        try:
            with open('quick_test_results.json', 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            print(f"\n💾 测试结果已保存到: quick_test_results.json")
        except Exception as e:
            print(f"\n❌ 保存测试结果失败: {e}")

async def main():
    """主函数"""
    tester = QuickTester()
    await tester.run_all_tests()
    
    print("\n🎉 快速测试完成！")
    print("\n💡 下一步建议:")
    print("1. 如果测试通过，可以运行 python main.py 开始使用")
    print("2. 如果有测试失败，请检查对应的API密钥配置")
    print("3. 运行 python start.py 查看完整的功能演示")

if __name__ == "__main__":
    asyncio.run(main())