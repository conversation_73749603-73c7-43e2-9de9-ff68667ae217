# -*- coding: utf-8 -*-
"""
LLM服务实现
集成智谱AI、通义千问、Deepseek、Google Gemini等大语言模型服务
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, List, Optional
import aiohttp
from dataclasses import dataclass

from ..core.mcp_service_manager import (
    MCPServiceInterface, ServiceRequest, ServiceResponse, ServiceType, ServiceStatus
)

logger = logging.getLogger(__name__)

@dataclass
class LLMRequest:
    """LLM请求数据结构"""
    prompt: str
    model: str = "auto"
    max_tokens: int = 2000
    temperature: float = 0.7
    system_prompt: Optional[str] = None
    stream: bool = False

@dataclass
class LLMResponse:
    """LLM响应数据结构"""
    content: str
    model_used: str
    tokens_used: int = 0
    cost: float = 0.0
    finish_reason: str = "stop"

class BaseLLMClient(MCPServiceInterface):
    """LLM客户端基类"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.api_key = config.get('key', '')
        self.base_url = config.get('url', '')
        self.model_name = config.get('model', '')
        self.session = None
    
    async def initialize(self) -> bool:
        """初始化LLM客户端"""
        try:
            if not self.api_key:
                logger.error(f"{self.name}: API密钥未配置")
                return False
            
            # 创建HTTP会话
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=300)
            )
            
            # 测试连接
            health_ok = await self.health_check()
            if health_ok:
                logger.info(f"{self.name}: 初始化成功")
                return True
            else:
                logger.warning(f"{self.name}: 健康检查失败")
                return False
                
        except Exception as e:
            logger.error(f"{self.name}: 初始化失败 - {e}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        if self.session:
            await self.session.close()
    
    async def process(self, request: ServiceRequest) -> ServiceResponse:
        """处理LLM请求"""
        start_time = time.time()
        
        try:
            action = request.parameters.get('action', 'generate')
            
            if action == 'generate':
                llm_request = LLMRequest(**request.parameters.get('llm_request', {}))
                result = await self.generate_text(llm_request)
                
                return ServiceResponse(
                    success=True,
                    data=result,
                    execution_time=time.time() - start_time,
                    service_name=self.name
                )
            
            elif action == 'generate_storyboard':
                article = request.parameters.get('article', '')
                language = request.parameters.get('language', 'zh')
                result = await self.generate_storyboard(article, language)
                
                return ServiceResponse(
                    success=True,
                    data=result,
                    execution_time=time.time() - start_time,
                    service_name=self.name
                )
            
            else:
                return ServiceResponse(
                    success=False,
                    error=f"不支持的操作: {action}",
                    service_name=self.name
                )
                
        except Exception as e:
            return ServiceResponse(
                success=False,
                error=str(e),
                execution_time=time.time() - start_time,
                service_name=self.name
            )
    
    async def generate_text(self, request: LLMRequest) -> LLMResponse:
        """生成文本（子类实现）"""
        raise NotImplementedError
    
    async def generate_storyboard(self, article: str, language: str = 'zh') -> List[Dict[str, Any]]:
        """生成五阶段分镜"""
        system_prompt = self._get_storyboard_system_prompt(language)
        user_prompt = self._get_storyboard_user_prompt(article, language)
        
        llm_request = LLMRequest(
            prompt=user_prompt,
            system_prompt=system_prompt,
            max_tokens=3000,
            temperature=0.3
        )
        
        response = await self.generate_text(llm_request)
        
        # 解析分镜JSON
        try:
            storyboard_data = json.loads(response.content)
            return storyboard_data.get('shots', [])
        except json.JSONDecodeError as e:
            logger.error(f"分镜JSON解析失败: {e}")
            # 尝试提取JSON部分
            content = response.content
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            if start_idx != -1 and end_idx != -1:
                try:
                    json_str = content[start_idx:end_idx]
                    storyboard_data = json.loads(json_str)
                    return storyboard_data.get('shots', [])
                except:
                    pass
            
            # 返回空分镜
            return []
    
    def _get_storyboard_system_prompt(self, language: str) -> str:
        """获取分镜系统提示词"""
        if language == 'en':
            return """
You are a professional video storyboard creator. Your task is to convert articles into detailed 5-stage video storyboards.

For each shot, provide:
1. Narration text (what will be spoken)
2. Visual description (what will be shown)
3. Motion prompt (camera movement and scene dynamics)
4. Duration (in seconds)
5. Style preferences

Output format must be valid JSON:
{
  "shots": [
    {
      "id": 1,
      "narration": "Spoken text",
      "visual_prompt": "Detailed visual description",
      "motion_prompt": "Camera and motion description",
      "duration": 5.0,
      "style": "realistic",
      "voice_settings": {
        "voice": "en-US-AriaNeural",
        "rate": 1.0,
        "volume": 1.0
      }
    }
  ]
}

Ensure each shot is 3-8 seconds long, with clear visual and audio elements.
"""
        else:
            return """
你是专业的视频分镜创作者。你的任务是将文章转换为详细的5阶段视频分镜。

每个镜头需要提供：
1. 旁白文本（将要朗读的内容）
2. 视觉描述（将要展示的画面）
3. 运动提示词（镜头运动和场景动态）
4. 时长（秒数）
5. 风格偏好

输出格式必须是有效的JSON：
{
  "shots": [
    {
      "id": 1,
      "narration": "旁白文本",
      "visual_prompt": "详细的视觉描述",
      "motion_prompt": "镜头和运动描述",
      "duration": 5.0,
      "style": "realistic",
      "voice_settings": {
        "voice": "zh-CN-YunxiNeural",
        "rate": 1.0,
        "volume": 1.0
      }
    }
  ]
}

确保每个镜头时长3-8秒，具有清晰的视觉和音频元素。
"""
    
    def _get_storyboard_user_prompt(self, article: str, language: str) -> str:
        """获取分镜用户提示词"""
        if language == 'en':
            return f"""
Please convert the following article into a 5-stage video storyboard:

{article}

Requirements:
- Create exactly 5 shots
- Each shot should be 3-8 seconds long
- Total video length should be 15-40 seconds
- Include engaging visual descriptions
- Add appropriate camera movements
- Use clear, concise narration
- Output valid JSON format only
"""
        else:
            return f"""
请将以下文章转换为5阶段视频分镜：

{article}

要求：
- 创建恰好5个镜头
- 每个镜头时长3-8秒
- 总视频时长15-40秒
- 包含引人入胜的视觉描述
- 添加适当的镜头运动
- 使用清晰简洁的旁白
- 仅输出有效的JSON格式
"""

class ZhipuAIClient(BaseLLMClient):
    """智谱AI客户端"""
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            # 发送简单的测试请求
            test_request = LLMRequest(
                prompt="你好",
                max_tokens=10
            )
            response = await self.generate_text(test_request)
            return len(response.content) > 0
        except:
            return False
    
    async def generate_text(self, request: LLMRequest) -> LLMResponse:
        """生成文本"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        messages = []
        if request.system_prompt:
            messages.append({
                'role': 'system',
                'content': request.system_prompt
            })
        
        messages.append({
            'role': 'user',
            'content': request.prompt
        })
        
        data = {
            'model': 'glm-4-flash',
            'messages': messages,
            'max_tokens': request.max_tokens,
            'temperature': request.temperature,
            'stream': request.stream
        }
        
        async with self.session.post(self.base_url, headers=headers, json=data) as response:
            if response.status == 200:
                result = await response.json()
                content = result['choices'][0]['message']['content']
                
                return LLMResponse(
                    content=content,
                    model_used='glm-4-flash',
                    tokens_used=result.get('usage', {}).get('total_tokens', 0)
                )
            else:
                error_text = await response.text()
                raise Exception(f"智谱AI API错误 {response.status}: {error_text}")

class TongyiClient(BaseLLMClient):
    """通义千问客户端"""
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            test_request = LLMRequest(
                prompt="你好",
                max_tokens=10
            )
            response = await self.generate_text(test_request)
            return len(response.content) > 0
        except:
            return False
    
    async def generate_text(self, request: LLMRequest) -> LLMResponse:
        """生成文本"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        messages = []
        if request.system_prompt:
            messages.append({
                'role': 'system',
                'content': request.system_prompt
            })
        
        messages.append({
            'role': 'user',
            'content': request.prompt
        })
        
        data = {
            'model': 'qwen-turbo',
            'messages': messages,
            'max_tokens': request.max_tokens,
            'temperature': request.temperature
        }
        
        async with self.session.post(self.base_url, headers=headers, json=data) as response:
            if response.status == 200:
                result = await response.json()
                content = result['choices'][0]['message']['content']
                
                return LLMResponse(
                    content=content,
                    model_used='qwen-turbo',
                    tokens_used=result.get('usage', {}).get('total_tokens', 0)
                )
            else:
                error_text = await response.text()
                raise Exception(f"通义千问API错误 {response.status}: {error_text}")

class DeepseekClient(BaseLLMClient):
    """Deepseek客户端"""
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            test_request = LLMRequest(
                prompt="Hello",
                max_tokens=10
            )
            response = await self.generate_text(test_request)
            return len(response.content) > 0
        except:
            return False
    
    async def generate_text(self, request: LLMRequest) -> LLMResponse:
        """生成文本"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        messages = []
        if request.system_prompt:
            messages.append({
                'role': 'system',
                'content': request.system_prompt
            })
        
        messages.append({
            'role': 'user',
            'content': request.prompt
        })
        
        data = {
            'model': 'deepseek-chat',
            'messages': messages,
            'max_tokens': request.max_tokens,
            'temperature': request.temperature
        }
        
        async with self.session.post(self.base_url, headers=headers, json=data) as response:
            if response.status == 200:
                result = await response.json()
                content = result['choices'][0]['message']['content']
                
                return LLMResponse(
                    content=content,
                    model_used='deepseek-chat',
                    tokens_used=result.get('usage', {}).get('total_tokens', 0)
                )
            else:
                error_text = await response.text()
                raise Exception(f"Deepseek API错误 {response.status}: {error_text}")

class GeminiClient(BaseLLMClient):
    """Google Gemini客户端"""
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            test_request = LLMRequest(
                prompt="Hello",
                max_tokens=10
            )
            response = await self.generate_text(test_request)
            return len(response.content) > 0
        except:
            return False
    
    async def generate_text(self, request: LLMRequest) -> LLMResponse:
        """生成文本"""
        # Gemini API使用不同的格式
        url = f"{self.base_url}?key={self.api_key}"
        
        # 构建请求内容
        content_parts = []
        if request.system_prompt:
            content_parts.append({'text': f"System: {request.system_prompt}\n\nUser: {request.prompt}"})
        else:
            content_parts.append({'text': request.prompt})
        
        data = {
            'contents': [{
                'parts': content_parts
            }],
            'generationConfig': {
                'maxOutputTokens': request.max_tokens,
                'temperature': request.temperature
            }
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        async with self.session.post(url, headers=headers, json=data) as response:
            if response.status == 200:
                result = await response.json()
                content = result['candidates'][0]['content']['parts'][0]['text']
                
                return LLMResponse(
                    content=content,
                    model_used='gemini-1.5-flash',
                    tokens_used=result.get('usageMetadata', {}).get('totalTokenCount', 0)
                )
            else:
                error_text = await response.text()
                raise Exception(f"Gemini API错误 {response.status}: {error_text}")

class LLMServiceManager:
    """LLM服务管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.clients = {}
        self._initialize_clients()
    
    def _initialize_clients(self):
        """初始化所有LLM客户端"""
        models = self.config.get('models', [])
        
        for model_config in models:
            model_type = model_config.get('type', '')
            model_name = model_config.get('name', '')
            
            if model_type == 'zhipu':
                self.clients[model_name] = ZhipuAIClient(model_name, model_config)
            elif model_type == 'tongyi':
                self.clients[model_name] = TongyiClient(model_name, model_config)
            elif model_type == 'deepseek':
                self.clients[model_name] = DeepseekClient(model_name, model_config)
            elif model_type == 'google':
                self.clients[model_name] = GeminiClient(model_name, model_config)
    
    def get_client(self, name: str) -> Optional[BaseLLMClient]:
        """获取LLM客户端"""
        return self.clients.get(name)
    
    def get_all_clients(self) -> List[BaseLLMClient]:
        """获取所有LLM客户端"""
        return list(self.clients.values())
    
    async def initialize_all(self):
        """初始化所有客户端"""
        for client in self.clients.values():
            await client.initialize()
    
    async def cleanup_all(self):
        """清理所有客户端"""
        for client in self.clients.values():
            await client.cleanup()

# 便捷函数
def create_llm_service_manager(config: Dict[str, Any]) -> LLMServiceManager:
    """创建LLM服务管理器"""
    return LLMServiceManager(config)

if __name__ == "__main__":
    # 测试代码
    async def test_llm_service():
        # 模拟配置
        test_config = {
            'models': [
                {
                    'name': '智谱AI',
                    'type': 'zhipu',
                    'key': 'test_key',
                    'url': 'https://open.bigmodel.cn/api/paas/v4/chat/completions'
                }
            ]
        }
        
        manager = create_llm_service_manager(test_config)
        await manager.initialize_all()
        
        # 测试文本生成
        client = manager.get_client('智谱AI')
        if client:
            request = LLMRequest(prompt="你好，请介绍一下自己")
            response = await client.generate_text(request)
            print(f"响应: {response.content}")
        
        await manager.cleanup_all()
    
    # asyncio.run(test_llm_service())
    print("LLM服务模块已加载")