# AI视频生成器 2.0 项目开发规则

## 1. 总体原则

### 1.1 核心价值观
- **质量第一**: 代码质量优于开发速度
- **用户体验**: 所有决策以用户体验为中心
- **可维护性**: 编写易于理解和维护的代码
- **性能优化**: 持续关注应用程序性能
- **安全意识**: 始终考虑安全性和隐私保护

### 1.2 开发哲学
- **简单胜过复杂**: 优先选择简单的解决方案
- **显式胜过隐式**: 代码意图应该明确表达
- **一致性**: 保持代码风格和架构的一致性
- **渐进式改进**: 支持持续重构和优化
- **测试驱动**: 所有功能必须有对应的测试

## 2. 代码规范

### 2.1 Python代码规范

#### 2.1.1 基础规范
```python
# 严格遵循PEP 8，使用以下工具确保代码质量
# - black: 代码格式化
# - isort: 导入排序
# - flake8: 代码检查
# - mypy: 类型检查
# - pylint: 代码质量分析

# 示例：正确的代码风格
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod
import asyncio
import logging


@dataclass
class ProjectConfig:
    """项目配置数据类
    
    Attributes:
        name: 项目名称
        description: 项目描述
        settings: 项目设置字典
    """
    name: str
    description: Optional[str] = None
    settings: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.settings is None:
            self.settings = {}


class ServiceBase(ABC):
    """服务基类
    
    所有服务类必须继承此基类并实现抽象方法。
    """
    
    def __init__(self, config: Dict[str, Any]) -> None:
        self._config = config
        self._logger = logging.getLogger(self.__class__.__name__)
        self._initialized = False
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化服务
        
        Returns:
            bool: 初始化是否成功
            
        Raises:
            ServiceInitializationError: 初始化失败时抛出
        """
        pass
    
    @abstractmethod
    async def shutdown(self) -> None:
        """关闭服务"""
        pass
    
    @property
    def is_initialized(self) -> bool:
        """检查服务是否已初始化"""
        return self._initialized
```

#### 2.1.2 命名规范
```python
# 类名：使用PascalCase
class ImageGenerationService:
    pass

class VideoProcessorFactory:
    pass

# 函数和变量名：使用snake_case
def generate_storyboard(text: str, style: str) -> Storyboard:
    pass

def process_image_batch(images: List[Image]) -> List[ProcessedImage]:
    pass

# 常量：使用UPPER_SNAKE_CASE
MAX_RETRY_ATTEMPTS = 3
DEFAULT_TIMEOUT_SECONDS = 30
SUPPORTED_IMAGE_FORMATS = ['.jpg', '.png', '.webp']

# 私有成员：使用单下划线前缀
class MyClass:
    def __init__(self):
        self._private_var = None
        self.__very_private_var = None  # 仅在需要名称修饰时使用
    
    def _private_method(self) -> None:
        pass

# 类型变量：使用简短的PascalCase
T = TypeVar('T')
ModelT = TypeVar('ModelT', bound='BaseModel')
```

#### 2.1.3 文档字符串规范
```python
def generate_video_from_storyboard(
    storyboard: Storyboard,
    output_path: str,
    quality: VideoQuality = VideoQuality.HIGH,
    progress_callback: Optional[Callable[[float], None]] = None
) -> VideoGenerationResult:
    """从分镜生成视频
    
    根据提供的分镜数据生成完整的视频文件，支持多种质量设置
    和进度回调功能。
    
    Args:
        storyboard: 包含所有镜头信息的分镜对象
        output_path: 输出视频文件的完整路径
        quality: 视频质量设置，默认为高质量
        progress_callback: 可选的进度回调函数，接收0-1之间的进度值
    
    Returns:
        VideoGenerationResult: 包含生成结果和元数据的对象
        
    Raises:
        InvalidStoryboardError: 当分镜数据无效时
        FilePermissionError: 当无法写入输出路径时
        VideoGenerationError: 当视频生成过程中出现错误时
        
    Example:
        >>> storyboard = create_sample_storyboard()
        >>> result = generate_video_from_storyboard(
        ...     storyboard=storyboard,
        ...     output_path="/path/to/output.mp4",
        ...     quality=VideoQuality.MEDIUM
        ... )
        >>> print(f"Video generated: {result.file_path}")
        
    Note:
        此函数是异步的，建议在异步上下文中调用。
        生成过程可能需要较长时间，建议使用进度回调监控进度。
    """
    pass
```

### 2.2 类型注解规范

#### 2.2.1 强制类型注解
```python
# 所有公共函数和方法必须有完整的类型注解
from typing import Union, Optional, List, Dict, Any, Callable, TypeVar, Generic

# 函数参数和返回值
def process_text(text: str, max_length: int = 1000) -> ProcessedText:
    pass

# 异步函数
async def fetch_data(url: str, timeout: float = 30.0) -> Dict[str, Any]:
    pass

# 泛型类型
T = TypeVar('T')

class Repository(Generic[T]):
    def get_by_id(self, item_id: str) -> Optional[T]:
        pass
    
    def list_all(self) -> List[T]:
        pass

# 复杂类型
ConfigDict = Dict[str, Union[str, int, bool, List[str]]]
EventHandler = Callable[[str, Dict[str, Any]], None]

def setup_event_system(
    handlers: Dict[str, EventHandler],
    config: ConfigDict
) -> None:
    pass
```

#### 2.2.2 自定义类型定义
```python
# 使用TypedDict定义结构化字典
from typing_extensions import TypedDict, Literal

class ProjectMetadata(TypedDict):
    name: str
    version: str
    created_at: str
    tags: List[str]
    settings: Dict[str, Any]

# 使用Literal定义枚举值
VideoFormat = Literal['mp4', 'avi', 'mov', 'webm']
ImageFormat = Literal['jpg', 'png', 'webp', 'gif']

# 使用Union定义联合类型
MediaFile = Union[VideoFile, ImageFile, AudioFile]
GenerationResult = Union[SuccessResult, ErrorResult]
```

### 2.3 错误处理规范

#### 2.3.1 异常层次结构
```python
# 定义清晰的异常层次结构
class AIVideoGeneratorError(Exception):
    """应用程序基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.timestamp = datetime.utcnow()

# 业务逻辑异常
class BusinessLogicError(AIVideoGeneratorError):
    """业务逻辑异常"""
    pass

class InvalidStoryboardError(BusinessLogicError):
    """无效分镜异常"""
    pass

class GenerationLimitExceededError(BusinessLogicError):
    """生成限制超出异常"""
    pass

# 服务异常
class ServiceError(AIVideoGeneratorError):
    """服务异常"""
    pass

class AIServiceUnavailableError(ServiceError):
    """AI服务不可用异常"""
    pass

class RateLimitExceededError(ServiceError):
    """速率限制超出异常"""
    pass

# 数据访问异常
class DataAccessError(AIVideoGeneratorError):
    """数据访问异常"""
    pass

class ProjectNotFoundError(DataAccessError):
    """项目未找到异常"""
    pass
```

#### 2.3.2 错误处理模式
```python
# 使用Result模式处理可能失败的操作
from typing import Union, Generic, TypeVar
from dataclasses import dataclass

T = TypeVar('T')
E = TypeVar('E', bound=Exception)

@dataclass
class Success(Generic[T]):
    value: T
    
    def is_success(self) -> bool:
        return True
    
    def is_error(self) -> bool:
        return False

@dataclass
class Error(Generic[E]):
    error: E
    
    def is_success(self) -> bool:
        return False
    
    def is_error(self) -> bool:
        return True

Result = Union[Success[T], Error[E]]

# 使用示例
async def generate_image(prompt: str) -> Result[Image, ImageGenerationError]:
    try:
        image = await image_service.generate(prompt)
        return Success(image)
    except Exception as e:
        return Error(ImageGenerationError(f"Failed to generate image: {e}"))

# 调用示例
result = await generate_image("a beautiful sunset")
if result.is_success():
    image = result.value
    # 处理成功情况
else:
    error = result.error
    # 处理错误情况
```

## 3. 架构规范

### 3.1 分层架构规则

#### 3.1.1 依赖方向规则
```
严格的依赖方向（只能向下依赖）：

UI Layer (用户界面层)
    ↓
Controller Layer (控制器层)
    ↓
Business Logic Layer (业务逻辑层)
    ↓
Service Layer (服务层)
    ↓
Data Access Layer (数据访问层)
    ↓
Infrastructure Layer (基础设施层)

禁止事项：
- 下层不能依赖上层
- 跨层依赖（除非通过依赖注入）
- 循环依赖
```

#### 3.1.2 模块组织规则
```python
# 项目结构必须遵循以下规范
src/
├── ui/                          # UI层
│   ├── __init__.py
│   ├── main_window.py
│   ├── views/                   # 视图组件
│   ├── components/              # 可复用组件
│   └── themes/                  # 主题系统
├── controllers/                 # 控制器层
│   ├── __init__.py
│   ├── app_controller.py
│   ├── project_controller.py
│   └── media_controller.py
├── business/                    # 业务逻辑层
│   ├── __init__.py
│   ├── storyboard/             # 分镜业务逻辑
│   ├── media/                  # 媒体处理业务逻辑
│   └── consistency/            # 一致性管理
├── services/                    # 服务层
│   ├── __init__.py
│   ├── ai/                     # AI服务
│   ├── media/                  # 媒体服务
│   └── external/               # 外部服务
├── data/                       # 数据访问层
│   ├── __init__.py
│   ├── repositories/           # 仓库模式
│   ├── models/                 # 数据模型
│   └── migrations/             # 数据库迁移
├── infrastructure/             # 基础设施层
│   ├── __init__.py
│   ├── database/               # 数据库
│   ├── cache/                  # 缓存
│   ├── logging/                # 日志
│   └── config/                 # 配置
└── shared/                     # 共享组件
    ├── __init__.py
    ├── types/                  # 类型定义
    ├── utils/                  # 工具函数
    ├── constants/              # 常量
    └── exceptions/             # 异常定义
```

### 3.2 依赖注入规范

#### 3.2.1 容器配置
```python
# 使用dependency-injector进行依赖管理
from dependency_injector import containers, providers
from dependency_injector.wiring import Provide, inject

class ApplicationContainer(containers.DeclarativeContainer):
    """应用程序依赖容器"""
    
    # 配置提供者
    config = providers.Configuration()
    
    # 基础设施层
    database = providers.Singleton(
        Database,
        connection_string=config.database.connection_string
    )
    
    cache = providers.Singleton(
        CacheManager,
        redis_url=config.cache.redis_url
    )
    
    # 数据访问层
    project_repository = providers.Factory(
        ProjectRepository,
        database=database
    )
    
    # 服务层
    llm_service = providers.Factory(
        LLMService,
        config=config.llm
    )
    
    image_service = providers.Factory(
        ImageService,
        config=config.image
    )
    
    # 业务逻辑层
    storyboard_generator = providers.Factory(
        StoryboardGenerator,
        llm_service=llm_service,
        project_repository=project_repository
    )
    
    # 控制器层
    project_controller = providers.Factory(
        ProjectController,
        storyboard_generator=storyboard_generator,
        project_repository=project_repository
    )

# 使用依赖注入
class ProjectController:
    @inject
    def __init__(
        self,
        storyboard_generator: StoryboardGenerator = Provide[ApplicationContainer.storyboard_generator],
        project_repository: ProjectRepository = Provide[ApplicationContainer.project_repository]
    ):
        self.storyboard_generator = storyboard_generator
        self.project_repository = project_repository
```

### 3.3 接口设计规范

#### 3.3.1 抽象接口定义
```python
# 所有服务必须定义清晰的接口
from abc import ABC, abstractmethod
from typing import Protocol

class ImageGenerationService(Protocol):
    """图像生成服务协议"""
    
    async def generate_image(
        self, 
        prompt: str, 
        style: str = "default",
        size: Tuple[int, int] = (1024, 1024)
    ) -> ImageResult:
        """生成图像"""
        ...
    
    async def batch_generate(
        self, 
        prompts: List[str], 
        style: str = "default"
    ) -> List[ImageResult]:
        """批量生成图像"""
        ...
    
    def get_supported_styles(self) -> List[str]:
        """获取支持的风格列表"""
        ...

# 具体实现必须遵循接口契约
class PollutionsImageService:
    """Pollutions AI图像生成服务实现"""
    
    async def generate_image(
        self, 
        prompt: str, 
        style: str = "default",
        size: Tuple[int, int] = (1024, 1024)
    ) -> ImageResult:
        # 实现细节
        pass
```

## 4. 测试规范

### 4.1 测试策略

#### 4.1.1 测试金字塔
```
        /\        E2E Tests (端到端测试)
       /  \       - 用户场景测试
      /____\      - UI自动化测试
     /      \     
    /        \    Integration Tests (集成测试)
   /          \   - API测试
  /____________\  - 服务集成测试
 /              \ 
/________________\ Unit Tests (单元测试)
                   - 函数测试
                   - 类测试
                   - 模块测试

测试比例要求：
- 单元测试：70%
- 集成测试：20%
- 端到端测试：10%
```

#### 4.1.2 测试覆盖率要求
```python
# 测试覆盖率要求
# - 整体代码覆盖率：≥ 90%
# - 业务逻辑层覆盖率：≥ 95%
# - 服务层覆盖率：≥ 90%
# - 数据访问层覆盖率：≥ 85%
# - UI层覆盖率：≥ 70%

# 使用pytest和coverage进行测试
# pytest.ini配置
[tool:pytest]
minversion = 6.0
addopts = 
    --strict-markers
    --strict-config
    --cov=src
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=90
    --asyncio-mode=auto
testpaths = tests
markers =
    unit: 单元测试
    integration: 集成测试
    e2e: 端到端测试
    slow: 慢速测试
    external: 需要外部服务的测试
```

#### 4.1.3 测试组织结构
```python
# 测试目录结构
tests/
├── unit/                       # 单元测试
│   ├── test_business/
│   ├── test_services/
│   ├── test_data/
│   └── test_shared/
├── integration/                # 集成测试
│   ├── test_api/
│   ├── test_database/
│   └── test_external_services/
├── e2e/                        # 端到端测试
│   ├── test_user_workflows/
│   └── test_ui_scenarios/
├── fixtures/                   # 测试夹具
│   ├── sample_data/
│   └── mock_responses/
└── conftest.py                 # pytest配置

# 测试文件命名规范
# - 单元测试：test_<module_name>.py
# - 集成测试：test_<integration_scenario>.py
# - 端到端测试：test_<user_scenario>.py
```

#### 4.1.4 测试编写规范
```python
# 单元测试示例
import pytest
from unittest.mock import Mock, AsyncMock, patch
from src.business.storyboard.generator import StoryboardGenerator
from src.shared.exceptions import InvalidInputError

class TestStoryboardGenerator:
    """分镜生成器测试类"""
    
    @pytest.fixture
    def mock_llm_service(self):
        """LLM服务模拟对象"""
        service = AsyncMock()
        service.generate_storyboard.return_value = {
            "shots": [
                {"description": "Test shot 1", "duration": 3.0},
                {"description": "Test shot 2", "duration": 2.5}
            ]
        }
        return service
    
    @pytest.fixture
    def storyboard_generator(self, mock_llm_service):
        """分镜生成器实例"""
        return StoryboardGenerator(llm_service=mock_llm_service)
    
    @pytest.mark.asyncio
    async def test_generate_from_text_success(self, storyboard_generator, mock_llm_service):
        """测试成功生成分镜"""
        # Arrange
        input_text = "A story about a brave knight"
        style = "cinematic"
        
        # Act
        result = await storyboard_generator.generate_from_text(input_text, style)
        
        # Assert
        assert result is not None
        assert len(result.shots) == 2
        assert result.shots[0].description == "Test shot 1"
        mock_llm_service.generate_storyboard.assert_called_once_with(input_text, style)
    
    @pytest.mark.asyncio
    async def test_generate_from_text_empty_input(self, storyboard_generator):
        """测试空输入处理"""
        # Arrange
        input_text = ""
        style = "cinematic"
        
        # Act & Assert
        with pytest.raises(InvalidInputError, match="Input text cannot be empty"):
            await storyboard_generator.generate_from_text(input_text, style)
    
    @pytest.mark.parametrize("text,style,expected_shots", [
        ("Short story", "minimal", 1),
        ("Medium length story with multiple scenes", "standard", 3),
        ("Very long story with many detailed scenes and characters", "detailed", 5),
    ])
    @pytest.mark.asyncio
    async def test_generate_different_lengths(self, storyboard_generator, text, style, expected_shots):
        """测试不同长度文本的处理"""
        # 参数化测试示例
        pass
```

### 4.2 Mock和测试替身规范

#### 4.2.1 外部服务Mock
```python
# 外部AI服务Mock
class MockAIService:
    """AI服务Mock类"""
    
    def __init__(self, response_delay: float = 0.1):
        self.response_delay = response_delay
        self.call_count = 0
        self.last_request = None
    
    async def generate(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """模拟AI生成"""
        await asyncio.sleep(self.response_delay)
        self.call_count += 1
        self.last_request = {"prompt": prompt, **kwargs}
        
        return {
            "result": f"Generated content for: {prompt[:50]}...",
            "metadata": {
                "model": "mock-model",
                "tokens_used": len(prompt) * 2
            }
        }
    
    def reset(self):
        """重置Mock状态"""
        self.call_count = 0
        self.last_request = None

# 使用pytest-mock进行更复杂的Mock
@pytest.fixture
def mock_external_api(mocker):
    """Mock外部API调用"""
    mock_response = {
        "status": "success",
        "data": {"generated_image_url": "https://example.com/image.jpg"}
    }
    
    return mocker.patch(
        'src.services.external.image_api.ImageAPI.generate',
        return_value=mock_response
    )
```

## 5. 性能规范

### 5.1 性能指标要求

#### 5.1.1 响应时间要求
```python
# 性能指标定义
PERFORMANCE_REQUIREMENTS = {
    "ui_response": {
        "target": 100,  # 毫秒
        "max": 200,     # 毫秒
        "description": "UI操作响应时间"
    },
    "text_processing": {
        "target": 2000,  # 毫秒
        "max": 5000,     # 毫秒
        "description": "文本处理和分镜生成"
    },
    "image_generation": {
        "target": 10000,  # 毫秒
        "max": 30000,    # 毫秒
        "description": "单张图像生成"
    },
    "video_composition": {
        "target": 30000,  # 毫秒
        "max": 120000,   # 毫秒
        "description": "视频合成（每分钟）"
    },
    "memory_usage": {
        "target": 512,   # MB
        "max": 1024,     # MB
        "description": "内存使用量"
    }
}

# 性能测试装饰器
import time
import functools
from typing import Callable, Any

def performance_test(max_duration_ms: int):
    """性能测试装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            start_time = time.perf_counter()
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                end_time = time.perf_counter()
                duration_ms = (end_time - start_time) * 1000
                
                if duration_ms > max_duration_ms:
                    pytest.fail(
                        f"Performance test failed: {func.__name__} took {duration_ms:.2f}ms, "
                        f"expected < {max_duration_ms}ms"
                    )
                    
                print(f"Performance: {func.__name__} completed in {duration_ms:.2f}ms")
        
        return wrapper
    return decorator

# 使用示例
@performance_test(max_duration_ms=5000)
async def test_storyboard_generation_performance():
    """测试分镜生成性能"""
    generator = StoryboardGenerator()
    result = await generator.generate_from_text("Test story", "cinematic")
    assert result is not None
```

#### 5.1.2 内存管理规范
```python
# 内存管理最佳实践
import gc
import weakref
from typing import Dict, Any, Optional

class ResourceManager:
    """资源管理器
    
    负责管理大型对象的生命周期，防止内存泄漏
    """
    
    def __init__(self):
        self._resources: Dict[str, Any] = {}
        self._weak_refs: Dict[str, weakref.ref] = {}
    
    def register_resource(self, name: str, resource: Any) -> None:
        """注册资源"""
        self._resources[name] = resource
        
        # 为大型对象创建弱引用
        if hasattr(resource, '__sizeof__') and resource.__sizeof__() > 1024 * 1024:  # 1MB
            self._weak_refs[name] = weakref.ref(resource, self._cleanup_callback)
    
    def get_resource(self, name: str) -> Optional[Any]:
        """获取资源"""
        return self._resources.get(name)
    
    def release_resource(self, name: str) -> None:
        """释放资源"""
        if name in self._resources:
            resource = self._resources.pop(name)
            
            # 显式清理
            if hasattr(resource, 'cleanup'):
                resource.cleanup()
            
            # 移除弱引用
            if name in self._weak_refs:
                del self._weak_refs[name]
            
            # 强制垃圾回收
            del resource
            gc.collect()
    
    def _cleanup_callback(self, weak_ref: weakref.ref) -> None:
        """弱引用清理回调"""
        # 清理相关资源
        pass
    
    def get_memory_usage(self) -> Dict[str, int]:
        """获取内存使用情况"""
        usage = {}
        for name, resource in self._resources.items():
            if hasattr(resource, '__sizeof__'):
                usage[name] = resource.__sizeof__()
        return usage

# 上下文管理器用于自动资源清理
from contextlib import asynccontextmanager

@asynccontextmanager
async def managed_ai_service(service_config: Dict[str, Any]):
    """AI服务上下文管理器"""
    service = None
    try:
        service = await create_ai_service(service_config)
        await service.initialize()
        yield service
    finally:
        if service:
            await service.shutdown()
            del service
            gc.collect()
```

### 5.2 缓存策略规范

#### 5.2.1 缓存层次和策略
```python
# 缓存策略配置
CACHE_STRATEGIES = {
    "image_generation": {
        "ttl": 3600 * 24 * 7,  # 7天
        "max_size": 1000,      # 最大条目数
        "eviction": "lru",     # LRU淘汰策略
        "compression": True,    # 启用压缩
    },
    "text_processing": {
        "ttl": 3600 * 24,      # 1天
        "max_size": 500,
        "eviction": "lfu",     # LFU淘汰策略
        "compression": False,
    },
    "user_preferences": {
        "ttl": 3600 * 24 * 30, # 30天
        "max_size": 10000,
        "eviction": "ttl",     # TTL淘汰策略
        "compression": False,
    }
}

# 缓存键命名规范
def generate_cache_key(operation: str, **params) -> str:
    """生成标准化的缓存键
    
    格式: {operation}:{param1}={value1}:{param2}={value2}:hash={hash}
    """
    import hashlib
    
    # 排序参数确保一致性
    sorted_params = sorted(params.items())
    param_str = ":".join(f"{k}={v}" for k, v in sorted_params)
    
    # 生成参数哈希
    param_hash = hashlib.md5(param_str.encode()).hexdigest()[:8]
    
    return f"{operation}:{param_str}:hash={param_hash}"

# 使用示例
cache_key = generate_cache_key(
    "image_generation",
    prompt="a beautiful sunset",
    style="photorealistic",
    size="1024x1024"
)
# 结果: "image_generation:prompt=a beautiful sunset:size=1024x1024:style=photorealistic:hash=a1b2c3d4"
```

## 6. 安全规范

### 6.1 数据安全

#### 6.1.1 敏感数据处理
```python
# 敏感数据加密
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

class SecureDataManager:
    """安全数据管理器"""
    
    def __init__(self, master_key: bytes = None):
        if master_key is None:
            master_key = self._generate_key()
        self.cipher = Fernet(master_key)
    
    def _generate_key(self) -> bytes:
        """生成加密密钥"""
        password = os.environ.get('APP_MASTER_PASSWORD', 'default-password').encode()
        salt = os.environ.get('APP_SALT', 'default-salt').encode()
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return key
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        encrypted_data = self.cipher.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted_data = self.cipher.decrypt(encrypted_bytes)
        return decrypted_data.decode()

# API密钥管理
class APIKeyManager:
    """API密钥管理器"""
    
    def __init__(self, secure_manager: SecureDataManager):
        self.secure_manager = secure_manager
        self._keys: Dict[str, str] = {}
    
    def store_api_key(self, service_name: str, api_key: str) -> None:
        """存储API密钥"""
        encrypted_key = self.secure_manager.encrypt_sensitive_data(api_key)
        self._keys[service_name] = encrypted_key
        
        # 清除原始密钥
        api_key = "*" * len(api_key)
        del api_key
    
    def get_api_key(self, service_name: str) -> Optional[str]:
        """获取API密钥"""
        encrypted_key = self._keys.get(service_name)
        if encrypted_key:
            return self.secure_manager.decrypt_sensitive_data(encrypted_key)
        return None
    
    def mask_api_key(self, api_key: str) -> str:
        """掩码API密钥用于日志"""
        if len(api_key) <= 8:
            return "*" * len(api_key)
        return api_key[:4] + "*" * (len(api_key) - 8) + api_key[-4:]
```

#### 6.1.2 输入验证和清理
```python
# 输入验证器
from typing import Any, Dict, List, Union
import re
import html

class InputValidator:
    """输入验证器"""
    
    # 安全的字符模式
    SAFE_TEXT_PATTERN = re.compile(r'^[\w\s\-.,!?;:()\[\]{}"\'\/\\]+$')
    EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    URL_PATTERN = re.compile(r'^https?:\/\/[\w\-]+(\.[\w\-]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?$')
    
    @classmethod
    def validate_text_input(cls, text: str, max_length: int = 10000) -> str:
        """验证文本输入"""
        if not isinstance(text, str):
            raise ValueError("Input must be a string")
        
        if len(text) > max_length:
            raise ValueError(f"Text too long: {len(text)} > {max_length}")
        
        # HTML转义
        escaped_text = html.escape(text)
        
        # 检查恶意模式
        if not cls.SAFE_TEXT_PATTERN.match(escaped_text):
            raise ValueError("Text contains unsafe characters")
        
        return escaped_text
    
    @classmethod
    def validate_file_path(cls, file_path: str) -> str:
        """验证文件路径"""
        import os.path
        
        # 防止路径遍历攻击
        if ".." in file_path or file_path.startswith("/"):
            raise ValueError("Invalid file path")
        
        # 规范化路径
        normalized_path = os.path.normpath(file_path)
        
        return normalized_path
    
    @classmethod
    def sanitize_filename(cls, filename: str) -> str:
        """清理文件名"""
        # 移除危险字符
        safe_chars = re.sub(r'[^\w\-_\.]', '_', filename)
        
        # 限制长度
        if len(safe_chars) > 255:
            name, ext = os.path.splitext(safe_chars)
            safe_chars = name[:255-len(ext)] + ext
        
        return safe_chars
```

### 6.2 网络安全

#### 6.2.1 HTTP客户端安全配置
```python
# 安全的HTTP客户端
import aiohttp
import ssl
from typing import Optional, Dict, Any

class SecureHTTPClient:
    """安全的HTTP客户端"""
    
    def __init__(self, timeout: int = 30, max_retries: int = 3):
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        self.max_retries = max_retries
        
        # SSL配置
        self.ssl_context = ssl.create_default_context()
        self.ssl_context.check_hostname = True
        self.ssl_context.verify_mode = ssl.CERT_REQUIRED
        
        # 连接器配置
        self.connector = aiohttp.TCPConnector(
            ssl=self.ssl_context,
            limit=100,
            limit_per_host=10,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
    
    async def request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        data: Optional[Any] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """安全的HTTP请求"""
        
        # 验证URL
        if not url.startswith(('https://', 'http://localhost', 'http://127.0.0.1')):
            raise ValueError("Only HTTPS URLs are allowed (except localhost)")
        
        # 设置安全头
        safe_headers = {
            'User-Agent': 'AI-Video-Generator/2.0',
            'Accept': 'application/json',
            'Connection': 'close',
        }
        if headers:
            safe_headers.update(headers)
        
        # 移除敏感头
        sensitive_headers = ['authorization', 'cookie', 'x-api-key']
        for header in sensitive_headers:
            if header in safe_headers:
                # 在日志中掩码敏感信息
                masked_value = self._mask_header_value(safe_headers[header])
                logger.debug(f"Request header {header}: {masked_value}")
        
        async with aiohttp.ClientSession(
            connector=self.connector,
            timeout=self.timeout
        ) as session:
            for attempt in range(self.max_retries + 1):
                try:
                    async with session.request(
                        method=method,
                        url=url,
                        headers=safe_headers,
                        data=data,
                        **kwargs
                    ) as response:
                        response.raise_for_status()
                        return await response.json()
                        
                except aiohttp.ClientError as e:
                    if attempt == self.max_retries:
                        raise
                    await asyncio.sleep(2 ** attempt)  # 指数退避
    
    def _mask_header_value(self, value: str) -> str:
        """掩码头部值"""
        if len(value) <= 8:
            return "*" * len(value)
        return value[:4] + "*" * (len(value) - 8) + value[-4:]
```

## 7. 日志和监控规范

### 7.1 日志规范

#### 7.1.1 结构化日志
```python
# 结构化日志配置
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional

class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # 添加额外字段
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        
        if hasattr(record, 'duration_ms'):
            log_entry['duration_ms'] = record.duration_ms
        
        # 异常信息
        if record.exc_info:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': self.formatException(record.exc_info)
            }
        
        return json.dumps(log_entry, ensure_ascii=False)

# 日志级别使用规范
LOG_LEVEL_USAGE = {
    'DEBUG': '详细的调试信息，仅在开发环境使用',
    'INFO': '一般信息，记录正常的业务流程',
    'WARNING': '警告信息，可能的问题但不影响功能',
    'ERROR': '错误信息，功能异常但应用可继续运行',
    'CRITICAL': '严重错误，应用可能无法继续运行'
}

# 日志记录最佳实践
class ApplicationLogger:
    """应用程序日志记录器"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def log_user_action(self, action: str, user_id: str, details: Dict[str, Any] = None):
        """记录用户操作"""
        self.logger.info(
            f"User action: {action}",
            extra={
                'user_id': user_id,
                'action': action,
                'details': details or {}
            }
        )
    
    def log_performance(self, operation: str, duration_ms: float, details: Dict[str, Any] = None):
        """记录性能信息"""
        level = logging.WARNING if duration_ms > 5000 else logging.INFO
        self.logger.log(
            level,
            f"Performance: {operation} completed in {duration_ms:.2f}ms",
            extra={
                'operation': operation,
                'duration_ms': duration_ms,
                'details': details or {}
            }
        )
    
    def log_api_call(self, service: str, endpoint: str, status_code: int, duration_ms: float):
        """记录API调用"""
        level = logging.ERROR if status_code >= 400 else logging.INFO
        self.logger.log(
            level,
            f"API call: {service}/{endpoint} returned {status_code}",
            extra={
                'service': service,
                'endpoint': endpoint,
                'status_code': status_code,
                'duration_ms': duration_ms
            }
        )
    
    def log_security_event(self, event_type: str, details: Dict[str, Any]):
        """记录安全事件"""
        self.logger.warning(
            f"Security event: {event_type}",
            extra={
                'security_event': event_type,
                'details': details
            }
        )
```

### 7.2 监控指标

#### 7.2.1 应用程序指标
```python
# 指标收集器
from collections import defaultdict, deque
from threading import Lock
import time

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, max_samples: int = 1000):
        self.max_samples = max_samples
        self._lock = Lock()
        
        # 计数器
        self.counters: Dict[str, int] = defaultdict(int)
        
        # 仪表
        self.gauges: Dict[str, float] = {}
        
        # 直方图
        self.histograms: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_samples))
        
        # 时间序列
        self.timeseries: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_samples))
    
    def increment_counter(self, name: str, value: int = 1, tags: Dict[str, str] = None):
        """增加计数器"""
        with self._lock:
            key = self._make_key(name, tags)
            self.counters[key] += value
    
    def set_gauge(self, name: str, value: float, tags: Dict[str, str] = None):
        """设置仪表值"""
        with self._lock:
            key = self._make_key(name, tags)
            self.gauges[key] = value
    
    def record_histogram(self, name: str, value: float, tags: Dict[str, str] = None):
        """记录直方图值"""
        with self._lock:
            key = self._make_key(name, tags)
            self.histograms[key].append(value)
    
    def record_timeseries(self, name: str, value: float, tags: Dict[str, str] = None):
        """记录时间序列值"""
        with self._lock:
            key = self._make_key(name, tags)
            timestamp = time.time()
            self.timeseries[key].append((timestamp, value))
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            stats = {
                'counters': dict(self.counters),
                'gauges': dict(self.gauges),
                'histograms': {},
                'timeseries': {}
            }
            
            # 计算直方图统计
            for name, values in self.histograms.items():
                if values:
                    sorted_values = sorted(values)
                    stats['histograms'][name] = {
                        'count': len(values),
                        'min': min(values),
                        'max': max(values),
                        'mean': sum(values) / len(values),
                        'p50': self._percentile(sorted_values, 0.5),
                        'p95': self._percentile(sorted_values, 0.95),
                        'p99': self._percentile(sorted_values, 0.99)
                    }
            
            # 时间序列数据
            for name, values in self.timeseries.items():
                stats['timeseries'][name] = list(values)
            
            return stats
    
    def _make_key(self, name: str, tags: Dict[str, str] = None) -> str:
        """生成指标键"""
        if not tags:
            return name
        
        tag_str = ','.join(f'{k}={v}' for k, v in sorted(tags.items()))
        return f'{name}[{tag_str}]'
    
    def _percentile(self, sorted_values: List[float], p: float) -> float:
        """计算百分位数"""
        if not sorted_values:
            return 0.0
        
        index = int(len(sorted_values) * p)
        if index >= len(sorted_values):
            index = len(sorted_values) - 1
        
        return sorted_values[index]

# 性能监控装饰器
def monitor_performance(metric_name: str, tags: Dict[str, str] = None):
    """性能监控装饰器"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            
            try:
                result = await func(*args, **kwargs)
                metrics.increment_counter(f'{metric_name}.success', tags=tags)
                return result
            except Exception as e:
                metrics.increment_counter(f'{metric_name}.error', tags=tags)
                raise
            finally:
                duration = (time.perf_counter() - start_time) * 1000
                metrics.record_histogram(f'{metric_name}.duration', duration, tags=tags)
        
        return wrapper
    return decorator
```

## 8. 版本控制和发布规范

### 8.1 Git工作流规范

#### 8.1.1 分支策略
```
Git分支模型 (Git Flow):

main (主分支)
├── develop (开发分支)
│   ├── feature/user-authentication (功能分支)
│   ├── feature/video-generation (功能分支)
│   └── feature/ui-improvements (功能分支)
├── release/v2.0.0 (发布分支)
├── hotfix/critical-bug-fix (热修复分支)
└── support/v1.x (支持分支)

分支命名规范:
- feature/功能描述 (例: feature/add-video-export)
- bugfix/问题描述 (例: bugfix/fix-memory-leak)
- hotfix/紧急修复 (例: hotfix/security-patch)
- release/版本号 (例: release/v2.0.0)
- support/版本号 (例: support/v1.x)
```

#### 8.1.2 提交信息规范
```
提交信息格式 (Conventional Commits):

<type>[optional scope]: <description>

[optional body]

[optional footer(s)]

类型 (type):
- feat: 新功能
- fix: 错误修复
- docs: 文档更新
- style: 代码格式化
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动
- perf: 性能优化
- ci: CI/CD相关

示例:
feat(storyboard): add automatic scene detection

Implement AI-powered scene detection that automatically
identifies scene boundaries in user input text.

- Add scene detection algorithm
- Integrate with existing storyboard generator
- Add unit tests for scene detection

Closes #123
Breaking-change: Changes storyboard API structure
```

### 8.2 版本号规范

#### 8.2.1 语义化版本控制
```
版本号格式: MAJOR.MINOR.PATCH[-PRERELEASE][+BUILD]

- MAJOR: 不兼容的API修改
- MINOR: 向后兼容的功能性新增
- PATCH: 向后兼容的问题修正
- PRERELEASE: 预发布版本标识 (alpha, beta, rc)
- BUILD: 构建元数据

示例:
- 2.0.0: 主要版本发布
- 2.1.0: 新功能发布
- 2.1.1: 错误修复
- 2.2.0-alpha.1: Alpha预发布版本
- 2.2.0-beta.1: Beta预发布版本
- 2.2.0-rc.1: Release Candidate
- 2.2.0+20241201.1: 带构建信息的版本
```

### 8.3 代码审查规范

#### 8.3.1 Pull Request规范
```markdown
# Pull Request模板

## 变更描述
简要描述此PR的目的和变更内容。

## 变更类型
- [ ] 新功能 (feature)
- [ ] 错误修复 (bugfix)
- [ ] 性能优化 (performance)
- [ ] 代码重构 (refactor)
- [ ] 文档更新 (docs)
- [ ] 测试相关 (test)

## 测试
- [ ] 单元测试已通过
- [ ] 集成测试已通过
- [ ] 手动测试已完成
- [ ] 性能测试已完成 (如适用)

## 检查清单
- [ ] 代码遵循项目编码规范
- [ ] 已添加必要的测试
- [ ] 已更新相关文档
- [ ] 已考虑向后兼容性
- [ ] 已检查安全性影响
- [ ] 已验证性能影响

## 相关Issue
Closes #issue_number

## 截图 (如适用)
如果是UI相关变更，请提供截图。

## 额外说明
任何需要审查者特别注意的地方。
```

#### 8.3.2 代码审查检查点
```python
# 代码审查检查清单
CODE_REVIEW_CHECKLIST = {
    "功能性": [
        "代码是否实现了预期功能",
        "是否处理了边界情况",
        "错误处理是否完善",
        "是否有潜在的逻辑错误"
    ],
    "代码质量": [
        "代码是否易于理解",
        "命名是否清晰准确",
        "函数是否单一职责",
        "是否有重复代码",
        "注释是否充分"
    ],
    "性能": [
        "是否有性能瓶颈",
        "内存使用是否合理",
        "算法复杂度是否可接受",
        "是否有不必要的计算"
    ],
    "安全性": [
        "是否有安全漏洞",
        "输入验证是否充分",
        "敏感数据是否正确处理",
        "权限检查是否完善"
    ],
    "测试": [
        "测试覆盖率是否足够",
        "测试用例是否全面",
        "是否有集成测试",
        "性能测试是否必要"
    ],
    "文档": [
        "API文档是否更新",
        "代码注释是否准确",
        "README是否需要更新",
        "变更日志是否记录"
    ]
}
```

## 9. 部署和运维规范

### 9.1 构建规范

#### 9.1.1 自动化构建
```yaml
# GitHub Actions工作流示例
name: Build and Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11, 3.12]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Lint with flake8
      run: |
        flake8 src tests --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 src tests --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Type check with mypy
      run: mypy src
    
    - name: Test with pytest
      run: |
        pytest tests/ --cov=src --cov-report=xml --cov-report=html
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
```

#### 9.1.2 打包规范
```python
# setup.py配置
from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="ai-video-generator",
    version="2.0.0",
    author="AI Video Generator Team",
    author_email="<EMAIL>",
    description="AI-powered video generation tool",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/ai-video-generator/ai-video-generator",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
    ],
    python_requires=">=3.11",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0",
            "pytest-cov>=4.0",
            "pytest-asyncio>=0.21",
            "black>=23.0",
            "isort>=5.0",
            "flake8>=6.0",
            "mypy>=1.0",
            "pre-commit>=3.0",
        ],
        "docs": [
            "sphinx>=6.0",
            "sphinx-rtd-theme>=1.0",
            "myst-parser>=1.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "ai-video-generator=src.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "src": ["assets/*", "themes/*", "templates/*"],
    },
)
```

### 9.2 配置管理

#### 9.2.1 环境配置
```yaml
# config/environments/development.yaml
app:
  name: "AI Video Generator"
  version: "2.0.0"
  debug: true
  log_level: "DEBUG"

database:
  url: "sqlite:///data/dev.db"
  echo: true
  pool_size: 5

cache:
  type: "memory"
  max_size: 1000
  ttl: 3600

services:
  llm:
    provider: "openai"
    model: "gpt-4"
    max_tokens: 4000
    temperature: 0.7
  
  image:
    provider: "pollinations"
    default_size: "1024x1024"
    quality: "high"
  
  video:
    provider: "cogvideox"
    default_duration: 5
    fps: 24

# config/environments/production.yaml
app:
  name: "AI Video Generator"
  version: "2.0.0"
  debug: false
  log_level: "INFO"

database:
  url: "postgresql://user:pass@localhost/aivideo"
  echo: false
  pool_size: 20
  max_overflow: 30

cache:
  type: "redis"
  url: "redis://localhost:6379/0"
  max_connections: 50

services:
  llm:
    provider: "openai"
    model: "gpt-4"
    max_tokens: 4000
    temperature: 0.7
    rate_limit: 100  # requests per minute
```

## 10. 文档规范

### 10.1 文档结构

```
docs/
├── README.md                   # 项目概述
├── INSTALLATION.md             # 安装指南
├── USER_GUIDE.md              # 用户指南
├── API_REFERENCE.md           # API参考
├── DEVELOPER_GUIDE.md         # 开发者指南
├── ARCHITECTURE.md            # 架构文档
├── DEPLOYMENT.md              # 部署指南
├── TROUBLESHOOTING.md         # 故障排除
├── CHANGELOG.md               # 变更日志
├── CONTRIBUTING.md            # 贡献指南
└── api/                       # API文档
    ├── controllers/
    ├── services/
    └── models/
```

### 10.2 文档编写规范

#### 10.2.1 API文档规范
```python
# API文档示例
class StoryboardController:
    """分镜控制器
    
    负责处理分镜相关的所有操作，包括创建、编辑、删除和查询分镜。
    """
    
    async def create_storyboard(
        self,
        request: StoryboardCreateRequest
    ) -> StoryboardResponse:
        """创建新分镜
        
        根据用户提供的文本和风格设置创建新的分镜。
        
        Args:
            request: 分镜创建请求，包含以下字段：
                - text (str): 输入文本，最大长度10000字符
                - style (str): 风格设置，可选值：'cinematic', 'anime', 'realistic'
                - language (str, optional): 语言设置，默认为'zh-CN'
                - project_id (UUID): 所属项目ID
        
        Returns:
            StoryboardResponse: 分镜响应对象，包含：
                - id (UUID): 分镜唯一标识符
                - shots (List[Shot]): 镜头列表
                - metadata (Dict): 元数据信息
                - created_at (datetime): 创建时间
        
        Raises:
            ValidationError: 当输入参数无效时
            ServiceUnavailableError: 当AI服务不可用时
            RateLimitExceededError: 当超出速率限制时
        
        Example:
            >>> request = StoryboardCreateRequest(
            ...     text="一个关于勇敢骑士的故事",
            ...     style="cinematic",
            ...     project_id=UUID("123e4567-e89b-12d3-a456-************")
            ... )
            >>> response = await controller.create_storyboard(request)
            >>> print(f"Created storyboard with {len(response.shots)} shots")
        
        Note:
            - 此操作可能需要较长时间完成（通常5-30秒）
            - 建议使用异步调用并提供进度回调
            - 生成的分镜会自动保存到指定项目中
        """
        pass
```

## 11. 质量保证规范

### 11.1 代码质量工具配置

#### 11.1.1 Pre-commit配置
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: debug-statements
  
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3.11
  
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]
  
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        additional_dependencies: [flake8-docstrings]
  
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
```

#### 11.1.2 代码质量配置文件
```ini
# setup.cfg
[flake8]
max-line-length = 127
extend-ignore = E203, W503
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    build,
    dist,
    *.egg-info
max-complexity = 10
docstring-convention = google

[mypy]
python_version = 3.11
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_equality = True

[coverage:run]
source = src
omit = 
    */tests/*
    */venv/*
    */__pycache__/*
    */migrations/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:
    if TYPE_CHECKING:
show_missing = True
skip_covered = False
```

## 12. 项目管理规范

### 12.1 开发流程

#### 12.1.1 功能开发流程
```
1. 需求分析
   ├── 用户故事编写
   ├── 验收标准定义
   ├── 技术方案设计
   └── 工作量估算

2. 开发准备
   ├── 创建功能分支
   ├── 编写测试用例
   ├── 设计API接口
   └── 更新文档

3. 编码实现
   ├── 实现核心逻辑
   ├── 编写单元测试
   ├── 代码审查
   └── 集成测试

4. 质量保证
   ├── 功能测试
   ├── 性能测试
   ├── 安全测试
   └── 用户验收测试

5. 发布部署
   ├── 合并到主分支
   ├── 创建发布版本
   ├── 部署到生产环境
   └── 监控和反馈
```

### 12.2 问题管理

#### 12.2.1 Bug报告模板
```markdown
# Bug报告

## 问题描述
简要描述遇到的问题。

## 复现步骤
1. 打开应用程序
2. 点击...
3. 输入...
4. 观察到错误

## 预期行为
描述应该发生什么。

## 实际行为
描述实际发生了什么。

## 环境信息
- 操作系统: [例如 Windows 11]
- 应用版本: [例如 v2.0.0]
- Python版本: [例如 3.11.5]
- 其他相关信息:

## 错误日志
```
粘贴相关的错误日志
```

## 截图
如果适用，添加截图来帮助解释问题。

## 严重程度
- [ ] 严重 (应用崩溃/数据丢失)
- [ ] 高 (主要功能无法使用)
- [ ] 中 (功能受限但有替代方案)
- [ ] 低 (轻微问题/改进建议)
```

---

**版本**: 1.0  
**创建日期**: 2024年12月  
**最后更新**: 2024年12月  
**维护者**: AI视频生成器开发团队  

**重要提醒**: 本文档是项目开发的强制性规范，所有团队成员必须严格遵守。违反规范的代码将不被接受合并到主分支。