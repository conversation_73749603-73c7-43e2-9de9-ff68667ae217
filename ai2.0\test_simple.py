#!/usr/bin/env python3
"""简单测试程序

测试基本的PyQt6功能和导入。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试基本导入"""
    print("测试基本导入...")
    
    try:
        import PyQt6
        print("✓ PyQt6 导入成功")
    except ImportError as e:
        print(f"✗ PyQt6 导入失败: {e}")
        return False
    
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel
        print("✓ PyQt6.QtWidgets 导入成功")
    except ImportError as e:
        print(f"✗ PyQt6.QtWidgets 导入失败: {e}")
        return False
    
    try:
        import sqlalchemy
        print("✓ SQLAlchemy 导入成功")
    except ImportError as e:
        print(f"✗ SQLAlchemy 导入失败: {e}")
        return False
    
    try:
        import aiohttp
        print("✓ aiohttp 导入成功")
    except ImportError as e:
        print(f"✗ aiohttp 导入失败: {e}")
        return False
    
    return True


def test_simple_gui():
    """测试简单GUI"""
    print("\n测试简单GUI...")
    
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
        from PyQt6.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        window = QMainWindow()
        window.setWindowTitle("AI视频生成器 2.0 - 测试")
        window.setGeometry(100, 100, 400, 300)
        
        # 创建中央组件
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        title_label = QLabel("AI视频生成器 2.0")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2196F3;
                margin: 20px;
            }
        """)
        layout.addWidget(title_label)
        
        status_label = QLabel("程序运行正常！")
        status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        status_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #4CAF50;
                margin: 10px;
            }
        """)
        layout.addWidget(status_label)
        
        info_label = QLabel("这是一个简单的测试窗口，用于验证PyQt6是否正常工作。")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #666;
                margin: 10px;
            }
        """)
        layout.addWidget(info_label)
        
        # 显示窗口
        window.show()
        print("✓ GUI窗口创建成功")
        print("窗口已显示，请检查是否能看到测试窗口")
        
        # 运行应用程序（5秒后自动关闭）
        from PyQt6.QtCore import QTimer
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(5000)  # 5秒后关闭
        
        return app.exec()
        
    except Exception as e:
        print(f"✗ GUI测试失败: {e}")
        return False


def test_project_imports():
    """测试项目模块导入"""
    print("\n测试项目模块导入...")
    
    try:
        from src.core.config import ConfigManager
        print("✓ ConfigManager 导入成功")
    except ImportError as e:
        print(f"✗ ConfigManager 导入失败: {e}")
        return False
    
    try:
        from src.models.base import Base
        print("✓ Base 模型导入成功")
    except ImportError as e:
        print(f"✗ Base 模型导入失败: {e}")
        return False
    
    try:
        from src.ui.themes import get_theme_manager
        print("✓ 主题管理器导入成功")
    except ImportError as e:
        print(f"✗ 主题管理器导入失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("AI视频生成器 2.0 - 简单测试")
    print("=" * 50)
    
    # 测试基本导入
    if not test_imports():
        print("\n基本导入测试失败，请检查依赖安装")
        return 1
    
    # 测试项目导入
    if not test_project_imports():
        print("\n项目模块导入测试失败，请检查代码结构")
        return 1
    
    # 测试GUI
    print("\n所有导入测试通过！")
    print("现在测试GUI功能...")
    
    result = test_simple_gui()
    
    if result == 0:
        print("\n✓ 所有测试通过！程序可以正常运行。")
    else:
        print("\n✗ GUI测试失败")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
