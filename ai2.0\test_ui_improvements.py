#!/usr/bin/env python3
"""测试UI改进

测试修复后的UI功能，包括：
1. 导航栏显示完整
2. 按钮功能正常
3. 布局优化
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_ui_improvements():
    """测试UI改进"""
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        
        print("正在启动UI改进测试...")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("AI视频生成器 UI测试")
        
        # 导入主窗口
        from src.ui.main_window import MainWindow
        from src.core.config import ConfigManager
        
        # 创建配置
        config = ConfigManager()
        
        # 创建主窗口
        window = MainWindow(config)
        window.setWindowTitle("AI视频生成器 2.0 - UI改进测试")
        
        # 显示窗口
        window.show()
        
        print("UI测试窗口已显示")
        print("\n测试项目：")
        print("1. 检查左侧导航栏是否显示完整")
        print("2. 点击导航项目是否能正常切换页面")
        print("3. 点击'新建项目'按钮是否弹出对话框")
        print("4. 点击'打开项目'按钮是否弹出文件选择")
        print("5. 在分镜生成页面输入文本并点击生成")
        print("6. 在设置页面切换主题")
        print("7. 检查右侧信息面板是否占位合理")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"UI测试失败：{e}")
        import traceback
        traceback.print_exc()
        return 1


def main():
    """主函数"""
    print("AI视频生成器 2.0 - UI改进测试")
    print("=" * 50)
    
    try:
        return test_ui_improvements()
    except KeyboardInterrupt:
        print("\n用户中断测试")
        return 0
    except Exception as e:
        print(f"测试异常：{e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
