#!/usr/bin/env python3
"""AI视频生成器2.0启动脚本"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查必要的依赖"""
    required_packages = {
        "PyQt6": "PyQt6",
        "sqlalchemy": "sqlalchemy",
        "aiohttp": "aiohttp",
        "aiofiles": "aiofiles",
        "psutil": "psutil"
    }

    missing_packages = []

    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        print("错误：缺少必要的依赖包：")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖：")
        print(f"pip install {' '.join(missing_packages)}")
        return False

    return True


def create_directories():
    """创建必要的目录"""
    directories = [
        "logs",
        "config",
        "generated",
        "generated/images",
        "generated/videos",
        "generated/voice",
        "cache"
    ]

    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)


if __name__ == "__main__":
    print("AI视频生成器 2.0")
    print("=" * 50)

    # 检查依赖
    print("正在检查依赖...")
    if not check_dependencies():
        sys.exit(1)

    # 创建必要目录
    print("正在创建目录...")
    create_directories()

    # 启动应用程序
    print("正在启动应用程序...")
    try:
        from main import run_app
        sys.exit(run_app())
    except ImportError as e:
        print(f"导入错误：{e}")
        print("请确保所有源文件都在正确的位置")
        sys.exit(1)
    except Exception as e:
        print(f"启动失败：{e}")
        sys.exit(1)
