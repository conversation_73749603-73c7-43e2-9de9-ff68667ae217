#!/bin/bash

# AI视频生成器 2.0 启动脚本

echo "========================================"
echo "AI视频生成器 2.0"
echo "========================================"
echo

# 检查Python版本
python_version=$(python3 --version 2>/dev/null | cut -d' ' -f2)
if [ -z "$python_version" ]; then
    echo "错误：未找到Python 3"
    echo "请安装Python 3.8或更高版本"
    exit 1
fi

echo "Python版本：$python_version"

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo "正在创建虚拟环境..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "错误：无法创建虚拟环境"
        exit 1
    fi
fi

# 激活虚拟环境
echo "正在激活虚拟环境..."
source venv/bin/activate

# 检查依赖是否安装
echo "正在检查依赖..."
python -c "import PyQt6" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装依赖包..."
    pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误：依赖安装失败"
        exit 1
    fi
fi

# 启动程序
echo "正在启动AI视频生成器 2.0..."
echo
python run.py

# 检查退出状态
if [ $? -ne 0 ]; then
    echo
    echo "程序异常退出"
    read -p "按Enter键继续..."
else
    echo
    echo "程序正常退出"
fi
