# -*- coding: utf-8 -*-
"""
视频生成服务实现
集成CogVideoX、Runway ML、Pika Labs、Stable Video Diffusion等视频生成服务
"""

import asyncio
import base64
import json
import logging
import os
import tempfile
import time
from typing import Dict, Any, List, Optional, Union
import aiohttp
import aiofiles
from dataclasses import dataclass
from pathlib import Path
import subprocess

from ..core.mcp_service_manager import (
    MCPServiceInterface, ServiceRequest, ServiceResponse, ServiceType, ServiceStatus
)

logger = logging.getLogger(__name__)

@dataclass
class VideoRequest:
    """视频生成请求数据结构"""
    prompt: str = ""
    image_path: str = ""  # 图转视频的输入图像
    duration: float = 5.0  # 视频时长（秒）
    fps: int = 24  # 帧率
    width: int = 1280
    height: int = 720
    motion_strength: float = 0.8  # 运动强度 0.0-1.0
    seed: int = -1  # -1表示随机
    style: str = "realistic"  # realistic, anime, artistic等
    output_path: Optional[str] = None
    model: str = ""  # 指定模型

@dataclass
class VideoResponse:
    """视频生成响应数据结构"""
    video_path: str
    duration: float = 0.0
    fps: int = 0
    width: int = 0
    height: int = 0
    file_size: int = 0
    generation_time: float = 0.0
    model_used: str = ""
    service_used: str = ""
    success: bool = True
    error_message: str = ""
    prompt_used: str = ""

class BaseVideoClient(MCPServiceInterface):
    """视频生成客户端基类"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.session = None
        self.supported_models = self._get_supported_models()
        self.supported_formats = self._get_supported_formats()
    
    def _get_supported_models(self) -> List[str]:
        """获取支持的模型列表（子类实现）"""
        return []
    
    def _get_supported_formats(self) -> List[str]:
        """获取支持的格式列表（子类实现）"""
        return ['mp4', 'mov', 'avi']
    
    async def initialize(self) -> bool:
        """初始化视频生成客户端"""
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=300)  # 5分钟超时
            )
            
            health_ok = await self.health_check()
            if health_ok:
                logger.info(f"{self.name}: 视频生成服务初始化成功")
                return True
            else:
                logger.warning(f"{self.name}: 视频生成服务健康检查失败")
                return False
                
        except Exception as e:
            logger.error(f"{self.name}: 视频生成服务初始化失败 - {e}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        if self.session:
            await self.session.close()
    
    async def process(self, request: ServiceRequest) -> ServiceResponse:
        """处理视频生成请求"""
        start_time = time.time()
        
        try:
            action = request.parameters.get('action', 'generate')
            
            if action == 'generate':
                video_request = VideoRequest(**request.parameters.get('video_request', {}))
                result = await self.generate_video(video_request)
                
                return ServiceResponse(
                    success=True,
                    data=result,
                    execution_time=time.time() - start_time,
                    service_name=self.name
                )
            
            elif action == 'image_to_video':
                video_request = VideoRequest(**request.parameters.get('video_request', {}))
                result = await self.image_to_video(video_request)
                
                return ServiceResponse(
                    success=True,
                    data=result,
                    execution_time=time.time() - start_time,
                    service_name=self.name
                )
            
            elif action == 'get_status':
                task_id = request.parameters.get('task_id', '')
                result = await self.get_generation_status(task_id)
                
                return ServiceResponse(
                    success=True,
                    data=result,
                    execution_time=time.time() - start_time,
                    service_name=self.name
                )
            
            else:
                return ServiceResponse(
                    success=False,
                    error=f"不支持的操作: {action}",
                    service_name=self.name
                )
                
        except Exception as e:
            return ServiceResponse(
                success=False,
                error=str(e),
                execution_time=time.time() - start_time,
                service_name=self.name
            )
    
    async def generate_video(self, request: VideoRequest) -> VideoResponse:
        """生成视频（子类实现）"""
        raise NotImplementedError
    
    async def image_to_video(self, request: VideoRequest) -> VideoResponse:
        """图像转视频（子类实现）"""
        raise NotImplementedError
    
    async def get_generation_status(self, task_id: str) -> Dict[str, Any]:
        """获取生成状态（子类实现）"""
        raise NotImplementedError

class CogVideoXClient(BaseVideoClient):
    """CogVideoX客户端（智谱AI）"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.api_key = config.get('api_key', '')
        self.base_url = config.get('base_url', 'https://open.bigmodel.cn/api/paas/v4/videos/generations')
    
    def _get_supported_models(self) -> List[str]:
        """CogVideoX支持的模型"""
        return ['cogvideox']
    
    async def health_check(self) -> bool:
        """健康检查"""
        return bool(self.api_key)
    
    async def generate_video(self, request: VideoRequest) -> VideoResponse:
        """使用CogVideoX生成视频"""
        if not self.api_key:
            raise Exception("智谱AI API密钥未配置")
        
        try:
            start_time = time.time()
            
            # 设置输出路径
            if not request.output_path:
                output_dir = tempfile.gettempdir()
                timestamp = int(time.time() * 1000)
                request.output_path = os.path.join(output_dir, f"cogvideox_{timestamp}.mp4")
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(request.output_path), exist_ok=True)
            
            # 构建请求数据
            data = {
                'model': 'cogvideox',
                'prompt': request.prompt
            }
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            # 提交生成任务
            async with self.session.post(self.base_url, headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    task_id = result.get('id', '')
                    
                    if task_id:
                        # 等待生成完成
                        video_url = await self._wait_for_completion(task_id)
                        
                        if video_url:
                            # 下载视频
                            await self._download_video(video_url, request.output_path)
                            
                            # 获取视频信息
                            video_info = await self._get_video_info(request.output_path)
                            
                            return VideoResponse(
                                video_path=request.output_path,
                                duration=video_info.get('duration', request.duration),
                                fps=video_info.get('fps', request.fps),
                                width=video_info.get('width', request.width),
                                height=video_info.get('height', request.height),
                                file_size=os.path.getsize(request.output_path),
                                generation_time=time.time() - start_time,
                                model_used='cogvideox',
                                service_used=self.name,
                                success=True,
                                prompt_used=request.prompt
                            )
                        else:
                            raise Exception("视频生成失败")
                    else:
                        raise Exception("无法获取任务ID")
                else:
                    error_text = await response.text()
                    raise Exception(f"CogVideoX API错误 {response.status}: {error_text}")
                    
        except Exception as e:
            logger.error(f"CogVideoX视频生成失败: {e}")
            return VideoResponse(
                video_path="",
                success=False,
                error_message=str(e),
                service_used=self.name
            )
    
    async def image_to_video(self, request: VideoRequest) -> VideoResponse:
        """图像转视频"""
        if not request.image_path or not os.path.exists(request.image_path):
            raise Exception("输入图像路径无效")
        
        # 读取图像并转换为base64
        async with aiofiles.open(request.image_path, 'rb') as f:
            image_data = await f.read()
            image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        try:
            start_time = time.time()
            
            if not request.output_path:
                output_dir = tempfile.gettempdir()
                timestamp = int(time.time() * 1000)
                request.output_path = os.path.join(output_dir, f"cogvideox_i2v_{timestamp}.mp4")
            
            os.makedirs(os.path.dirname(request.output_path), exist_ok=True)
            
            data = {
                'model': 'cogvideox',
                'prompt': request.prompt,
                'image_url': f"data:image/jpeg;base64,{image_base64}"
            }
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            async with self.session.post(self.base_url, headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    task_id = result.get('id', '')
                    
                    if task_id:
                        video_url = await self._wait_for_completion(task_id)
                        
                        if video_url:
                            await self._download_video(video_url, request.output_path)
                            video_info = await self._get_video_info(request.output_path)
                            
                            return VideoResponse(
                                video_path=request.output_path,
                                duration=video_info.get('duration', request.duration),
                                fps=video_info.get('fps', request.fps),
                                width=video_info.get('width', request.width),
                                height=video_info.get('height', request.height),
                                file_size=os.path.getsize(request.output_path),
                                generation_time=time.time() - start_time,
                                model_used='cogvideox',
                                service_used=self.name,
                                success=True,
                                prompt_used=request.prompt
                            )
                        else:
                            raise Exception("图转视频生成失败")
                    else:
                        raise Exception("无法获取任务ID")
                else:
                    error_text = await response.text()
                    raise Exception(f"CogVideoX图转视频API错误 {response.status}: {error_text}")
                    
        except Exception as e:
            logger.error(f"CogVideoX图转视频失败: {e}")
            return VideoResponse(
                video_path="",
                success=False,
                error_message=str(e),
                service_used=self.name
            )
    
    async def _wait_for_completion(self, task_id: str, timeout: int = 300) -> Optional[str]:
        """等待生成完成"""
        status_url = f"https://open.bigmodel.cn/api/paas/v4/async-result/{task_id}"
        headers = {
            'Authorization': f'Bearer {self.api_key}'
        }
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                async with self.session.get(status_url, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        task_status = result.get('task_status', '')
                        
                        if task_status == 'SUCCESS':
                            video_result = result.get('video_result', [])
                            if video_result:
                                return video_result[0].get('url', '')
                        elif task_status == 'FAIL':
                            raise Exception(f"视频生成失败: {result.get('error', '未知错误')}")
            except Exception as e:
                logger.error(f"检查任务状态失败: {e}")
            
            await asyncio.sleep(5)  # 每5秒检查一次
        
        raise Exception("视频生成超时")
    
    async def _download_video(self, video_url: str, output_path: str):
        """下载视频文件"""
        async with self.session.get(video_url) as response:
            if response.status == 200:
                async with aiofiles.open(output_path, 'wb') as f:
                    async for chunk in response.content.iter_chunked(8192):
                        await f.write(chunk)
            else:
                raise Exception(f"下载视频失败: {response.status}")
    
    async def _get_video_info(self, video_path: str) -> Dict[str, Any]:
        """获取视频信息"""
        try:
            # 使用ffprobe获取视频信息
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                info = json.loads(result.stdout)
                video_stream = next(
                    (s for s in info['streams'] if s['codec_type'] == 'video'),
                    {}
                )
                
                return {
                    'duration': float(info.get('format', {}).get('duration', 0)),
                    'width': int(video_stream.get('width', 0)),
                    'height': int(video_stream.get('height', 0)),
                    'fps': eval(video_stream.get('r_frame_rate', '24/1'))
                }
        except Exception as e:
            logger.error(f"获取视频信息失败: {e}")
        
        return {}

class RunwayMLClient(BaseVideoClient):
    """Runway ML客户端"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.api_key = config.get('api_key', '')
        self.base_url = config.get('base_url', 'https://api.runwayml.com/v1')
    
    async def health_check(self) -> bool:
        """健康检查"""
        return bool(self.api_key)
    
    async def generate_video(self, request: VideoRequest) -> VideoResponse:
        """使用Runway ML生成视频"""
        if not self.api_key:
            raise Exception("Runway ML API密钥未配置")
        
        # Runway ML API实现（需要根据实际API文档调整）
        # 这里提供一个基础框架
        try:
            start_time = time.time()
            
            if not request.output_path:
                output_dir = tempfile.gettempdir()
                timestamp = int(time.time() * 1000)
                request.output_path = os.path.join(output_dir, f"runway_{timestamp}.mp4")
            
            # 实际API调用逻辑
            # ...
            
            return VideoResponse(
                video_path=request.output_path,
                success=False,
                error_message="Runway ML API集成待实现",
                service_used=self.name
            )
            
        except Exception as e:
            logger.error(f"Runway ML视频生成失败: {e}")
            return VideoResponse(
                video_path="",
                success=False,
                error_message=str(e),
                service_used=self.name
            )

class PikaLabsClient(BaseVideoClient):
    """Pika Labs客户端"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.api_key = config.get('api_key', '')
        self.base_url = config.get('base_url', 'https://api.pika.art/v1')
    
    async def health_check(self) -> bool:
        """健康检查"""
        return bool(self.api_key)
    
    async def generate_video(self, request: VideoRequest) -> VideoResponse:
        """使用Pika Labs生成视频"""
        if not self.api_key:
            raise Exception("Pika Labs API密钥未配置")
        
        # Pika Labs API实现（需要根据实际API文档调整）
        try:
            start_time = time.time()
            
            if not request.output_path:
                output_dir = tempfile.gettempdir()
                timestamp = int(time.time() * 1000)
                request.output_path = os.path.join(output_dir, f"pika_{timestamp}.mp4")
            
            # 实际API调用逻辑
            # ...
            
            return VideoResponse(
                video_path=request.output_path,
                success=False,
                error_message="Pika Labs API集成待实现",
                service_used=self.name
            )
            
        except Exception as e:
            logger.error(f"Pika Labs视频生成失败: {e}")
            return VideoResponse(
                video_path="",
                success=False,
                error_message=str(e),
                service_used=self.name
            )

class VideoServiceManager:
    """视频生成服务管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.clients = {}
        self.routing_strategy = config.get('routing_strategy', 'quality_first')
        self._initialize_clients()
    
    def _initialize_clients(self):
        """初始化视频生成客户端"""
        engines = self.config.get('engines', {})
        
        # CogVideoX（智谱AI）
        if 'cogvideox' in engines and engines['cogvideox'].get('api_key'):
            cogvideox_config = engines['cogvideox']
            self.clients['cogvideox'] = CogVideoXClient('CogVideoX', cogvideox_config)
        
        # Runway ML
        if 'runway' in engines and engines['runway'].get('api_key'):
            runway_config = engines['runway']
            self.clients['runway'] = RunwayMLClient('Runway ML', runway_config)
        
        # Pika Labs
        if 'pika' in engines and engines['pika'].get('api_key'):
            pika_config = engines['pika']
            self.clients['pika'] = PikaLabsClient('Pika Labs', pika_config)
    
    def get_client(self, name: str) -> Optional[BaseVideoClient]:
        """获取视频生成客户端"""
        return self.clients.get(name)
    
    def get_all_clients(self) -> List[BaseVideoClient]:
        """获取所有视频生成客户端"""
        return list(self.clients.values())
    
    def get_best_client(self, task_type: str = "text_to_video") -> Optional[BaseVideoClient]:
        """获取最佳视频生成客户端"""
        if self.routing_strategy == 'quality_first':
            # 优先使用高质量服务
            if 'cogvideox' in self.clients:
                return self.clients['cogvideox']
            elif 'runway' in self.clients:
                return self.clients['runway']
            elif 'pika' in self.clients:
                return self.clients['pika']
        
        elif self.routing_strategy == 'speed_first':
            # 优先使用快速服务
            if 'pika' in self.clients:
                return self.clients['pika']
            elif 'cogvideox' in self.clients:
                return self.clients['cogvideox']
        
        # 默认返回第一个可用客户端
        return next(iter(self.clients.values())) if self.clients else None
    
    async def initialize_all(self):
        """初始化所有客户端"""
        for client in self.clients.values():
            await client.initialize()
    
    async def cleanup_all(self):
        """清理所有客户端"""
        for client in self.clients.values():
            await client.cleanup()
    
    async def generate_video(self, prompt: str = "", image_path: str = "",
                           duration: float = 5.0, width: int = 1280, height: int = 720,
                           output_path: str = None, engine: str = None) -> Optional[VideoResponse]:
        """生成视频（便捷方法）"""
        # 选择引擎
        if engine:
            client = self.get_client(engine)
        else:
            task_type = "image_to_video" if image_path else "text_to_video"
            client = self.get_best_client(task_type)
        
        if not client:
            raise Exception("没有可用的视频生成服务")
        
        request = VideoRequest(
            prompt=prompt,
            image_path=image_path,
            duration=duration,
            width=width,
            height=height,
            output_path=output_path
        )
        
        if image_path:
            return await client.image_to_video(request)
        else:
            return await client.generate_video(request)
    
    async def generate_storyboard_videos(self, storyboard_data: Dict[str, Any],
                                       image_files: List[str], output_dir: str) -> List[str]:
        """为分镜生成视频"""
        video_files = []
        
        try:
            shots = storyboard_data.get('shots', [])
            
            for i, (shot, image_file) in enumerate(zip(shots, image_files)):
                if os.path.exists(image_file):
                    prompt = shot.get('visual_description', '')
                    output_path = os.path.join(output_dir, f"shot_{i+1}.mp4")
                    
                    response = await self.generate_video(
                        prompt=prompt,
                        image_path=image_file,
                        duration=5.0,
                        output_path=output_path
                    )
                    
                    if response and response.success:
                        video_files.append(response.video_path)
                    else:
                        logger.warning(f"分镜 {i+1} 视频生成失败")
                
                # 添加延迟避免API限制
                await asyncio.sleep(2.0)
            
            return video_files
            
        except Exception as e:
            logger.error(f"分镜视频生成失败: {e}")
            return []
    
    async def merge_videos(self, video_files: List[str], audio_files: List[str],
                         output_path: str) -> bool:
        """合并视频和音频"""
        try:
            if not video_files:
                return False
            
            # 创建临时文件列表
            temp_list_file = os.path.join(tempfile.gettempdir(), 'video_list.txt')
            
            # 写入视频文件列表
            async with aiofiles.open(temp_list_file, 'w', encoding='utf-8') as f:
                for video_file in video_files:
                    if os.path.exists(video_file):
                        await f.write(f"file '{video_file}'\n")
            
            # 使用ffmpeg合并视频
            cmd = [
                'ffmpeg', '-f', 'concat', '-safe', '0',
                '-i', temp_list_file,
                '-c', 'copy',
                output_path, '-y'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            # 清理临时文件
            if os.path.exists(temp_list_file):
                os.remove(temp_list_file)
            
            if result.returncode == 0:
                logger.info(f"视频合并成功: {output_path}")
                
                # 如果有音频文件，添加音频轨道
                if audio_files:
                    await self._add_audio_to_video(output_path, audio_files)
                
                return True
            else:
                logger.error(f"视频合并失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"视频合并异常: {e}")
            return False
    
    async def _add_audio_to_video(self, video_path: str, audio_files: List[str]):
        """为视频添加音频轨道"""
        try:
            if not audio_files:
                return
            
            # 合并音频文件
            temp_audio = os.path.join(tempfile.gettempdir(), 'merged_audio.mp3')
            
            if len(audio_files) == 1:
                # 只有一个音频文件，直接使用
                temp_audio = audio_files[0]
            else:
                # 多个音频文件，需要合并
                audio_list_file = os.path.join(tempfile.gettempdir(), 'audio_list.txt')
                
                async with aiofiles.open(audio_list_file, 'w', encoding='utf-8') as f:
                    for audio_file in audio_files:
                        if os.path.exists(audio_file):
                            await f.write(f"file '{audio_file}'\n")
                
                cmd = [
                    'ffmpeg', '-f', 'concat', '-safe', '0',
                    '-i', audio_list_file,
                    '-c', 'copy',
                    temp_audio, '-y'
                ]
                
                subprocess.run(cmd, capture_output=True)
                
                if os.path.exists(audio_list_file):
                    os.remove(audio_list_file)
            
            # 将音频添加到视频
            output_with_audio = video_path.replace('.mp4', '_with_audio.mp4')
            
            cmd = [
                'ffmpeg',
                '-i', video_path,
                '-i', temp_audio,
                '-c:v', 'copy',
                '-c:a', 'aac',
                '-shortest',
                output_with_audio, '-y'
            ]
            
            result = subprocess.run(cmd, capture_output=True)
            
            if result.returncode == 0:
                # 替换原文件
                os.replace(output_with_audio, video_path)
                logger.info(f"音频添加成功: {video_path}")
            
            # 清理临时文件
            if temp_audio != audio_files[0] and os.path.exists(temp_audio):
                os.remove(temp_audio)
                
        except Exception as e:
            logger.error(f"添加音频失败: {e}")

# 便捷函数
def create_video_service_manager(config: Dict[str, Any]) -> VideoServiceManager:
    """创建视频生成服务管理器"""
    return VideoServiceManager(config)

if __name__ == "__main__":
    # 测试代码
    async def test_video_service():
        # 模拟配置
        test_config = {
            'routing_strategy': 'quality_first',
            'engines': {
                'cogvideox': {
                    'api_key': '',  # 需要配置实际API密钥
                    'base_url': 'https://open.bigmodel.cn/api/paas/v4/videos/generations'
                }
            }
        }
        
        manager = create_video_service_manager(test_config)
        await manager.initialize_all()
        
        # 测试文本转视频
        try:
            response = await manager.generate_video(
                prompt="一只可爱的小猫在花园里玩耍，阳光明媚，画面温馨",
                duration=5.0,
                width=1280,
                height=720
            )
            if response and response.success:
                print(f"视频生成成功: {response.video_path}")
                print(f"使用服务: {response.service_used}")
                print(f"生成时间: {response.generation_time:.2f}秒")
                print(f"文件大小: {response.file_size} bytes")
        except Exception as e:
            print(f"视频生成失败: {e}")
        
        await manager.cleanup_all()
    
    # asyncio.run(test_video_service())
    print("视频生成服务模块已加载")