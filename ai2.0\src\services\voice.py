"""语音合成服务

实现各种语音合成服务的接口。
"""

from typing import Dict, Any, Optional, List
import aiohttp
import aiofiles
from pathlib import Path
import uuid
from datetime import datetime
import base64
import json

from src.services.base import VoiceGenerationService, ServiceConfig, ServiceResponse, ServiceStatus
from src.core.exceptions import NetworkError, APIKeyInvalidError, RateLimitExceededError
from src.utils.logger import get_logger


class EdgeTTSService(VoiceGenerationService):
    """Edge TTS语音合成服务（免费）"""
    
    def __init__(self, config: ServiceConfig):
        super().__init__(config)
        self.output_dir = Path("generated/voice")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.logger = get_logger(__name__)
        
        # 支持的语音列表
        self.voices = {
            "zh-CN": {
                "female": ["zh-CN-<PERSON>xiaoNeural", "zh-<PERSON><PERSON>-<PERSON><PERSON>eural", "zh-CN-XiaomengNeural"],
                "male": ["zh-CN-YunxiNeural", "zh-CN-YunyangNeural", "zh-CN-<PERSON>jianNeural"]
            },
            "en-US": {
                "female": ["en-US-AriaNeural", "en-US-JennyNeural", "en-US-MichelleNeural"],
                "male": ["en-US-ChristopherNeural", "en-US-EricNeural", "en-US-GuyNeural"]
            }
        }
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            # Edge TTS是免费服务，直接返回可用
            self._set_status(ServiceStatus.AVAILABLE)
            return True
        except Exception as e:
            self._set_status(ServiceStatus.ERROR, str(e))
            return False
    
    async def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            "provider": "edge_tts",
            "status": self.status.value,
            "features": ["text_to_speech", "multiple_voices", "free_service"],
            "supported_languages": list(self.voices.keys()),
            "voices": self.voices
        }
    
    async def generate(self, text: str, **kwargs) -> ServiceResponse[str]:
        """生成语音"""
        voice = kwargs.get("voice", "zh-CN-XiaoxiaoNeural")
        rate = kwargs.get("rate", "0%")
        pitch = kwargs.get("pitch", "0%")
        return await self.text_to_speech(text, voice, rate, pitch)
    
    async def validate_text(self, text: str) -> bool:
        """验证文本"""
        if not text or not text.strip():
            return False
        if len(text) > 5000:  # Edge TTS的限制
            return False
        return True
    
    async def text_to_speech(
        self, 
        text: str, 
        voice: str = "zh-CN-XiaoxiaoNeural",
        rate: str = "0%",
        pitch: str = "0%"
    ) -> ServiceResponse[str]:
        """文本转语音"""
        try:
            if not await self.validate_text(text):
                return ServiceResponse.error_response("Invalid text input")
            
            # 使用edge-tts库（需要安装：pip install edge-tts）
            try:
                import edge_tts
            except ImportError:
                return ServiceResponse.error_response("edge-tts library not installed")
            
            # 生成唯一文件名
            file_id = str(uuid.uuid4())
            file_name = f"{file_id}.mp3"
            file_path = self.output_dir / file_name
            
            # 创建TTS通信器
            communicate = edge_tts.Communicate(text, voice, rate=rate, pitch=pitch)
            
            # 生成语音文件
            await communicate.save(str(file_path))
            
            # 检查文件是否生成成功
            if not file_path.exists():
                return ServiceResponse.error_response("Failed to generate voice file")
            
            metadata = {
                "voice": voice,
                "rate": rate,
                "pitch": pitch,
                "text_length": len(text),
                "file_size": file_path.stat().st_size,
                "duration_estimate": len(text) * 0.1,  # 估算时长（秒）
                "generated_at": datetime.utcnow().isoformat()
            }
            
            return ServiceResponse.success_response(str(file_path), metadata)
            
        except Exception as e:
            self.logger.error(f"Edge TTS generation failed: {e}")
            return ServiceResponse.error_response(str(e))
    
    async def get_available_voices(self, language: str = "zh-CN") -> List[str]:
        """获取可用语音列表"""
        if language in self.voices:
            all_voices = []
            for gender_voices in self.voices[language].values():
                all_voices.extend(gender_voices)
            return all_voices
        return []
    
    async def clone_voice(self, audio_path: str, text: str) -> ServiceResponse[str]:
        """语音克隆（Edge TTS不支持）"""
        return ServiceResponse.error_response("Voice cloning not supported by Edge TTS")


class OpenAITTSService(VoiceGenerationService):
    """OpenAI TTS语音合成服务"""
    
    def __init__(self, config: ServiceConfig):
        super().__init__(config)
        self.base_url = config.base_url or "https://api.openai.com/v1"
        self.model = config.custom_settings.get("model", "tts-1") if config.custom_settings else "tts-1"
        self.output_dir = Path("generated/voice")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.logger = get_logger(__name__)
        
        # 支持的语音
        self.voices = ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.config.api_key:
                self._set_status(ServiceStatus.ERROR, "API key not configured")
                return False
            
            # 简单的API测试
            headers = {
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/models",
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        self._set_status(ServiceStatus.AVAILABLE)
                        return True
                    elif response.status == 401:
                        self._set_status(ServiceStatus.ERROR, "Invalid API key")
                        return False
                    else:
                        self._set_status(ServiceStatus.UNAVAILABLE, f"HTTP {response.status}")
                        return False
        except Exception as e:
            self._set_status(ServiceStatus.ERROR, str(e))
            return False
    
    async def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            "provider": "openai_tts",
            "model": self.model,
            "base_url": self.base_url,
            "status": self.status.value,
            "features": ["text_to_speech", "high_quality", "multiple_voices"],
            "voices": self.voices
        }
    
    async def generate(self, text: str, **kwargs) -> ServiceResponse[str]:
        """生成语音"""
        voice = kwargs.get("voice", "alloy")
        speed = kwargs.get("speed", 1.0)
        return await self.text_to_speech(text, voice, speed)
    
    async def validate_text(self, text: str) -> bool:
        """验证文本"""
        if not text or not text.strip():
            return False
        if len(text) > 4096:  # OpenAI TTS的限制
            return False
        return True
    
    async def text_to_speech(
        self, 
        text: str, 
        voice: str = "alloy",
        speed: float = 1.0
    ) -> ServiceResponse[str]:
        """文本转语音"""
        try:
            if not self.config.api_key:
                return ServiceResponse.error_response("API key not configured")
            
            if not await self.validate_text(text):
                return ServiceResponse.error_response("Invalid text input")
            
            if voice not in self.voices:
                return ServiceResponse.error_response(f"Unsupported voice: {voice}")
            
            payload = {
                "model": self.model,
                "input": text,
                "voice": voice,
                "speed": max(0.25, min(4.0, speed))  # 限制速度范围
            }
            
            headers = {
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/audio/speech",
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                ) as response:
                    if response.status == 200:
                        # 生成唯一文件名
                        file_id = str(uuid.uuid4())
                        file_name = f"{file_id}.mp3"
                        file_path = self.output_dir / file_name
                        
                        # 保存音频文件
                        async with aiofiles.open(file_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                await f.write(chunk)
                        
                        metadata = {
                            "model": self.model,
                            "voice": voice,
                            "speed": speed,
                            "text_length": len(text),
                            "file_size": file_path.stat().st_size,
                            "generated_at": datetime.utcnow().isoformat()
                        }
                        
                        return ServiceResponse.success_response(str(file_path), metadata)
                    
                    elif response.status == 401:
                        raise APIKeyInvalidError("Invalid OpenAI API key", provider="openai")
                    
                    elif response.status == 429:
                        retry_after = response.headers.get("Retry-After")
                        raise RateLimitExceededError(
                            "OpenAI rate limit exceeded", 
                            retry_after=int(retry_after) if retry_after else None
                        )
                    
                    else:
                        response_data = await response.json()
                        error_msg = response_data.get("error", {}).get("message", f"HTTP {response.status}")
                        return ServiceResponse.error_response(error_msg, str(response.status))
        
        except (APIKeyInvalidError, RateLimitExceededError):
            raise
        except Exception as e:
            self.logger.error(f"OpenAI TTS generation failed: {e}")
            return ServiceResponse.error_response(str(e))
    
    async def get_available_voices(self) -> List[str]:
        """获取可用语音列表"""
        return self.voices.copy()
    
    async def clone_voice(self, audio_path: str, text: str) -> ServiceResponse[str]:
        """语音克隆（OpenAI TTS不支持）"""
        return ServiceResponse.error_response("Voice cloning not supported by OpenAI TTS")
