# AI视频生成器 2.0 用户手册

## 目录
1. [简介](#简介)
2. [系统要求](#系统要求)
3. [安装指南](#安装指南)
4. [快速开始](#快速开始)
5. [功能详解](#功能详解)
6. [常见问题](#常见问题)
7. [技术支持](#技术支持)

## 简介

AI视频生成器 2.0 是一款基于人工智能的视频内容生成工具，能够根据文本描述自动生成分镜脚本、图像、语音和视频内容。

### 主要功能
- 📝 智能分镜生成：根据文本自动生成详细的分镜脚本
- 🎨 AI图像生成：为每个镜头生成对应的图像
- 🎵 语音合成：将文本转换为自然的语音
- 🎬 视频制作：组合图像、语音生成完整视频
- 🎯 一致性检查：确保视频内容的连贯性
- 🎨 多主题支持：浅色/深色主题切换

## 系统要求

### 最低配置
- **操作系统**：Windows 10/11, macOS 10.15+, Linux Ubuntu 18.04+
- **处理器**：Intel i5 或 AMD Ryzen 5 同等性能
- **内存**：8GB RAM
- **存储空间**：2GB 可用空间
- **网络**：稳定的互联网连接（用于AI服务）

### 推荐配置
- **处理器**：Intel i7 或 AMD Ryzen 7 同等性能
- **内存**：16GB RAM
- **显卡**：独立显卡（用于视频处理加速）
- **存储空间**：10GB 可用空间（SSD推荐）

## 安装指南

### 方式一：使用安装包（推荐）
1. 下载最新版本的安装包
2. 双击运行安装程序
3. 按照向导完成安装
4. 启动程序

### 方式二：从源码运行
1. 确保已安装 Python 3.8+
2. 克隆或下载项目源码
3. 创建虚拟环境：
   ```bash
   python -m venv venv
   ```
4. 激活虚拟环境：
   - Windows: `venv\Scripts\activate`
   - macOS/Linux: `source venv/bin/activate`
5. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```
6. 运行程序：
   ```bash
   python run.py
   ```

## 快速开始

### 第一次使用
1. **启动程序**：双击桌面图标或从开始菜单启动
2. **配置AI服务**：
   - 点击"设置"菜单
   - 选择"AI服务配置"
   - 输入相应的API密钥
3. **创建第一个项目**：
   - 点击"新建项目"
   - 输入项目名称和描述
   - 输入故事文本
   - 选择视频风格

### 基本工作流程
1. **项目创建** → 2. **分镜生成** → 3. **内容生成** → 4. **视频合成** → 5. **导出分享**

## 功能详解

### 1. 项目管理
- **新建项目**：创建新的视频项目
- **打开项目**：加载已有项目
- **项目设置**：配置视频参数（分辨率、帧率等）
- **项目历史**：查看最近使用的项目

### 2. 分镜生成
- **文本输入**：输入故事文本或脚本
- **风格选择**：选择视频风格（动画、真实、卡通等）
- **自动分镜**：AI自动生成分镜脚本
- **手动编辑**：调整分镜内容和时长

### 3. 内容生成
- **图像生成**：为每个镜头生成对应图像
- **语音合成**：将文本转换为语音
- **视频生成**：生成动态视频片段
- **批量处理**：一键生成所有内容

### 4. 编辑和调整
- **镜头编辑**：修改镜头内容和参数
- **时长调整**：调整每个镜头的时长
- **效果添加**：添加转场和特效
- **音频调整**：调整语音音量和速度

### 5. 预览和导出
- **实时预览**：预览生成的视频
- **质量设置**：选择导出质量
- **格式选择**：支持多种视频格式
- **批量导出**：导出多个版本

## AI服务配置

### 支持的AI服务

#### LLM服务（文本生成）
- **OpenAI GPT**：需要OpenAI API密钥
- **智谱AI**：需要智谱AI API密钥

#### 图像生成服务
- **OpenAI DALL-E**：高质量图像生成
- **Pollinations**：免费图像生成服务

#### 语音合成服务
- **OpenAI TTS**：高质量语音合成
- **Edge TTS**：免费语音合成服务

#### 视频生成服务
- **CogVideoX**：专业视频生成
- **本地合成**：使用本地工具合成

### 配置步骤
1. 进入"设置" → "AI服务配置"
2. 选择要配置的服务类型
3. 输入相应的API密钥
4. 测试连接
5. 保存配置

## 常见问题

### Q: 程序启动失败怎么办？
A: 
1. 检查系统是否满足最低要求
2. 确保网络连接正常
3. 尝试以管理员身份运行
4. 查看日志文件获取详细错误信息

### Q: AI服务连接失败？
A:
1. 检查API密钥是否正确
2. 确认网络连接正常
3. 检查服务商是否有使用限制
4. 尝试切换到其他服务提供商

### Q: 生成的内容质量不满意？
A:
1. 优化输入文本的描述
2. 调整生成参数
3. 尝试不同的AI服务
4. 使用手动编辑功能调整

### Q: 视频导出失败？
A:
1. 检查磁盘空间是否充足
2. 确认所有内容都已生成
3. 尝试降低导出质量
4. 检查文件权限

### Q: 程序运行缓慢？
A:
1. 关闭不必要的后台程序
2. 增加系统内存
3. 使用SSD存储
4. 调整生成质量设置

## 技术支持

### 获取帮助
- **在线文档**：访问项目官网查看最新文档
- **社区论坛**：加入用户社区讨论问题
- **问题反馈**：通过GitHub Issues报告问题
- **邮件支持**：发送邮件到*******************

### 日志文件位置
- Windows: `%APPDATA%\AI视频生成器\logs\`
- macOS: `~/Library/Application Support/AI视频生成器/logs/`
- Linux: `~/.local/share/AI视频生成器/logs/`

### 配置文件位置
- Windows: `%APPDATA%\AI视频生成器\config\`
- macOS: `~/Library/Application Support/AI视频生成器/config/`
- Linux: `~/.local/share/AI视频生成器/config/`

## 版本历史

### v2.0.0 (当前版本)
- 全新的用户界面设计
- 支持多种AI服务
- 增强的分镜生成功能
- 改进的性能和稳定性
- 新增主题切换功能

### 更新说明
程序会自动检查更新，也可以手动检查：
1. 点击"帮助"菜单
2. 选择"检查更新"
3. 按照提示下载安装

---

**感谢使用AI视频生成器 2.0！**

如有任何问题或建议，欢迎联系我们。
