# 基础配置文件
app:
  name: "AI视频生成器"
  version: "2.0.0"
  debug: false
  log_level: "INFO"

# 数据库配置
database:
  url: "sqlite:///data/app.db"
  echo: false
  pool_size: 5
  max_overflow: 10

# 缓存配置
cache:
  type: "memory"  # memory, redis
  max_size: 1000
  ttl: 3600

# AI服务配置
services:
  llm:
    default_provider: "openai"
    timeout: 30
    max_retries: 3
    
  image:
    default_provider: "pollinations"
    timeout: 60
    max_retries: 3
    
  video:
    default_provider: "cogvideox"
    timeout: 120
    max_retries: 2
    
  voice:
    default_provider: "edge-tts"
    timeout: 30
    max_retries: 3

# UI配置
ui:
  theme: "light"
  language: "zh-CN"
  window:
    width: 1600
    height: 1000
    min_width: 1200
    min_height: 800

# 项目配置
project:
  default_style: "cinematic"
  default_language: "zh-CN"
  auto_save: true
  auto_save_interval: 300  # 秒

# 性能配置
performance:
  max_concurrent_tasks: 5
  image_cache_size: 100
  video_cache_size: 10
