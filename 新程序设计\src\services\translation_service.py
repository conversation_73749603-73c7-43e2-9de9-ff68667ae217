# -*- coding: utf-8 -*-
"""
翻译服务实现
集成百度翻译API和其他翻译服务
"""

import asyncio
import hashlib
import json
import logging
import random
import time
from typing import Dict, Any, List, Optional
import aiohttp
from dataclasses import dataclass

from ..core.mcp_service_manager import (
    MCPServiceInterface, ServiceRequest, ServiceResponse, ServiceType, ServiceStatus
)

logger = logging.getLogger(__name__)

@dataclass
class TranslationRequest:
    """翻译请求数据结构"""
    text: str
    source_lang: str = 'auto'
    target_lang: str = 'en'
    domain: str = 'general'  # 翻译领域：general, tech, medical等

@dataclass
class TranslationResponse:
    """翻译响应数据结构"""
    translated_text: str
    source_lang: str
    target_lang: str
    confidence: float = 1.0
    service_used: str = ""

class BaseTranslationClient(MCPServiceInterface):
    """翻译客户端基类"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.session = None
        self.supported_languages = self._get_supported_languages()
    
    def _get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表（子类实现）"""
        return {
            'zh': 'Chinese',
            'en': 'English',
            'auto': 'Auto Detect'
        }
    
    async def initialize(self) -> bool:
        """初始化翻译客户端"""
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            health_ok = await self.health_check()
            if health_ok:
                logger.info(f"{self.name}: 翻译服务初始化成功")
                return True
            else:
                logger.warning(f"{self.name}: 翻译服务健康检查失败")
                return False
                
        except Exception as e:
            logger.error(f"{self.name}: 翻译服务初始化失败 - {e}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        if self.session:
            await self.session.close()
    
    async def process(self, request: ServiceRequest) -> ServiceResponse:
        """处理翻译请求"""
        start_time = time.time()
        
        try:
            action = request.parameters.get('action', 'translate')
            
            if action == 'translate':
                translation_request = TranslationRequest(**request.parameters.get('translation_request', {}))
                result = await self.translate(translation_request)
                
                return ServiceResponse(
                    success=True,
                    data=result,
                    execution_time=time.time() - start_time,
                    service_name=self.name
                )
            
            elif action == 'detect_language':
                text = request.parameters.get('text', '')
                result = await self.detect_language(text)
                
                return ServiceResponse(
                    success=True,
                    data=result,
                    execution_time=time.time() - start_time,
                    service_name=self.name
                )
            
            elif action == 'batch_translate':
                texts = request.parameters.get('texts', [])
                source_lang = request.parameters.get('source_lang', 'auto')
                target_lang = request.parameters.get('target_lang', 'en')
                result = await self.batch_translate(texts, source_lang, target_lang)
                
                return ServiceResponse(
                    success=True,
                    data=result,
                    execution_time=time.time() - start_time,
                    service_name=self.name
                )
            
            else:
                return ServiceResponse(
                    success=False,
                    error=f"不支持的操作: {action}",
                    service_name=self.name
                )
                
        except Exception as e:
            return ServiceResponse(
                success=False,
                error=str(e),
                execution_time=time.time() - start_time,
                service_name=self.name
            )
    
    async def translate(self, request: TranslationRequest) -> TranslationResponse:
        """翻译文本（子类实现）"""
        raise NotImplementedError
    
    async def detect_language(self, text: str) -> str:
        """检测语言（子类实现）"""
        raise NotImplementedError
    
    async def batch_translate(self, texts: List[str], source_lang: str, target_lang: str) -> List[TranslationResponse]:
        """批量翻译"""
        results = []
        for text in texts:
            request = TranslationRequest(
                text=text,
                source_lang=source_lang,
                target_lang=target_lang
            )
            result = await self.translate(request)
            results.append(result)
            # 添加延迟避免API限制
            await asyncio.sleep(0.1)
        return results

class BaiduTranslationClient(BaseTranslationClient):
    """百度翻译客户端"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.app_id = config.get('app_id', '')
        self.secret_key = config.get('secret_key', '')
        self.api_url = config.get('api_url', 'https://fanyi-api.baidu.com/api/trans/vip/translate')
    
    def _get_supported_languages(self) -> Dict[str, str]:
        """百度翻译支持的语言"""
        return {
            'auto': '自动检测',
            'zh': '中文',
            'en': '英语',
            'yue': '粤语',
            'wyw': '文言文',
            'jp': '日语',
            'kor': '韩语',
            'spa': '西班牙语',
            'fra': '法语',
            'th': '泰语',
            'ara': '阿拉伯语',
            'ru': '俄语',
            'pt': '葡萄牙语',
            'de': '德语',
            'it': '意大利语',
            'el': '希腊语',
            'nl': '荷兰语',
            'pl': '波兰语',
            'bul': '保加利亚语',
            'est': '爱沙尼亚语',
            'dan': '丹麦语',
            'fin': '芬兰语',
            'cs': '捷克语',
            'rom': '罗马尼亚语',
            'slo': '斯洛文尼亚语',
            'swe': '瑞典语',
            'hu': '匈牙利语',
            'vie': '越南语'
        }
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.app_id or not self.secret_key:
                return False
            
            # 发送简单的测试翻译请求
            test_request = TranslationRequest(
                text="hello",
                source_lang="en",
                target_lang="zh"
            )
            response = await self.translate(test_request)
            return len(response.translated_text) > 0
        except:
            return False
    
    def _generate_sign(self, query: str, salt: str) -> str:
        """生成百度翻译API签名"""
        sign_str = f"{self.app_id}{query}{salt}{self.secret_key}"
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest()
    
    async def translate(self, request: TranslationRequest) -> TranslationResponse:
        """翻译文本"""
        if not self.app_id or not self.secret_key:
            raise Exception("百度翻译API配置不完整")
        
        # 生成随机盐值
        salt = str(random.randint(32768, 65536))
        
        # 生成签名
        sign = self._generate_sign(request.text, salt)
        
        # 构建请求参数
        params = {
            'q': request.text,
            'from': request.source_lang,
            'to': request.target_lang,
            'appid': self.app_id,
            'salt': salt,
            'sign': sign
        }
        
        async with self.session.get(self.api_url, params=params) as response:
            if response.status == 200:
                result = await response.json()
                
                # 检查错误
                if 'error_code' in result:
                    error_code = result['error_code']
                    error_msg = self._get_error_message(error_code)
                    raise Exception(f"百度翻译API错误 {error_code}: {error_msg}")
                
                # 提取翻译结果
                trans_result = result.get('trans_result', [])
                if trans_result:
                    translated_text = trans_result[0]['dst']
                    detected_lang = result.get('from', request.source_lang)
                    
                    return TranslationResponse(
                        translated_text=translated_text,
                        source_lang=detected_lang,
                        target_lang=request.target_lang,
                        service_used=self.name
                    )
                else:
                    raise Exception("百度翻译返回空结果")
            else:
                error_text = await response.text()
                raise Exception(f"百度翻译API请求失败 {response.status}: {error_text}")
    
    async def detect_language(self, text: str) -> str:
        """检测语言"""
        # 百度翻译通过翻译请求自动检测语言
        request = TranslationRequest(
            text=text[:100],  # 只取前100个字符检测
            source_lang='auto',
            target_lang='en'
        )
        response = await self.translate(request)
        return response.source_lang
    
    def _get_error_message(self, error_code: str) -> str:
        """获取错误信息"""
        error_messages = {
            '52001': 'APP ID无效',
            '52002': '签名错误',
            '52003': '访问频率受限',
            '52004': '账户余额不足',
            '52005': '长query请求频繁',
            '54000': '必填参数为空',
            '54001': '签名错误',
            '54003': '访问频率受限',
            '54004': '账户余额不足',
            '54005': '长query请求频繁',
            '58000': '客户端IP非法',
            '58001': '译文语言方向不支持',
            '58002': '服务当前已关闭',
            '90107': '认证未通过或未生效'
        }
        return error_messages.get(error_code, f'未知错误: {error_code}')

class GoogleTranslationClient(BaseTranslationClient):
    """Google翻译客户端（使用Gemini API）"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.api_key = config.get('api_key', '')
        self.base_url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent'
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.api_key:
                return False
            
            test_request = TranslationRequest(
                text="hello",
                source_lang="en",
                target_lang="zh"
            )
            response = await self.translate(test_request)
            return len(response.translated_text) > 0
        except:
            return False
    
    async def translate(self, request: TranslationRequest) -> TranslationResponse:
        """使用Gemini进行翻译"""
        if not self.api_key:
            raise Exception("Google API密钥未配置")
        
        # 构建翻译提示词
        if request.target_lang == 'zh':
            prompt = f"请将以下文本翻译成中文，只返回翻译结果，不要添加任何解释：\n\n{request.text}"
        elif request.target_lang == 'en':
            prompt = f"Please translate the following text to English, return only the translation result without any explanation:\n\n{request.text}"
        else:
            prompt = f"Please translate the following text to {request.target_lang}, return only the translation result without any explanation:\n\n{request.text}"
        
        url = f"{self.base_url}?key={self.api_key}"
        
        data = {
            'contents': [{
                'parts': [{'text': prompt}]
            }],
            'generationConfig': {
                'maxOutputTokens': 1000,
                'temperature': 0.1
            }
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        async with self.session.post(url, headers=headers, json=data) as response:
            if response.status == 200:
                result = await response.json()
                translated_text = result['candidates'][0]['content']['parts'][0]['text'].strip()
                
                return TranslationResponse(
                    translated_text=translated_text,
                    source_lang=request.source_lang,
                    target_lang=request.target_lang,
                    service_used=self.name
                )
            else:
                error_text = await response.text()
                raise Exception(f"Google翻译API错误 {response.status}: {error_text}")
    
    async def detect_language(self, text: str) -> str:
        """检测语言"""
        prompt = f"Please detect the language of the following text and return only the language code (like 'zh', 'en', 'ja', etc.):\n\n{text[:200]}"
        
        url = f"{self.base_url}?key={self.api_key}"
        
        data = {
            'contents': [{
                'parts': [{'text': prompt}]
            }],
            'generationConfig': {
                'maxOutputTokens': 10,
                'temperature': 0.1
            }
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        async with self.session.post(url, headers=headers, json=data) as response:
            if response.status == 200:
                result = await response.json()
                detected_lang = result['candidates'][0]['content']['parts'][0]['text'].strip().lower()
                return detected_lang
            else:
                return 'auto'

class TranslationServiceManager:
    """翻译服务管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.clients = {}
        self._initialize_clients()
    
    def _initialize_clients(self):
        """初始化翻译客户端"""
        # 百度翻译
        baidu_config = self.config.get('BAIDU_TRANSLATE_CONFIG', {})
        if baidu_config.get('app_id') and baidu_config.get('secret_key'):
            self.clients['baidu'] = BaiduTranslationClient('百度翻译', baidu_config)
        
        # Google翻译（使用Gemini API）
        # 这里可以从LLM配置中获取Google API密钥
        google_config = {
            'api_key': ''  # 需要从其他配置获取
        }
        # self.clients['google'] = GoogleTranslationClient('Google翻译', google_config)
    
    def get_client(self, name: str) -> Optional[BaseTranslationClient]:
        """获取翻译客户端"""
        return self.clients.get(name)
    
    def get_all_clients(self) -> List[BaseTranslationClient]:
        """获取所有翻译客户端"""
        return list(self.clients.values())
    
    def get_best_client(self, source_lang: str, target_lang: str) -> Optional[BaseTranslationClient]:
        """获取最佳翻译客户端"""
        # 优先使用百度翻译（中英互译效果好）
        if 'baidu' in self.clients:
            baidu_client = self.clients['baidu']
            if (source_lang in baidu_client.supported_languages and 
                target_lang in baidu_client.supported_languages):
                return baidu_client
        
        # 备选其他客户端
        for client in self.clients.values():
            if (source_lang in client.supported_languages and 
                target_lang in client.supported_languages):
                return client
        
        return None
    
    async def initialize_all(self):
        """初始化所有客户端"""
        for client in self.clients.values():
            await client.initialize()
    
    async def cleanup_all(self):
        """清理所有客户端"""
        for client in self.clients.values():
            await client.cleanup()
    
    async def translate_text(self, text: str, source_lang: str = 'auto', target_lang: str = 'en') -> Optional[TranslationResponse]:
        """翻译文本（便捷方法）"""
        client = self.get_best_client(source_lang, target_lang)
        if not client:
            raise Exception(f"没有支持 {source_lang} -> {target_lang} 的翻译服务")
        
        request = TranslationRequest(
            text=text,
            source_lang=source_lang,
            target_lang=target_lang
        )
        
        return await client.translate(request)
    
    async def translate_article_to_multilang(self, article: str, target_languages: List[str] = ['en']) -> Dict[str, str]:
        """将文章翻译成多种语言"""
        results = {'zh': article}  # 原文
        
        for lang in target_languages:
            if lang != 'zh':
                try:
                    response = await self.translate_text(article, 'zh', lang)
                    if response:
                        results[lang] = response.translated_text
                    else:
                        logger.warning(f"翻译到 {lang} 失败")
                        results[lang] = article  # 使用原文作为备选
                except Exception as e:
                    logger.error(f"翻译到 {lang} 时出错: {e}")
                    results[lang] = article  # 使用原文作为备选
        
        return results

# 便捷函数
def create_translation_service_manager(config: Dict[str, Any]) -> TranslationServiceManager:
    """创建翻译服务管理器"""
    return TranslationServiceManager(config)

if __name__ == "__main__":
    # 测试代码
    async def test_translation_service():
        # 模拟配置
        test_config = {
            'BAIDU_TRANSLATE_CONFIG': {
                'app_id': '20240529002064529',
                'secret_key': 'fpPftxwOvbIGAWwmkucK',
                'api_url': 'https://fanyi-api.baidu.com/api/trans/vip/translate'
            }
        }
        
        manager = create_translation_service_manager(test_config)
        await manager.initialize_all()
        
        # 测试翻译
        try:
            response = await manager.translate_text(
                "你好，世界！这是一个测试。",
                source_lang='zh',
                target_lang='en'
            )
            if response:
                print(f"翻译结果: {response.translated_text}")
                print(f"使用服务: {response.service_used}")
        except Exception as e:
            print(f"翻译失败: {e}")
        
        await manager.cleanup_all()
    
    # asyncio.run(test_translation_service())
    print("翻译服务模块已加载")