#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器启动脚本
提供更友好的启动体验和错误处理
"""

import sys
import os
import subprocess

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True

def check_dependencies():
    """检查依赖包"""
    try:
        import PyQt5
        print("✅ PyQt5 已安装")
    except ImportError:
        print("❌ PyQt5 未安装，请运行: pip install PyQt5")
        return False
    
    try:
        import requests
        print("✅ requests 已安装")
    except ImportError:
        print("❌ requests 未安装，请运行: pip install requests")
        return False
    
    return True

def install_dependencies():
    """安装依赖"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        return False

def main():
    """主函数"""
    print("🎬 AI视频生成器启动器")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return
    
    # 检查依赖
    if not check_dependencies():
        choice = input("是否自动安装依赖? (y/n): ")
        if choice.lower() == 'y':
            if not install_dependencies():
                input("按回车键退出...")
                return
        else:
            print("请手动安装依赖: pip install -r requirements.txt")
            input("按回车键退出...")
            return
    
    # 启动主程序
    print("🚀 启动主程序...")
    try:
        import main
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
