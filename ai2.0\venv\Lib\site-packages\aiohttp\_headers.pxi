# The file is autogenerated from aiohttp/hdrs.py
# Run ./tools/gen.py to update it after the origin changing.

from . import hdrs
cdef tuple headers = (
    hdrs.ACCEPT,
    hdrs.ACCEPT_CHARSET,
    hdrs.ACCEPT_ENCODING,
    hdrs.ACCEPT_LANGUAGE,
    hdrs.ACCEPT_RANGES,
    hdrs.ACCESS_CONTROL_ALLOW_CREDENTIALS,
    hdrs.ACCESS_CONTROL_ALLOW_HEADERS,
    hdrs.ACCESS_CONTROL_ALLOW_METHODS,
    hdrs.ACCESS_CONTROL_ALLOW_ORIGIN,
    hdrs.ACCESS_CONTROL_EXPOSE_HEADERS,
    hdrs.ACCESS_CONTROL_MAX_AGE,
    hdrs.ACCESS_CONTROL_REQUEST_HEADERS,
    hdrs.ACCESS_CONTROL_REQUEST_METHOD,
    hdrs.AGE,
    hdrs.ALLOW,
    hdrs.AUTHORIZATION,
    hdrs.CACHE_CONTROL,
    hdrs.CONNECTION,
    hdrs.CONTENT_DISPOSITION,
    hdrs.CONTENT_ENCODING,
    hdrs.CONTENT_LANGUAGE,
    hdrs.CONTENT_LENGTH,
    hdrs.CONTENT_LOCATION,
    hdrs.CONTENT_MD5,
    hdrs.CONTENT_RANGE,
    hdrs.CONTENT_TRANSFER_ENCODING,
    hdrs.CONTENT_TYPE,
    hdrs.COOKIE,
    hdrs.DATE,
    hdrs.DESTINATION,
    hdrs.DIGEST,
    hdrs.ETAG,
    hdrs.EXPECT,
    hdrs.EXPIRES,
    hdrs.FORWARDED,
    hdrs.FROM,
    hdrs.HOST,
    hdrs.IF_MATCH,
    hdrs.IF_MODIFIED_SINCE,
    hdrs.IF_NONE_MATCH,
    hdrs.IF_RANGE,
    hdrs.IF_UNMODIFIED_SINCE,
    hdrs.KEEP_ALIVE,
    hdrs.LAST_EVENT_ID,
    hdrs.LAST_MODIFIED,
    hdrs.LINK,
    hdrs.LOCATION,
    hdrs.MAX_FORWARDS,
    hdrs.ORIGIN,
    hdrs.PRAGMA,
    hdrs.PROXY_AUTHENTICATE,
    hdrs.PROXY_AUTHORIZATION,
    hdrs.RANGE,
    hdrs.REFERER,
    hdrs.RETRY_AFTER,
    hdrs.SEC_WEBSOCKET_ACCEPT,
    hdrs.SEC_WEBSOCKET_EXTENSIONS,
    hdrs.SEC_WEBSOCKET_KEY,
    hdrs.SEC_WEBSOCKET_KEY1,
    hdrs.SEC_WEBSOCKET_PROTOCOL,
    hdrs.SEC_WEBSOCKET_VERSION,
    hdrs.SERVER,
    hdrs.SET_COOKIE,
    hdrs.TE,
    hdrs.TRAILER,
    hdrs.TRANSFER_ENCODING,
    hdrs.URI,
    hdrs.UPGRADE,
    hdrs.USER_AGENT,
    hdrs.VARY,
    hdrs.VIA,
    hdrs.WWW_AUTHENTICATE,
    hdrs.WANT_DIGEST,
    hdrs.WARNING,
    hdrs.X_FORWARDED_FOR,
    hdrs.X_FORWARDED_HOST,
    hdrs.X_FORWARDED_PROTO,
)
