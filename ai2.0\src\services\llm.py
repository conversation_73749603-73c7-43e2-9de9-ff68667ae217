"""大语言模型服务

实现各种LLM服务的接口。
"""

from typing import Dict, Any, List, Optional
import json
import aiohttp
from datetime import datetime

from src.services.base import LLMService, ServiceConfig, ServiceResponse
from src.core.exceptions import NetworkError, APIKeyInvalidError, RateLimitExceededError


class OpenAILLMService(LLMService):
    """OpenAI LLM服务"""
    
    def __init__(self, config: ServiceConfig):
        super().__init__(config)
        self.base_url = config.base_url or "https://api.openai.com/v1"
        self.model = config.custom_settings.get("model", "gpt-3.5-turbo") if config.custom_settings else "gpt-3.5-turbo"
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.config.api_key}",
                    "Content-Type": "application/json"
                }
                
                async with session.get(
                    f"{self.base_url}/models",
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        self._set_status(ServiceStatus.AVAILABLE)
                        return True
                    elif response.status == 401:
                        self._set_status(ServiceStatus.ERROR, "Invalid API key")
                        return False
                    else:
                        self._set_status(ServiceStatus.UNAVAILABLE, f"HTTP {response.status}")
                        return False
        except Exception as e:
            self._set_status(ServiceStatus.ERROR, str(e))
            return False
    
    async def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            "provider": "openai",
            "model": self.model,
            "base_url": self.base_url,
            "status": self.status.value
        }
    
    async def generate(self, prompt: str, **kwargs) -> ServiceResponse[str]:
        """生成内容"""
        return await self.text_completion(prompt, **kwargs)
    
    async def validate_prompt(self, prompt: str) -> bool:
        """验证提示词"""
        if not prompt or not prompt.strip():
            return False
        if len(prompt) > 100000:  # OpenAI的大致限制
            return False
        return True
    
    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> ServiceResponse[str]:
        """聊天完成"""
        try:
            if not self.config.api_key:
                return ServiceResponse.error_response("API key not configured")
            
            payload = {
                "model": kwargs.get("model", self.model),
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", 2000),
                "temperature": kwargs.get("temperature", 0.7),
                "top_p": kwargs.get("top_p", 1.0),
                "frequency_penalty": kwargs.get("frequency_penalty", 0.0),
                "presence_penalty": kwargs.get("presence_penalty", 0.0)
            }
            
            headers = {
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                ) as response:
                    response_data = await response.json()
                    
                    if response.status == 200:
                        content = response_data["choices"][0]["message"]["content"]
                        metadata = {
                            "model": response_data.get("model"),
                            "usage": response_data.get("usage"),
                            "finish_reason": response_data["choices"][0].get("finish_reason")
                        }
                        return ServiceResponse.success_response(content, metadata)
                    
                    elif response.status == 401:
                        raise APIKeyInvalidError("Invalid OpenAI API key", provider="openai")
                    
                    elif response.status == 429:
                        retry_after = response.headers.get("Retry-After")
                        raise RateLimitExceededError(
                            "OpenAI rate limit exceeded", 
                            retry_after=int(retry_after) if retry_after else None
                        )
                    
                    else:
                        error_msg = response_data.get("error", {}).get("message", f"HTTP {response.status}")
                        return ServiceResponse.error_response(error_msg, str(response.status))
        
        except (APIKeyInvalidError, RateLimitExceededError):
            raise
        except Exception as e:
            self.logger.error(f"OpenAI chat completion failed: {e}")
            return ServiceResponse.error_response(str(e))
    
    async def text_completion(
        self, 
        prompt: str, 
        **kwargs
    ) -> ServiceResponse[str]:
        """文本完成"""
        # 将文本完成转换为聊天完成
        messages = [{"role": "user", "content": prompt}]
        return await self.chat_completion(messages, **kwargs)
    
    async def analyze_text(
        self, 
        text: str, 
        analysis_type: str = "sentiment"
    ) -> ServiceResponse[Dict[str, Any]]:
        """文本分析"""
        analysis_prompts = {
            "sentiment": f"请分析以下文本的情感倾向，返回JSON格式：{{'sentiment': '正面/负面/中性', 'confidence': 0.0-1.0, 'keywords': []}}。文本：{text}",
            "summary": f"请总结以下文本的主要内容，返回JSON格式：{{'summary': '摘要内容', 'key_points': []}}。文本：{text}",
            "keywords": f"请提取以下文本的关键词，返回JSON格式：{{'keywords': [], 'entities': []}}。文本：{text}",
            "structure": f"请分析以下文本的结构，返回JSON格式：{{'structure': '结构类型', 'sections': []}}。文本：{text}"
        }
        
        if analysis_type not in analysis_prompts:
            return ServiceResponse.error_response(f"Unsupported analysis type: {analysis_type}")
        
        prompt = analysis_prompts[analysis_type]
        response = await self.text_completion(prompt, temperature=0.3)
        
        if response.success:
            try:
                # 尝试解析JSON响应
                result = json.loads(response.data)
                return ServiceResponse.success_response(result)
            except json.JSONDecodeError:
                # 如果不是JSON，返回原始文本
                return ServiceResponse.success_response({"raw_response": response.data})
        
        return response


class ZhipuLLMService(LLMService):
    """智谱AI LLM服务"""
    
    def __init__(self, config: ServiceConfig):
        super().__init__(config)
        self.base_url = config.base_url or "https://open.bigmodel.cn/api/paas/v4"
        self.model = config.custom_settings.get("model", "glm-4") if config.custom_settings else "glm-4"
    
    async def health_check(self) -> bool:
        """健康检查"""
        # 智谱AI的健康检查实现
        try:
            # 发送一个简单的请求来检查服务状态
            response = await self.text_completion("测试", max_tokens=10)
            return response.success
        except Exception as e:
            self._set_status(ServiceStatus.ERROR, str(e))
            return False
    
    async def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            "provider": "zhipu",
            "model": self.model,
            "base_url": self.base_url,
            "status": self.status.value
        }
    
    async def generate(self, prompt: str, **kwargs) -> ServiceResponse[str]:
        """生成内容"""
        return await self.text_completion(prompt, **kwargs)
    
    async def validate_prompt(self, prompt: str) -> bool:
        """验证提示词"""
        if not prompt or not prompt.strip():
            return False
        if len(prompt) > 50000:  # 智谱AI的大致限制
            return False
        return True
    
    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> ServiceResponse[str]:
        """聊天完成"""
        try:
            if not self.config.api_key:
                return ServiceResponse.error_response("API key not configured")
            
            payload = {
                "model": kwargs.get("model", self.model),
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", 2000),
                "temperature": kwargs.get("temperature", 0.7),
                "top_p": kwargs.get("top_p", 1.0)
            }
            
            headers = {
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                ) as response:
                    response_data = await response.json()
                    
                    if response.status == 200:
                        content = response_data["choices"][0]["message"]["content"]
                        metadata = {
                            "model": response_data.get("model"),
                            "usage": response_data.get("usage")
                        }
                        return ServiceResponse.success_response(content, metadata)
                    else:
                        error_msg = response_data.get("error", {}).get("message", f"HTTP {response.status}")
                        return ServiceResponse.error_response(error_msg, str(response.status))
        
        except Exception as e:
            self.logger.error(f"Zhipu chat completion failed: {e}")
            return ServiceResponse.error_response(str(e))
    
    async def text_completion(
        self, 
        prompt: str, 
        **kwargs
    ) -> ServiceResponse[str]:
        """文本完成"""
        messages = [{"role": "user", "content": prompt}]
        return await self.chat_completion(messages, **kwargs)
    
    async def analyze_text(
        self, 
        text: str, 
        analysis_type: str = "sentiment"
    ) -> ServiceResponse[Dict[str, Any]]:
        """文本分析"""
        # 类似OpenAI的实现，但可能需要调整提示词格式
        analysis_prompts = {
            "sentiment": f"分析文本情感，返回JSON：{text}",
            "summary": f"总结文本内容，返回JSON：{text}",
            "keywords": f"提取关键词，返回JSON：{text}",
            "structure": f"分析文本结构，返回JSON：{text}"
        }
        
        if analysis_type not in analysis_prompts:
            return ServiceResponse.error_response(f"Unsupported analysis type: {analysis_type}")
        
        prompt = analysis_prompts[analysis_type]
        response = await self.text_completion(prompt, temperature=0.3)
        
        if response.success:
            try:
                result = json.loads(response.data)
                return ServiceResponse.success_response(result)
            except json.JSONDecodeError:
                return ServiceResponse.success_response({"raw_response": response.data})
        
        return response
