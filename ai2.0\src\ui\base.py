"""基础UI组件

定义所有UI组件的基类和通用功能。
"""

from typing import Optional, Dict, Any, Callable
from abc import ABC, abstractmethod

from PyQt6.QtWidgets import (
    QWidget, QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QScrollArea, QSizePolicy
)
from PyQt6.QtCore import QObject, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QRect, Qt
from PyQt6.QtGui import QFont, QPalette, QColor

from src.utils.logger import get_logger


class BaseWidget(QWidget):
    """基础Widget类"""
    
    # 信号定义
    theme_changed = pyqtSignal(str)  # 主题变化信号
    size_changed = pyqtSignal()      # 尺寸变化信号
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.logger = get_logger(self.__class__.__name__)
        self._theme_name = "light"
        self._animations = {}
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self) -> None:
        """设置UI - 子类重写此方法"""
        pass
    
    def _connect_signals(self) -> None:
        """连接信号 - 子类重写此方法"""
        pass
    
    def set_theme(self, theme_name: str) -> None:
        """设置主题"""
        if self._theme_name != theme_name:
            self._theme_name = theme_name
            self._apply_theme()
            self.theme_changed.emit(theme_name)
    
    def get_theme(self) -> str:
        """获取当前主题"""
        return self._theme_name
    
    def _apply_theme(self) -> None:
        """应用主题 - 子类重写此方法"""
        pass
    
    def add_animation(self, name: str, animation: QPropertyAnimation) -> None:
        """添加动画"""
        self._animations[name] = animation
    
    def play_animation(self, name: str) -> None:
        """播放动画"""
        if name in self._animations:
            self._animations[name].start()
    
    def stop_animation(self, name: str) -> None:
        """停止动画"""
        if name in self._animations:
            self._animations[name].stop()
    
    def create_fade_animation(self, duration: int = 300) -> QPropertyAnimation:
        """创建淡入淡出动画"""
        animation = QPropertyAnimation(self, b"windowOpacity")
        animation.setDuration(duration)
        animation.setStartValue(0.0)
        animation.setEndValue(1.0)
        animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
        return animation
    
    def create_slide_animation(self, start_pos: QRect, end_pos: QRect, duration: int = 300) -> QPropertyAnimation:
        """创建滑动动画"""
        animation = QPropertyAnimation(self, b"geometry")
        animation.setDuration(duration)
        animation.setStartValue(start_pos)
        animation.setEndValue(end_pos)
        animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
        return animation
    
    def set_loading_state(self, loading: bool) -> None:
        """设置加载状态"""
        self.setEnabled(not loading)
        if loading:
            self.setCursor(Qt.CursorShape.WaitCursor)
        else:
            self.setCursor(Qt.CursorShape.ArrowCursor)
    
    def resizeEvent(self, event) -> None:
        """重写尺寸变化事件"""
        super().resizeEvent(event)
        self.size_changed.emit()


class BaseDialog(QDialog):
    """基础对话框类"""
    
    def __init__(self, parent: Optional[QWidget] = None, title: str = ""):
        super().__init__(parent)
        self.logger = get_logger(self.__class__.__name__)
        self._theme_name = "light"
        
        if title:
            self.setWindowTitle(title)
        
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self) -> None:
        """设置UI - 子类重写此方法"""
        self.setModal(True)
        self.resize(400, 300)
    
    def _connect_signals(self) -> None:
        """连接信号 - 子类重写此方法"""
        pass
    
    def set_theme(self, theme_name: str) -> None:
        """设置主题"""
        if self._theme_name != theme_name:
            self._theme_name = theme_name
            self._apply_theme()
    
    def _apply_theme(self) -> None:
        """应用主题 - 子类重写此方法"""
        pass


class ResponsiveWidget(BaseWidget):
    """响应式Widget"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self._breakpoints = {
            'xs': 0,     # 超小屏
            'sm': 576,   # 小屏
            'md': 768,   # 中屏
            'lg': 992,   # 大屏
            'xl': 1200   # 超大屏
        }
        self._current_breakpoint = 'lg'
        self.size_changed.connect(self._update_breakpoint)
    
    def _update_breakpoint(self) -> None:
        """更新断点"""
        width = self.width()
        new_breakpoint = 'xs'
        
        for name, min_width in sorted(self._breakpoints.items(), key=lambda x: x[1], reverse=True):
            if width >= min_width:
                new_breakpoint = name
                break
        
        if new_breakpoint != self._current_breakpoint:
            self._current_breakpoint = new_breakpoint
            self._on_breakpoint_changed(new_breakpoint)
    
    def _on_breakpoint_changed(self, breakpoint: str) -> None:
        """断点变化处理 - 子类重写此方法"""
        pass
    
    def get_current_breakpoint(self) -> str:
        """获取当前断点"""
        return self._current_breakpoint
    
    def is_mobile(self) -> bool:
        """是否为移动端尺寸"""
        return self._current_breakpoint in ['xs', 'sm']
    
    def is_desktop(self) -> bool:
        """是否为桌面端尺寸"""
        return self._current_breakpoint in ['lg', 'xl']


class LoadingWidget(BaseWidget):
    """加载指示器组件"""
    
    def __init__(self, parent: Optional[QWidget] = None, message: str = "加载中..."):
        self.message = message
        self._timer = QTimer()
        self._dots = 0
        self.label = None
        super().__init__(parent)
    
    def _setup_ui(self) -> None:
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 加载文本
        self.label = QLabel(self.message)
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        font = QFont()
        font.setPointSize(12)
        self.label.setFont(font)
        
        layout.addWidget(self.label)
        
        # 设置样式
        self.setStyleSheet("""
            LoadingWidget {
                background-color: rgba(0, 0, 0, 0.7);
                border-radius: 8px;
            }
            QLabel {
                color: white;
                padding: 10px;
            }
        """)
    
    def _connect_signals(self) -> None:
        """连接信号"""
        self._timer.timeout.connect(self._update_dots)
    
    def start_loading(self) -> None:
        """开始加载动画"""
        self._timer.start(500)  # 每500ms更新一次
        self.show()
    
    def stop_loading(self) -> None:
        """停止加载动画"""
        self._timer.stop()
        self.hide()
    
    def _update_dots(self) -> None:
        """更新加载点"""
        self._dots = (self._dots + 1) % 4
        dots = "." * self._dots
        self.label.setText(f"{self.message}{dots}")


class CardWidget(BaseWidget):
    """卡片组件"""
    
    def __init__(self, parent: Optional[QWidget] = None, title: str = "", content: str = ""):
        super().__init__(parent)
        self.title = title
        self.content = content
        self._setup_ui()
    
    def _setup_ui(self) -> None:
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(8)
        
        # 标题
        if self.title:
            title_label = QLabel(self.title)
            title_font = QFont()
            title_font.setPointSize(14)
            title_font.setBold(True)
            title_label.setFont(title_font)
            layout.addWidget(title_label)
        
        # 内容
        if self.content:
            content_label = QLabel(self.content)
            content_label.setWordWrap(True)
            layout.addWidget(content_label)
        
        # 设置样式
        self.setStyleSheet("""
            CardWidget {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
            }
        """)
    
    def set_title(self, title: str) -> None:
        """设置标题"""
        self.title = title
        # 更新UI中的标题
    
    def set_content(self, content: str) -> None:
        """设置内容"""
        self.content = content
        # 更新UI中的内容


class SeparatorWidget(QFrame):
    """分隔线组件"""
    
    def __init__(self, orientation: str = "horizontal", parent: Optional[QWidget] = None):
        super().__init__(parent)
        
        if orientation == "horizontal":
            self.setFrameShape(QFrame.Shape.HLine)
        else:
            self.setFrameShape(QFrame.Shape.VLine)
        
        self.setFrameShadow(QFrame.Shadow.Sunken)
        self.setStyleSheet("""
            QFrame {
                color: #e0e0e0;
                background-color: #e0e0e0;
            }
        """)
