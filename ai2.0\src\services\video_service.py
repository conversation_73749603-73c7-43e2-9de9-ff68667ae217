"""
视频合成服务 - 提供视频合成和处理功能
"""
import asyncio
import logging
import os
import subprocess
import time
import uuid
from typing import List, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class VideoResult:
    """视频生成结果"""
    success: bool
    video_path: str = ""
    duration: float = 0.0
    error_message: str = ""

class VideoService:
    """视频合成服务"""
    
    def __init__(self):
        self.output_dir = Path("output/videos")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 检查FFmpeg是否可用
        self.ffmpeg_available = self._check_ffmpeg()
    
    def _check_ffmpeg(self) -> bool:
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            logger.warning("FFmpeg未找到，视频合成功能将受限")
            return False
    
    async def create_video_from_images_and_audio(self, 
                                               image_paths: List[str], 
                                               audio_paths: List[str],
                                               output_name: str = None) -> VideoResult:
        """从图像和音频创建视频"""
        try:
            if not self.ffmpeg_available:
                return VideoResult(
                    success=False,
                    error_message="FFmpeg不可用，无法合成视频"
                )
            
            if not image_paths:
                return VideoResult(
                    success=False,
                    error_message="没有图像文件"
                )
            
            logger.info(f"开始合成视频: {len(image_paths)}张图像, {len(audio_paths)}段音频")
            
            # 生成输出文件名
            if not output_name:
                timestamp = int(time.time() * 1000)
                output_name = f"video_{timestamp}_{uuid.uuid4().hex[:8]}.mp4"
            
            output_path = self.output_dir / output_name
            
            # 创建视频
            success = await self._create_video_with_ffmpeg(image_paths, audio_paths, output_path)
            
            if success:
                # 获取视频时长
                duration = self._get_video_duration(str(output_path))
                
                logger.info(f"视频合成成功: {output_path}, 时长: {duration:.2f}秒")
                return VideoResult(
                    success=True,
                    video_path=str(output_path),
                    duration=duration
                )
            else:
                return VideoResult(
                    success=False,
                    error_message="视频合成失败"
                )
                
        except Exception as e:
            logger.error(f"视频合成异常: {e}")
            return VideoResult(
                success=False,
                error_message=str(e)
            )
    
    async def _create_video_with_ffmpeg(self, 
                                      image_paths: List[str], 
                                      audio_paths: List[str], 
                                      output_path: Path) -> bool:
        """使用FFmpeg创建视频"""
        try:
            # 如果有音频，先合并音频
            if audio_paths:
                merged_audio = await self._merge_audio_files(audio_paths)
                if not merged_audio:
                    logger.warning("音频合并失败，将创建无声视频")
            else:
                merged_audio = None
            
            # 创建图像列表文件
            image_list_file = self.output_dir / f"images_{uuid.uuid4().hex[:8]}.txt"
            
            # 计算每张图片的显示时长
            if merged_audio:
                audio_duration = self._get_audio_duration(merged_audio)
                duration_per_image = audio_duration / len(image_paths)
            else:
                duration_per_image = 3.0  # 默认每张图片3秒
            
            # 写入图像列表
            with open(image_list_file, 'w', encoding='utf-8') as f:
                for img_path in image_paths:
                    f.write(f"file '{os.path.abspath(img_path)}'\n")
                    f.write(f"duration {duration_per_image}\n")
                # 最后一张图片需要额外的duration行
                f.write(f"file '{os.path.abspath(image_paths[-1])}'\n")
            
            # 构建FFmpeg命令
            cmd = [
                'ffmpeg', '-y',  # 覆盖输出文件
                '-f', 'concat',
                '-safe', '0',
                '-i', str(image_list_file),
                '-vf', 'scale=1280:720:force_original_aspect_ratio=decrease,pad=1280:720:(ow-iw)/2:(oh-ih)/2',
                '-r', '25',  # 帧率
                '-pix_fmt', 'yuv420p'
            ]
            
            # 如果有音频，添加音频参数
            if merged_audio:
                cmd.extend(['-i', merged_audio, '-c:a', 'aac', '-shortest'])
            
            cmd.append(str(output_path))
            
            # 执行FFmpeg命令
            logger.info(f"执行FFmpeg命令: {' '.join(cmd)}")
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            # 清理临时文件
            try:
                os.remove(image_list_file)
                if merged_audio and merged_audio != audio_paths[0]:
                    os.remove(merged_audio)
            except:
                pass
            
            if process.returncode == 0:
                logger.info("FFmpeg执行成功")
                return True
            else:
                logger.error(f"FFmpeg执行失败: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"FFmpeg视频创建失败: {e}")
            return False
    
    async def _merge_audio_files(self, audio_paths: List[str]) -> Optional[str]:
        """合并音频文件"""
        if len(audio_paths) == 1:
            return audio_paths[0]
        
        try:
            # 创建音频列表文件
            audio_list_file = self.output_dir / f"audio_{uuid.uuid4().hex[:8]}.txt"
            merged_audio_path = self.output_dir / f"merged_audio_{uuid.uuid4().hex[:8]}.wav"
            
            with open(audio_list_file, 'w', encoding='utf-8') as f:
                for audio_path in audio_paths:
                    f.write(f"file '{os.path.abspath(audio_path)}'\n")
            
            # 合并音频
            cmd = [
                'ffmpeg', '-y',
                '-f', 'concat',
                '-safe', '0',
                '-i', str(audio_list_file),
                '-c', 'copy',
                str(merged_audio_path)
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            await process.communicate()
            
            # 清理临时文件
            try:
                os.remove(audio_list_file)
            except:
                pass
            
            if process.returncode == 0:
                return str(merged_audio_path)
            else:
                return None
                
        except Exception as e:
            logger.error(f"音频合并失败: {e}")
            return None
    
    def _get_video_duration(self, video_path: str) -> float:
        """获取视频时长"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet',
                '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1',
                video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                return float(result.stdout.strip())
            else:
                return 0.0
                
        except Exception:
            return 0.0
    
    def _get_audio_duration(self, audio_path: str) -> float:
        """获取音频时长"""
        return self._get_video_duration(audio_path)  # FFprobe也能处理音频
    
    def get_generated_videos(self) -> List[str]:
        """获取已生成的视频列表"""
        if not self.output_dir.exists():
            return []
        
        video_files = list(self.output_dir.glob('*.mp4'))
        
        # 按修改时间排序
        video_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        return [str(f) for f in video_files]
    
    def clear_videos(self):
        """清理生成的视频"""
        try:
            for video_file in self.get_generated_videos():
                os.remove(video_file)
            logger.info("已清理所有生成的视频")
        except Exception as e:
            logger.error(f"清理视频失败: {e}")

# 全局视频服务实例
video_service = VideoService()
